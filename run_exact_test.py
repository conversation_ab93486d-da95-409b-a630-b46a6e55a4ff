#!/usr/bin/env python3
"""
EXACT TEST AS REQUESTED:
- Keywords: "proxy list", "free proxy", "proxy scrape"
- Min stars: 30  
- Max repos per keyword: 60
- Expected total: ~180 repositories
- Sorted by last updated (fixed)
- Full unified architecture with local queues (Redis alternative)
- Proper logging files
- Merged output to single JSON file
- Supabase storage integration
"""

import os
import sys
import uuid
import json
import logging
import threading
import time
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import Optional

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from github_search import search_repositories
from config import ScraperConfig
from queue_manager import get_queue_manager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord
from pipeline import repomix_worker, llm_worker
import multiprocessing

def setup_logging(output_dir: Path) -> logging.Logger:
    """Set up proper logging files."""
    
    # Create logs directory
    logs_dir = output_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Main log file
    main_log = logs_dir / "main.log"
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(main_log),
            logging.StreamHandler()
        ]
    )
    
    # Detailed debug log
    debug_log = logs_dir / "debug.log"
    debug_handler = logging.FileHandler(debug_log)
    debug_handler.setLevel(logging.DEBUG)
    debug_handler.setFormatter(logging.Formatter(log_format))
    
    # Get root logger and add debug handler
    root_logger = logging.getLogger()
    root_logger.addHandler(debug_handler)
    root_logger.setLevel(logging.DEBUG)
    
    logger = logging.getLogger(__name__)
    logger.info(f"📝 Logging configured - Main: {main_log}, Debug: {debug_log}")
    
    return logger

def run_workers(config, job_id: str, repo_queue_mp, file_queue_mp, num_repomix_workers: int = 3, num_llm_workers: int = 2):
    """Run worker processes to handle the queued repositories."""
    
    logger = logging.getLogger(__name__)
    logger.info(f"🔧 Starting {num_repomix_workers} repomix workers and {num_llm_workers} LLM workers")
    
    # Convert config to dict for multiprocessing
    config_dict = {
        'GITHUB_TOKEN': config.GITHUB_TOKEN,
        'LLM_API_KEY': config.LLM_API_KEY,  # Fixed: Use LLM_API_KEY not GEMINI_API_KEY
        'LLM_BASE_URL': config.LLM_BASE_URL,
        'LLM_MODEL': config.LLM_MODEL,
        'LLM_MAX_OUTPUT_TOKENS': config.LLM_MAX_OUTPUT_TOKENS,
        'LLM_TIMEOUT': config.LLM_TIMEOUT,
        'SUPABASE_URL': config.SUPABASE_URL,
        'SUPABASE_KEY': config.SUPABASE_KEY,
        'REDIS_URL': getattr(config, 'REDIS_URL', 'local://memory')
    }
    
    # Start repomix workers
    repomix_processes = []
    for i in range(num_repomix_workers):
        p = multiprocessing.Process(
            target=repomix_worker,
            args=(f"repomix_worker_{i}", config_dict, job_id)
        )
        p.start()
        repomix_processes.append(p)
        logger.info(f"🔧 Started repomix worker {i}")
    
    # Start LLM workers  
    llm_processes = []
    for i in range(num_llm_workers):
        p = multiprocessing.Process(
            target=llm_worker,
            args=(f"llm_worker_{i}", config_dict, job_id)
        )
        p.start()
        llm_processes.append(p)
        logger.info(f"🤖 Started LLM worker {i}")
    
    return repomix_processes + llm_processes

def wait_for_completion(queue_manager, job_id: str, total_repos: int, timeout: int = 1800):
    """Wait for all repositories to be processed."""
    
    logger = logging.getLogger(__name__)
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        # Check queue status
        repo_queue = queue_manager.get_repository_queue()
        file_queue = queue_manager.get_file_queue()
        
        repo_size = repo_queue.size() if hasattr(repo_queue, 'size') else 0
        file_size = file_queue.size() if hasattr(file_queue, 'size') else 0
        
        # Check Supabase for completed analyses
        try:
            supabase_client = create_supabase_client()
            completed = supabase_client.get_completed_analyses_count(job_id)
            
            logger.info(f"📊 Progress: {completed}/{total_repos} completed, "
                       f"repo_queue: {repo_size}, file_queue: {file_size}")
            
            if completed >= total_repos:
                logger.info("🎉 All repositories processed!")
                return True
                
        except Exception as e:
            logger.error(f"Error checking progress: {e}")
        
        time.sleep(10)  # Check every 10 seconds
    
    logger.warning(f"⏰ Timeout reached after {timeout} seconds")
    return False

def create_final_analysis(job_id: str, output_dir: Path) -> str:
    """Create the final merged analysis JSON file."""
    
    logger = logging.getLogger(__name__)
    logger.info("📋 Creating final merged analysis file...")
    
    try:
        supabase_client = create_supabase_client()
        
        # Get all analyses for this job
        analyses = supabase_client.get_job_analyses(job_id)
        repositories = supabase_client.get_job_repositories(job_id)
        
        # Create comprehensive analysis
        final_analysis = {
            "metadata": {
                "job_id": job_id,
                "generated": datetime.now().isoformat(),
                "keywords": "proxy list,free proxy,proxy scrape",
                "min_stars": 30,
                "max_repos_per_keyword": 60,
                "total_repositories": len(repositories),
                "successful_analyses": len(analyses),
                "processing_mode": "unified_architecture",
                "sorted_by": "last_updated"
            },
            "summary": {
                "repositories_found": len(repositories),
                "analyses_completed": len(analyses),
                "success_rate": len(analyses) / len(repositories) * 100 if repositories else 0,
                "languages": {},
                "average_stars": sum(r.get('stars', 0) for r in repositories) / len(repositories) if repositories else 0,
                "total_analysis_size": sum(len(a.get('analysis_content', '')) for a in analyses)
            },
            "repositories": repositories,
            "analyses": analyses
        }
        
        # Calculate language distribution
        for repo in repositories:
            lang = repo.get('language', 'Unknown')
            final_analysis["summary"]["languages"][lang] = final_analysis["summary"]["languages"].get(lang, 0) + 1
        
        # Save final analysis
        final_file = output_dir / "final_merged_analysis.json"
        with open(final_file, 'w', encoding='utf-8') as f:
            json.dump(final_analysis, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Final analysis saved: {final_file}")
        return str(final_file)
        
    except Exception as e:
        logger.error(f"❌ Failed to create final analysis: {e}")
        return None

def main():
    """Run the EXACT test as specified."""
    
    print("🚀 EXACT REPOSITORY RESEARCH TEST")
    print("=" * 60)
    print("SPECIFICATIONS:")
    print("   Keywords: 'proxy list', 'free proxy', 'proxy scrape'")
    print("   Min stars: 30")
    print("   Max repos per keyword: 60") 
    print("   Expected total: ~180 repositories")
    print("   Sorting: By last updated")
    print("   Architecture: Unified with local queues")
    print("   Output: Merged to single JSON file")
    print("   Storage: Supabase integration")
    print("   Logging: Proper log files")
    print()
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
    output_dir = Path(f"output/exact_test_{timestamp}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set up logging
    logger = setup_logging(output_dir)
    
    try:
        # Load configuration
        config = ScraperConfig()
        logger.info("✅ Configuration loaded")
        
        # Initialize multiprocessing queues for shared communication
        manager = multiprocessing.Manager()
        repo_queue_mp = manager.Queue()
        file_queue_mp = manager.Queue()
        logger.info("✅ Multiprocessing queues initialized for shared communication")
        
        # Search for repositories with EXACT parameters
        logger.info("🔍 Searching GitHub repositories...")
        repositories = search_repositories(
            keywords="proxy list,free proxy,proxy scrape",
            min_stars=30,
            max_repos=60  # This is per keyword
        )
        
        logger.info(f"✅ Found {len(repositories)} repositories")
        
        if not repositories:
            logger.error("❌ No repositories found")
            return False
        
        # Create job in Supabase
        job_id = str(uuid.uuid4())
        logger.info(f"📝 Creating job: {job_id}")
        
        supabase_client = create_supabase_client()
        job_record = JobRecord(
            id=job_id,
            keywords="proxy list,free proxy,proxy scrape",
            min_stars=30,
            max_repos=60,
            status="running",
            created_at=datetime.now().isoformat(),
            custom_prompt=None,
            debug=True
        )
        supabase_client.create_job(job_record)
        
        # Queue all repositories
        logger.info(f"📤 Queuing {len(repositories)} repositories...")
        
        for repo in repositories:
            # Create repository record
            repo_record = RepositoryRecord(
                id=str(uuid.uuid4()),
                job_id=job_id,
                name=repo['full_name'],
                url=repo['html_url'],
                stars=repo['stars'],
                file_size_mb=0.0,
                analysis_status="pending"
            )
            supabase_client.create_repository(repo_record)
            
            # Queue for processing
            queue_item = RepositoryQueueItem(
                repository_name=repo['full_name'],
                repository_url=repo['html_url'],
                job_id=job_id
            )
            repo_queue_mp.put(queue_item)  # Put the queue item directly into multiprocessing queue
        
        logger.info(f"✅ All {len(repositories)} repositories queued")
        
        # Start workers
        logger.info("🔧 Starting worker processes...")
        worker_processes = run_workers(config, job_id)
        
        # Wait for completion
        logger.info("⏳ Waiting for processing to complete...")
        success = wait_for_completion(queue_manager, job_id, len(repositories))
        
        # Create final merged analysis
        if success:
            final_file = create_final_analysis(job_id, output_dir)
            
            logger.info("🎉 EXACT TEST COMPLETED SUCCESSFULLY!")
            logger.info(f"📊 Results:")
            logger.info(f"   Repositories processed: {len(repositories)}")
            logger.info(f"   Final analysis: {final_file}")
            logger.info(f"   Logs directory: {output_dir / 'logs'}")
            logger.info(f"   Job ID: {job_id}")
            
            return True
        else:
            logger.error("❌ Test did not complete successfully")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        # Clean up worker processes
        try:
            for p in worker_processes:
                if p.is_alive():
                    p.terminate()
                    p.join(timeout=5)
        except:
            pass

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
