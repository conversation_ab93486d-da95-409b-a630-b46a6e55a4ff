version: '3.8'

services:
  repo-scraper:
    build: .
    container_name: repository-research-service
    ports:
      - "8080:8080"
    volumes:
      - ./output:/app/output
      - ./logs:/app/logs
      - ./.env:/app/.env
    environment:
      - PYTHONUNBUFFERED=1
      - PORT=8080
    # Service runs automatically, access via http://localhost:8080
    # For CLI usage: docker-compose exec repo-scraper python main.py --keywords "..." --min-stars 30 --max-repos 20
