# Simplified Repository Research Tool

A streamlined tool for researching GitHub repositories using AI-powered analysis. This tool searches repositories by keywords, processes them with repomix, and generates comprehensive summaries using LLM analysis.

## Overview

This simplified version focuses on the core workflow:
1. **GitHub Search**: Search repositories by keywords (sorted by last updated, min stars filter)
2. **Repomix Processing**: Process repositories remotely with `npx repomix --remote`
3. **Smart Chunking**: Split files >3MB into max 3 parts
4. **LLM Analysis**: 2 workers processing 1 file/min each for comprehensive summaries
5. **Aggregated Output**: Single markdown file per keyword with all summaries

## Features

- **Simplified Workflow**: Streamlined 5-step process without unnecessary complexity
- **Remote Processing**: No local cloning required using `repomix --remote`
- **Fixed Chunking**: Simple 3MB size-based splitting (max 3 parts)
- **Dual Workers**: 2 LLM workers processing 1 file per minute each
- **Single Output**: Aggregated analysis file per keyword
- **Cloud Ready**: Easy containerization and deployment
- **Configurable LLM**: Easy switching of LLM provider, base URL, model, API key
- **Sentry Integration**: Comprehensive error tracking and monitoring

## Quick Start

### Prerequisites

- Python 3.11+
- Node.js and npm (for repomix)
- GitHub API token
- LLM API key (Gemini, OpenAI, etc.)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd repomixed
```

2. Install dependencies:
```bash
pip install -r requirements.txt
npm install -g repomix
```

3. Set up environment variables:
```bash
# Create .env file
GITHUB_TOKEN=your_github_token
LLM_API_KEY=your_llm_api_key
LLM_BASE_URL=https://generativelanguage.googleapis.com/v1beta
LLM_MODEL=gemini-2.5-flash
SENTRY_DSN=your_sentry_dsn  # optional
```

### Basic Usage

```python
from simple_repo_scraper import SimpleRepoScraper

# Initialize scraper
scraper = SimpleRepoScraper(
    keywords=["ai browse", "web agent", "llm browse", "browse agent"]
)

# Run analysis
scraper.run()
```

### Web Service

```bash
# Start web service
python web_service.py

# Or use Docker
docker run -p 8080:8080 -e GITHUB_TOKEN=... -e LLM_API_KEY=... repo-scraper
```

## Configuration

### Easy LLM Provider Switching

```bash
# For Gemini
LLM_BASE_URL=https://generativelanguage.googleapis.com/v1beta
LLM_MODEL=gemini-2.5-flash
LLM_API_KEY=your_gemini_key

# For OpenAI
LLM_BASE_URL=https://api.openai.com/v1
LLM_MODEL=gpt-4
LLM_API_KEY=your_openai_key

# For other providers
LLM_BASE_URL=your_provider_url
LLM_MODEL=your_model
LLM_API_KEY=your_api_key
```

### Key Settings

- `MAX_REPOS_PER_KEYWORD`: 80 (default)
- `MIN_STARS`: 100 (default)
- `MAX_FILE_SIZE_MB`: 3
- `MAX_CHUNKS`: 3 (only first 3 parts if chunked)
- `LLM_WORKERS`: 2 (processing 1 file/min each)

## Docker Deployment

```bash
# Build
docker build -t simple-repo-scraper .

# Run
docker run -p 8080:8080 \
  -e GITHUB_TOKEN=your_token \
  -e LLM_API_KEY=your_key \
  -e LLM_BASE_URL=your_url \
  -e LLM_MODEL=your_model \
  simple-repo-scraper
```

## API Endpoints

- `GET /health` - Health check
- `POST /start` - Start scraping job
- `GET /status/<job_id>` - Check job status
- `GET /results/<job_id>` - Get job results
- `GET /download/<job_id>/<keyword>` - Download analysis file
- `GET /jobs` - List all jobs
- `GET /config` - Get current configuration

## Output Structure

```
output/
├── {timestamp}/
│   ├── {keyword1}/
│   │   ├── repomixes/
│   │   │   ├── repo1.md
│   │   │   ├── repo1_part1.md (if chunked)
│   │   │   └── repo2.md
│   │   └── aggregated_repo_analysis.md  # Main output
│   └── {keyword2}/
│       └── aggregated_repo_analysis.md
```

## Testing & Validation

### Workflow Validation
```bash
python test_simple_workflow.py
```

### Token Limit Testing
```bash
python test_token_limits.py
```

### Check workflow_2.md
The workflow is documented and maintained in `workflow_2.md` for validation.

## Key Simplifications

1. **No complex metadata extraction** - just name, URL, description
2. **Fixed 3MB chunking** - simple size-based splitting
3. **2 workers only** - processing 1 file/min each
4. **Single aggregated output** - one file per keyword
5. **Remote processing** - no local cloning required
6. **Straightforward configuration** - easy LLM provider switching

## Monitoring

- **Sentry Integration**: Automatic error tracking and issue detection
- **Simple Logging**: Key metrics and processing status
- **Workflow Validation**: Through workflow_2.md and log-based testing

## Example Usage

```bash
# Run with default settings
python simple_repo_scraper.py

# Run web service
python web_service.py

# Test workflow
python test_simple_workflow.py

# Test token limits
python test_token_limits.py
```

## License

MIT License - see LICENSE file for details.
