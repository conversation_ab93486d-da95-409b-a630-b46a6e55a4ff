#!/usr/bin/env python3
"""
Setup script for cloud infrastructure (Redis + Supabase).
"""

import os
import sys
import subprocess
import requests
import time

def check_redis_connection(redis_url):
    """Check if Redis is accessible."""
    try:
        import redis
        r = redis.from_url(redis_url)
        r.ping()
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

def check_supabase_connection(supabase_url, supabase_key):
    """Check if Supabase is accessible."""
    try:
        from supabase import create_client
        client = create_client(supabase_url, supabase_key)
        # Try a simple operation on our jobs table
        result = client.table('jobs').select('*').limit(1).execute()
        return True
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def setup_supabase_schema(supabase_url, supabase_key):
    """Set up the required database schema in Supabase."""
    try:
        from supabase import create_client
        client = create_client(supabase_url, supabase_key)
        
        # Read the schema file
        schema_file = os.path.join('src', 'database_schema.sql')
        if os.path.exists(schema_file):
            with open(schema_file, 'r') as f:
                schema_sql = f.read()
            
            print("📊 Setting up Supabase database schema...")
            # Note: This would need to be run manually in Supabase SQL editor
            print("⚠️  Please run the following SQL in your Supabase SQL editor:")
            print("-" * 60)
            print(schema_sql)
            print("-" * 60)
            return True
        else:
            print("❌ Database schema file not found")
            return False
            
    except Exception as e:
        print(f"❌ Schema setup failed: {e}")
        return False

def test_cloud_pipeline():
    """Test the cloud pipeline with a small job."""
    print("\n🧪 Testing cloud pipeline...")
    
    try:
        # Import cloud components
        sys.path.insert(0, 'src')
        from cloud_pipeline import CloudRepositoryPipeline
        from config import ScraperConfig
        
        config = ScraperConfig()
        if not config.USE_CLOUD_SERVICES:
            print("❌ Cloud services not enabled in configuration")
            return False
        
        # Create a test pipeline
        pipeline = CloudRepositoryPipeline(config)
        print("✅ Cloud pipeline initialized successfully")
        
        # Test queue operations
        from redis_queue import QueueManager
        queue_manager = QueueManager(config.REDIS_URL)
        
        # Test repository queue
        repo_queue = queue_manager.get_repository_queue()
        print("✅ Repository queue accessible")
        
        # Test file queue
        file_queue = queue_manager.get_file_queue()
        print("✅ File queue accessible")
        
        print("✅ Cloud pipeline test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Cloud pipeline test failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 CLOUD INFRASTRUCTURE SETUP")
    print("=" * 60)

    # Check if required packages are installed
    required_packages = ['redis', 'supabase']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("📦 Installing missing packages...")
        import subprocess
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    deployment_mode = os.getenv('DEPLOYMENT_MODE', 'local')
    redis_url = os.getenv('REDIS_URL')
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    
    print(f"📋 Current Configuration:")
    print(f"   Deployment Mode: {deployment_mode}")
    print(f"   Redis URL: {redis_url or 'Not configured'}")
    print(f"   Supabase URL: {supabase_url or 'Not configured'}")
    print(f"   Supabase Key: {'Set' if supabase_key else 'Not configured'}")
    
    if deployment_mode != 'cloud':
        print("\n⚠️  Cloud mode not enabled.")
        print("   To enable cloud infrastructure:")
        print("   1. Set DEPLOYMENT_MODE=cloud in .env")
        print("   2. Configure REDIS_URL in .env")
        print("   3. Configure SUPABASE_URL and SUPABASE_KEY in .env")
        print("   4. Run this script again")
        return False
    
    # Test connections
    print(f"\n🔍 Testing Cloud Connections:")
    
    redis_ok = False
    supabase_ok = False
    
    if redis_url:
        print(f"📡 Testing Redis connection...")
        redis_ok = check_redis_connection(redis_url)
        if redis_ok:
            print("✅ Redis connection successful")
        else:
            print("❌ Redis connection failed")
    else:
        print("⚠️  Redis URL not configured")
    
    if supabase_url and supabase_key:
        print(f"📊 Testing Supabase connection...")
        supabase_ok = check_supabase_connection(supabase_url, supabase_key)
        if supabase_ok:
            print("✅ Supabase connection successful")
            # Setup schema
            setup_supabase_schema(supabase_url, supabase_key)
        else:
            print("❌ Supabase connection failed")
    else:
        print("⚠️  Supabase credentials not configured")
    
    # Test cloud pipeline if connections are working
    if redis_ok and supabase_ok:
        test_cloud_pipeline()
        
        print(f"\n🎉 CLOUD INFRASTRUCTURE READY!")
        print("=" * 50)
        print("✅ Redis queues operational")
        print("✅ Supabase database connected")
        print("✅ Cloud pipeline functional")
        print("✅ Distributed processing enabled")
        
        print(f"\n🚀 To use cloud infrastructure:")
        print("   docker run -p 8080:8080 --env-file .env repository-research-tool:latest")
        
        return True
    else:
        print(f"\n❌ CLOUD INFRASTRUCTURE NOT READY")
        print("=" * 50)
        print("   Please configure the missing services and try again")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
