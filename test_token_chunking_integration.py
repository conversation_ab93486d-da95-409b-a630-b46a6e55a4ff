#!/usr/bin/env python3
"""
Integration test for token-based chunking functionality.

Tests the actual token chunking logic that will be used in repomix_worker.
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class TestTokenChunkingIntegration(unittest.TestCase):
    """Test token-based chunking integration."""
    
    def create_large_content(self, target_tokens: int) -> str:
        """Create content with approximately target_tokens tokens."""
        # Rough estimation: ~1.3 tokens per word, ~10 words per line
        words_per_line = 10
        tokens_per_line = words_per_line * 1.3
        lines_needed = int(target_tokens / tokens_per_line)
        
        content_lines = []
        for i in range(lines_needed):
            # Create a line with ~10 words
            line = f"def function_{i}(param1, param2, param3): return param1 + param2 * param3"
            content_lines.append(line)
        
        return '\n'.join(content_lines)
    
    def test_token_counting(self):
        """Test that token counting works correctly."""
        from token_chunking import count_tokens_in_text
        
        # Test with small content
        small_content = "This is a test with about ten words in it."
        token_count = count_tokens_in_text(small_content)
        
        # Should be approximately 10-15 tokens
        self.assertGreater(token_count, 8)
        self.assertLess(token_count, 20)
        
        print(f"✅ Small content token count: {token_count}")
    
    def test_chunking_large_content(self):
        """Test that large content gets chunked correctly."""
        from token_chunking import get_token_chunker, count_tokens_in_text
        
        # Create content with >850K tokens
        large_content = self.create_large_content(1000000)  # 1M tokens
        
        # Count tokens in the content
        total_tokens = count_tokens_in_text(large_content)
        print(f"📊 Total tokens in large content: {total_tokens:,}")
        
        # Should be chunked
        self.assertGreater(total_tokens, 850000)
        
        # Get chunker and chunk the content
        chunker = get_token_chunker(max_tokens_per_chunk=850000, max_chunks=3)
        chunks = chunker.chunk_content_by_tokens(large_content)
        
        # Should create exactly 3 chunks (max limit)
        self.assertEqual(len(chunks), 3)
        
        # Each chunk should be <= 850K tokens
        for i, chunk in enumerate(chunks):
            chunk_tokens = count_tokens_in_text(chunk)
            print(f"📝 Chunk {i} tokens: {chunk_tokens:,}")
            self.assertLessEqual(chunk_tokens, 850000)
        
        print(f"✅ Large content chunked into {len(chunks)} parts")
    
    def test_no_chunking_small_content(self):
        """Test that small content doesn't get chunked."""
        from token_chunking import get_token_chunker, count_tokens_in_text
        
        # Create content with <850K tokens
        small_content = self.create_large_content(300000)  # 300K tokens
        
        # Count tokens in the content
        total_tokens = count_tokens_in_text(small_content)
        print(f"📊 Total tokens in small content: {total_tokens:,}")
        
        # Should not be chunked
        self.assertLessEqual(total_tokens, 850000)
        
        # Get chunker and chunk the content
        chunker = get_token_chunker(max_tokens_per_chunk=850000, max_chunks=3)
        chunks = chunker.chunk_content_by_tokens(small_content)
        
        # Should create exactly 1 chunk (no chunking needed)
        self.assertEqual(len(chunks), 1)
        
        # The single chunk should be the original content
        self.assertEqual(chunks[0], small_content)
        
        print(f"✅ Small content not chunked (1 part)")
    
    def test_chunking_workflow_simulation(self):
        """Test the complete chunking workflow as it would be used in repomix_worker."""
        from token_chunking import count_tokens_in_text, get_token_chunker
        
        # Simulate repomix output content
        test_content = self.create_large_content(2000000)  # 2M tokens
        
        # Step 1: Check token count
        token_count = count_tokens_in_text(test_content)
        print(f"🔍 Simulated repomix output: {token_count:,} tokens")
        
        # Step 2: Decide whether to chunk
        if token_count > 850000:
            print("🔄 Token count exceeds 850K - chunking required")
            
            # Step 3: Use TokenChunker to split into parts, keeping only first 3
            chunker = get_token_chunker(max_tokens_per_chunk=850000, max_chunks=3)
            chunks = chunker.chunk_content_by_tokens(test_content)
            
            print(f"📝 Split into {len(chunks)} chunks")
            
            # Step 4: Simulate uploading each chunk
            chunk_urls = []
            for i, chunk_content in enumerate(chunks):
                chunk_filename = f"test_repo_chunk_{i}.md"
                # In real implementation, this would upload to Supabase Storage
                chunk_url = f"https://storage.test/job_123/{chunk_filename}"
                chunk_urls.append(chunk_url)
                print(f"📤 Simulated upload: {chunk_filename}")
            
            # Verify we got exactly 3 chunks
            self.assertEqual(len(chunks), 3)
            self.assertEqual(len(chunk_urls), 3)
            
        else:
            print("✅ Token count within 850K limit - no chunking needed")
            # Would upload single file
            chunk_urls = ["https://storage.test/job_123/test_repo.md"]
        
        print(f"✅ Chunking workflow simulation completed with {len(chunk_urls)} files")

if __name__ == '__main__':
    print("🧪 Testing Token-Based Chunking Integration...")
    unittest.main(verbosity=2)
