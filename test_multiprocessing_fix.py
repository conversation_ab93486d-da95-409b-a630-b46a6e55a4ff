#!/usr/bin/env python3
"""
Test the multiprocessing queue fix for worker communication.
This implements DeepView's solution using multiprocessing.Manager().
"""

import sys
import os
import multiprocessing
import time
import logging
from dataclasses import dataclass
from typing import Optional

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

@dataclass
class TestQueueItem:
    """Simple test item for queue communication."""
    repository_name: str
    repository_url: str
    job_id: str

def test_worker(worker_id: str, config_dict: dict, job_id: str, repo_queue_mp, file_queue_mp):
    """Test worker function using multiprocessing queues."""
    import logging
    
    # Initialize logger first
    logger = logging.getLogger(f'test_worker_{worker_id}')
    logger.info(f"Test Worker {worker_id} started for job {job_id}")
    
    processed_count = 0
    
    while True:
        try:
            # Get item from multiprocessing queue
            repo_item: Optional[TestQueueItem] = repo_queue_mp.get(timeout=10)
            if repo_item is None:
                logger.info(f"Test Worker {worker_id}: No more items, exiting")
                break
            
            logger.info(f"Test Worker {worker_id}: Processing {repo_item.repository_name}")
            
            # Simulate processing
            time.sleep(1)
            processed_count += 1
            
            # Create a result item for file queue
            result_item = TestQueueItem(
                repository_name=repo_item.repository_name,
                repository_url=repo_item.repository_url + "_processed",
                job_id=job_id
            )
            file_queue_mp.put(result_item)
            
            logger.info(f"Test Worker {worker_id}: Completed {repo_item.repository_name}")
            
        except Exception as e:
            logger.error(f"Test Worker {worker_id}: Error processing: {e}", exc_info=True)
            continue
    
    logger.info(f"Test Worker {worker_id}: Finished processing {processed_count} items")

def main():
    """Test multiprocessing queue communication."""
    print("🧪 TESTING MULTIPROCESSING QUEUE FIX")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Create multiprocessing manager for shared queues
    manager = multiprocessing.Manager()
    repo_queue_mp = manager.Queue()
    file_queue_mp = manager.Queue()
    
    logger.info("✅ Multiprocessing queues initialized")
    
    # Create test data
    test_repos = [
        TestQueueItem("test/repo1", "https://github.com/test/repo1", "test-job-1"),
        TestQueueItem("test/repo2", "https://github.com/test/repo2", "test-job-1"),
        TestQueueItem("test/repo3", "https://github.com/test/repo3", "test-job-1"),
    ]
    
    # Populate repository queue
    for repo in test_repos:
        repo_queue_mp.put(repo)
        logger.info(f"📤 Queued: {repo.repository_name}")
    
    logger.info(f"✅ Queued {len(test_repos)} test repositories")
    
    # Start test workers
    config_dict = {"test": "config"}
    job_id = "test-job-1"
    
    workers = []
    for i in range(2):  # Start 2 test workers
        p = multiprocessing.Process(
            target=test_worker,
            args=(f"worker_{i}", config_dict, job_id, repo_queue_mp, file_queue_mp)
        )
        p.start()
        workers.append(p)
        logger.info(f"🔧 Started test worker {i}")
    
    # Monitor progress
    start_time = time.time()
    timeout = 30  # 30 second timeout
    
    while time.time() - start_time < timeout:
        repo_size = repo_queue_mp.qsize()
        file_size = file_queue_mp.qsize()
        
        logger.info(f"📊 Queue status: repo_queue={repo_size}, file_queue={file_size}")
        
        if repo_size == 0 and file_size == len(test_repos):
            logger.info("🎉 All items processed successfully!")
            break
        
        time.sleep(2)
    
    # Wait for workers to complete
    for p in workers:
        p.join(timeout=5)
        if p.is_alive():
            logger.warning(f"Worker {p.pid} still alive, terminating")
            p.terminate()
    
    # Check results
    results = []
    while not file_queue_mp.empty():
        try:
            result = file_queue_mp.get_nowait()
            results.append(result)
        except:
            break
    
    print("\n🎯 TEST RESULTS:")
    print(f"   Input repositories: {len(test_repos)}")
    print(f"   Processed results: {len(results)}")
    print(f"   Success rate: {len(results)}/{len(test_repos)}")
    
    if len(results) == len(test_repos):
        print("✅ MULTIPROCESSING QUEUE FIX SUCCESSFUL!")
        print("   Workers can now communicate via shared queues")
        return True
    else:
        print("❌ Test failed - workers not processing correctly")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
