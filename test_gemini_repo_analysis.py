#!/usr/bin/env python3
"""
Test Gemini API for repository analysis workflow
"""

import requests
import json
import time
import os

# API Configuration
API_KEY = "AIzaSyDmNPg_95eUXq8beQuGcGm2iKPwxYuQv20"
BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models"

def test_repo_analysis():
    """Test Gemini API for repository analysis"""
    print("🔍 Testing Gemini API for Repository Analysis...")
    
    model = "gemini-2.5-flash"
    url = f"{BASE_URL}/{model}:generateContent?key={API_KEY}"
    
    # Sample repository content (simulating repomix output)
    sample_repo_content = """
# Repository: example-web-app
## File: src/app.py
```python
from flask import Flask, render_template, request, jsonify
import sqlite3
import os

app = Flask(__name__)

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/api/users', methods=['GET', 'POST'])
def users():
    if request.method == 'GET':
        conn = sqlite3.connect('users.db')
        users = conn.execute('SELECT * FROM users').fetchall()
        conn.close()
        return jsonify(users)
    elif request.method == 'POST':
        data = request.json
        conn = sqlite3.connect('users.db')
        conn.execute('INSERT INTO users (name, email) VALUES (?, ?)', 
                    (data['name'], data['email']))
        conn.commit()
        conn.close()
        return jsonify({'status': 'success'})

if __name__ == '__main__':
    app.run(debug=True)
```

## File: templates/index.html
```html
<!DOCTYPE html>
<html>
<head>
    <title>User Management</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>User Management System</h1>
    <form id="userForm">
        <input type="text" id="name" placeholder="Name" required>
        <input type="email" id="email" placeholder="Email" required>
        <button type="submit">Add User</button>
    </form>
    <div id="userList"></div>
    
    <script>
        $('#userForm').submit(function(e) {
            e.preventDefault();
            $.post('/api/users', {
                name: $('#name').val(),
                email: $('#email').val()
            }, function() {
                loadUsers();
            });
        });
        
        function loadUsers() {
            $.get('/api/users', function(users) {
                $('#userList').html(users.map(u => `<p>${u[1]} - ${u[2]}</p>`).join(''));
            });
        }
        
        loadUsers();
    </script>
</body>
</html>
```

## File: requirements.txt
```
Flask==2.3.3
```
"""
    
    analysis_prompt = """
Analyze this repository and provide a comprehensive summary of its features and functionality. 

Please provide:
1. **Primary Purpose**: What does this application do?
2. **Core Features**: List all main features with underlying technologies
3. **Architecture**: Describe the technical architecture and components
4. **Technologies Used**: List all frameworks, libraries, and technologies
5. **API Endpoints**: Document all API endpoints and their functionality
6. **Database Schema**: Describe data storage and schema
7. **Frontend Functionality**: Describe user interface features
8. **Security Considerations**: Note any security aspects or concerns
9. **Deployment Requirements**: What's needed to run this application

Be comprehensive and detailed in your analysis.

Repository Content:
"""
    
    payload = {
        "contents": [{
            "parts": [{
                "text": analysis_prompt + sample_repo_content
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 4000,
            "topP": 0.8,
            "topK": 40
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("📤 Sending analysis request...")
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.2f}s")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            candidates = data.get('candidates', [])
            if candidates:
                content = candidates[0].get('content', {})
                parts = content.get('parts', [])
                if parts:
                    analysis = parts[0].get('text', '')
                    
                    print("✅ Analysis completed successfully!")
                    print("\n" + "="*60)
                    print("📋 REPOSITORY ANALYSIS RESULT:")
                    print("="*60)
                    print(analysis)
                    print("="*60)
                    
                    # Save analysis to file
                    with open('gemini_analysis_result.md', 'w', encoding='utf-8') as f:
                        f.write("# Repository Analysis Result\n\n")
                        f.write(f"**Analysis Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"**Model:** {model}\n")
                        f.write(f"**Response Time:** {end_time - start_time:.2f}s\n\n")
                        f.write("## Analysis\n\n")
                        f.write(analysis)
                    
                    print(f"\n💾 Analysis saved to: gemini_analysis_result.md")
                    return True
                    
            print("❌ No valid response content found")
            return False
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_token_counting():
    """Test token counting for the analysis"""
    print("\n🔢 Testing Token Counting...")
    
    model = "gemini-2.5-flash"
    url = f"{BASE_URL}/{model}:countTokens?key={API_KEY}"
    
    test_content = "This is a test message for token counting. How many tokens does this contain?"
    
    payload = {
        "contents": [{
            "parts": [{
                "text": test_content
            }]
        }]
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            token_count = data.get('totalTokens', 0)
            print(f"✅ Token count: {token_count} tokens")
            print(f"📝 Text: '{test_content}'")
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Run repository analysis tests"""
    print("🚀 Starting Gemini Repository Analysis Tests\n")
    
    # Test 1: Repository analysis
    analysis_ok = test_repo_analysis()
    
    # Test 2: Token counting
    tokens_ok = test_token_counting()
    
    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY:")
    print(f"  Repository Analysis: {'✅ PASS' if analysis_ok else '❌ FAIL'}")
    print(f"  Token Counting: {'✅ PASS' if tokens_ok else '❌ FAIL'}")
    
    overall_success = analysis_ok and tokens_ok
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if analysis_ok:
        print("\n📄 Check 'gemini_analysis_result.md' for the detailed analysis output!")
    
    return overall_success

if __name__ == "__main__":
    main()
