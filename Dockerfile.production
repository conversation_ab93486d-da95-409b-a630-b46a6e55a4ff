# Multi-stage production Dockerfile for Repository Research Tool
# Optimized for cloud deployment with minimal attack surface

# Stage 1: Node.js dependencies
FROM node:18-alpine AS node-builder

# Install repomix globally
RUN npm install -g repomix

# Stage 2: Python dependencies builder
FROM python:3.11-slim AS python-builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Stage 3: Production runtime
FROM python:3.11-slim AS production

# Install minimal runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && rm -rf /tmp/* /var/tmp/*

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy Node.js and repomix from node-builder
COPY --from=node-builder /usr/local/bin/node /usr/local/bin/
COPY --from=node-builder /usr/local/lib/node_modules /usr/local/lib/node_modules
COPY --from=node-builder /usr/local/bin/npm /usr/local/bin/
COPY --from=node-builder /usr/local/bin/npx /usr/local/bin/

# Create symlinks for global npm packages
RUN ln -sf /usr/local/lib/node_modules/repomix/bin/repomix.js /usr/local/bin/repomix

# Copy Python dependencies from python-builder
COPY --from=python-builder /root/.local /home/<USER>/.local

# Copy application code
COPY src/ ./src/
COPY main.py .
COPY service.py .
COPY worker.py .
COPY entrypoint.sh .

# Create directories and set permissions
RUN mkdir -p output logs tmp \
    && chown -R appuser:appuser /app \
    && chmod -R 755 /app \
    && chmod +x /app/entrypoint.sh

# Switch to non-root user
USER appuser

# Set environment variables
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PORT=8080
ENV DEPLOYMENT_MODE=cloud

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port
EXPOSE 8080

# Use entrypoint script for service selection
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["api"]
