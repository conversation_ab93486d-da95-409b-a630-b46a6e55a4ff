#!/usr/bin/env python3
"""
Test ONLY the chunking component to ensure it follows your exact rules:
1. Check if file > 3MB
2. If yes, chunk to 3MB parts
3. Keep only first 3 chunks
"""

import sys
import os
import tempfile
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_chunking_logic():
    """Test the exact chunking logic you specified."""
    print("🧪 TESTING CHUNKING COMPONENT")
    print("=" * 50)
    print("YOUR RULES:")
    print("1. Check if file > 3MB")
    print("2. If yes, chunk to 3MB parts") 
    print("3. Keep only first 3 chunks")
    print()
    
    try:
        from utils import chunk_and_retain_file, get_file_size_mb
        
        # Create a test file > 9MB to test max_chunks=3 limit
        test_content = "A" * (10 * 1024 * 1024)  # 10MB of 'A' characters
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
            f.write(test_content)
            test_file_path = f.name
        
        print(f"📁 Created test file: {test_file_path}")
        file_size = get_file_size_mb(test_file_path)
        print(f"📊 File size: {file_size:.2f} MB")
        
        if file_size <= 3:
            print("❌ Test file should be > 3MB")
            return False

        print(f"✅ File is > 3MB ({file_size:.2f} MB)")
        print()

        # Test chunking
        print("🔪 Testing chunking logic...")
        chunks = chunk_and_retain_file(test_file_path, max_size_mb=3, max_chunks=3)

        print(f"📊 Number of chunks created: {len(chunks)}")

        # For a 10MB file with max_chunks=3, we expect exactly 3 chunks
        # (3MB + 3MB + 3MB, with 1MB discarded)
        expected_chunks = 3
        if len(chunks) != expected_chunks:
            print(f"❌ Expected {expected_chunks} chunks (max_chunks limit), got {len(chunks)}")
            return False

        print(f"✅ Correct number of chunks ({expected_chunks}) - max_chunks limit working")
        
        # Check each chunk size
        for i, chunk_path in enumerate(chunks):
            chunk_size = get_file_size_mb(chunk_path)
            print(f"   Chunk {i+1}: {chunk_size:.2f} MB")
            
            if chunk_size > 3.1:  # Allow small margin for headers
                print(f"❌ Chunk {i+1} is too large: {chunk_size:.2f} MB")
                return False
        
        print("✅ All chunks are ≤ 3MB")
        print()
        
        # Cleanup (original file is already deleted by chunking function)
        for chunk_path in chunks:
            if os.path.exists(chunk_path):
                os.unlink(chunk_path)
        
        print("🎉 CHUNKING COMPONENT TEST PASSED!")
        print("✅ Follows your exact rules:")
        print("   - Checks if file > 3MB")
        print("   - Chunks to 3MB parts")
        print("   - Keeps only first 3 chunks")
        
        return True
        
    except Exception as e:
        print(f"❌ Chunking test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_small_file():
    """Test that files ≤ 3MB are not chunked."""
    print("\n🧪 TESTING SMALL FILE (≤ 3MB)")
    print("=" * 50)
    
    try:
        from utils import chunk_and_retain_file, get_file_size_mb
        
        # Create a small test file
        test_content = "B" * (2 * 1024 * 1024)  # 2MB
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
            f.write(test_content)
            test_file_path = f.name
        
        file_size = get_file_size_mb(test_file_path)
        print(f"📊 File size: {file_size:.2f} MB")
        
        if file_size > 3:
            print("❌ Test file should be ≤ 3MB")
            return False
        
        # Test chunking
        chunks = chunk_and_retain_file(test_file_path, max_size_mb=3, max_chunks=3)
        
        print(f"📊 Number of chunks: {len(chunks)}")
        
        if len(chunks) != 1:
            print(f"❌ Small file should return 1 chunk (original), got {len(chunks)}")
            return False
        
        if chunks[0] != test_file_path:
            print("❌ Small file should return original path")
            return False
        
        print("✅ Small file correctly not chunked")
        
        # Cleanup
        os.unlink(test_file_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Small file test failed: {e}")
        return False

def main():
    """Test chunking component."""
    success1 = test_chunking_logic()
    success2 = test_small_file()
    
    if success1 and success2:
        print("\n🎉 ALL CHUNKING TESTS PASSED!")
        print("✅ Component is working correctly")
        return True
    else:
        print("\n❌ CHUNKING TESTS FAILED!")
        print("❌ Component needs fixing")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
