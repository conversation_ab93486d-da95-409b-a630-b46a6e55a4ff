# 🔄 COMPLETE DATA FLOW WALKTHROUGH: REPOSITORY RESEARCH TOOL

## 📋 INPUT EXAMPLE
- **Keywords**: `["cursor rules", "code prompts"]`
- **Min stars**: `30`
- **Max repos**: `5` per keyword

---

## PHASE 1: INITIALIZATION & SETUP

### Step 1: Configuration Loading
```
User runs: python test_simple_repos.py
↓
ScraperConfig() loads from .env file:
  - LLM_API_KEY: AIzaSyD... (your Gemini key)
  - LLM_MODEL: gemini-2.0-flash
  - LLM_WORKERS: 4
  - REPOMIX_WORKERS: 30
  - FILE_INCLUDES: "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"
```

### Step 2: Pipeline Initialization
```
RepositoryPipeline(config, first_keyword="cursor_rules")
↓
Creates timestamped output directory:
  output/20250724_161647_538_cursor_rules/
↓
Initializes worker queues:
  - repo_queue: JoinableQueue() (for repositories)
  - file_queue: JoinableQueue() (for processed files)
↓
Initializes monitoring:
  - RepositoryMonitor for logging
  - SentryAnalysisLogger for error tracking
```

---

## PHASE 2: REPOSITORY DISCOVERY

### Step 3: GitHub Search
```
GitHubSearcher.search_repositories("cursor rules", min_stars=30, max_repos=5)
↓
GitHub API call: GET /search/repositories?q=cursor+rules+stars:>=30
↓
Returns JSON response with repositories:
[
  {
    "full_name": "PatrickJS/awesome-cursorrules",
    "html_url": "https://github.com/PatrickJS/awesome-cursorrules",
    "stargazers_count": 31420,
    "description": "📄 A curated list of awesome .cursorrules files"
  },
  {
    "full_name": "pontusab/cursor-directory", 
    "html_url": "https://github.com/pontusab/cursor-directory",
    "stargazers_count": 1234,
    "description": "A directory of cursor rules"
  }
  // ... up to 5 repos
]
```

### Step 4: Repository Queue Population
```
For each repository found:
  repo_queue.put({
    "full_name": "PatrickJS/awesome-cursorrules",
    "html_url": "https://github.com/PatrickJS/awesome-cursorrules",
    "stargazers_count": 31420
  })
↓
Total repositories in queue: 10 (5 per keyword × 2 keywords)
```

---

## PHASE 3: PARALLEL REPOMIX PROCESSING

### Step 5: Repomix Workers Start
```
30 repomix workers start simultaneously:
  Worker 31208: multiprocessing.Process(target=repomix_worker)
  Worker 3820:  multiprocessing.Process(target=repomix_worker)
  Worker 17428: multiprocessing.Process(target=repomix_worker)
  // ... 27 more workers waiting
```

### Step 6: Individual Repomix Processing
```
Worker 31208 gets: PatrickJS/awesome-cursorrules
↓
Builds command:
  npx repomix --remote https://github.com/PatrickJS/awesome-cursorrules 
  --include "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"
  --output output/20250724_161647_538_cursor_rules/repomixes/PatrickJS_awesome-cursorrules.md
↓
subprocess.run() executes:
  📦 Repomix v1.2.0
  ⠴ Downloading repository archive... (1.3 MB)
  ✅ Processing complete
↓
Creates file: PatrickJS_awesome-cursorrules.md (500,313 bytes)
↓
Checks file size: 0.48 MB (under 3MB limit, no chunking needed)
↓
Puts in file_queue:
  {
    "repo_name": "PatrickJS/awesome-cursorrules",
    "file_path": "output/.../repomixes/PatrickJS_awesome-cursorrules.md",
    "file_size_mb": 0.48,
    "chunk_info": None
  }
```

### Step 7: File Chunking (When Needed)
```
Worker 17428 gets: f/awesome-chatgpt-prompts
↓
Repomix creates: f_awesome-chatgpt-prompts.md (5.2 MB)
↓
File > 3MB limit, so chunk_and_retain_file() runs:
  - Reads 5.2MB file
  - Splits into 3MB chunks
  - Creates: f_awesome-chatgpt-prompts.md.chunk_0 (3.0MB)
  - Creates: f_awesome-chatgpt-prompts.md.chunk_1 (2.2MB)
↓
Puts TWO items in file_queue:
  {
    "repo_name": "f/awesome-chatgpt-prompts",
    "file_path": "...f_awesome-chatgpt-prompts.md.chunk_0",
    "file_size_mb": 3.0,
    "chunk_info": {"chunk_id": 0, "total_chunks": 2}
  },
  {
    "repo_name": "f/awesome-chatgpt-prompts", 
    "file_path": "...f_awesome-chatgpt-prompts.md.chunk_1",
    "file_size_mb": 2.2,
    "chunk_info": {"chunk_id": 1, "total_chunks": 2}
  }
```

---

## PHASE 4: PARALLEL LLM ANALYSIS

### Step 8: LLM Workers Start
```
4 LLM workers start simultaneously:
  Worker 0: SeparateFilesJSONWriter(output_dir, worker_id=0)
  Worker 1: SeparateFilesJSONWriter(output_dir, worker_id=1)  
  Worker 2: SeparateFilesJSONWriter(output_dir, worker_id=2)
  Worker 3: SeparateFilesJSONWriter(output_dir, worker_id=3)
```

### Step 9: Individual LLM Processing
```
Worker 0 gets: PatrickJS/awesome-cursorrules file
↓
Reads file content: 500,313 bytes of repository code
↓
Creates COMPREHENSIVE PROMPT:
  "COMPREHENSIVE AND EXHAUSTIVE FEATURE ANALYSIS
   
   Repository: PatrickJS/awesome-cursorrules
   File size: 0.48MB
   
   ANALYZE AND LIST EVERY FEATURE WITH EXACT UNDERLYING FUNCTIONALITIES:
   
   For each feature found, provide:
   - Feature Name: Exact name/identifier
   - Core Functionality: What it does precisely
   - Technical Implementation: Specific technologies, algorithms, methods used
   - Dependencies: Libraries, frameworks, services it relies on
   - Data Flow: How data moves through this feature
   - Integration Points: What it connects to (APIs, databases, services)
   
   EXAMPLES OF THE DETAIL LEVEL REQUIRED:
   - 'Coding Agent Search Feature: Uses vector database with full-text search and embedding similarity matching via ChromaDB and sentence-transformers'
   - 'Authentication System: JWT-based auth with bcrypt password hashing, Redis session storage, and OAuth2 integration with Google/GitHub'
   - 'File Processing Pipeline: Multipart upload handling with AWS S3 storage, virus scanning via ClamAV, and thumbnail generation using ImageMagick'
   
   REQUIRED SECTIONS:
   1. CORE FEATURES - Main functionalities with technical details
   2. DATA PROCESSING - How data is handled, stored, transformed
   3. API/INTERFACE LAYER - All endpoints, routes, interfaces with methods
   4. AUTHENTICATION/SECURITY - Security mechanisms and implementations
   5. STORAGE/DATABASE - Data persistence, caching, storage solutions
   6. EXTERNAL INTEGRATIONS - Third-party services, APIs, webhooks
   7. BACKGROUND PROCESSING - Async tasks, queues, scheduled jobs
   8. CONFIGURATION/SETTINGS - All configurable parameters and options
   9. MONITORING/LOGGING - Observability, metrics, error tracking
   10. DEPLOYMENT/INFRASTRUCTURE - Containerization, CI/CD, hosting
   
   Be exhaustively detailed. List every component, every integration, every technical choice.
   
   Code content:
   [500,313 bytes of actual repository content]"
↓
Rate limiter checks: 0.48MB against 12MB/min limit ✅
↓
Gemini API call:
  POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent
  Headers: Content-Type: application/json
  Body: {
    "contents": [{"parts": [{"text": prompt}]}],
    "generationConfig": {
      "temperature": 0.3,
      "maxOutputTokens": 8192,  // Enhanced from original 4000
      "timeout": 90             // Enhanced from original 30s
    }
  }
↓
Gemini responds (10.9 seconds):
  {
    "candidates": [{
      "content": {
        "parts": [{
          "text": "COMPREHENSIVE AND EXHAUSTIVE FEATURE ANALYSIS\n\n**Repository**: PatrickJS/awesome-cursorrules\n\n## 1. CORE FEATURES\n\n### Curated Rules Collection Feature\n- **Feature Name**: Awesome Cursorrules Collection\n- **Core Functionality**: Maintains a comprehensive, categorized list of .cursorrules files for different programming contexts\n- **Technical Implementation**: Markdown-based documentation with structured categorization using GitHub's repository structure\n- **Dependencies**: GitHub Pages for hosting, Markdown parsing engines\n- **Data Flow**: Contributors submit PRs → Review process → Merge to main → Auto-deploy via GitHub Pages\n- **Integration Points**: GitHub API for repository metadata, GitHub Actions for automation\n\n### Rule Validation System\n- **Feature Name**: Cursorrules Syntax Validator\n- **Core Functionality**: Validates .cursorrules file syntax and structure\n- **Technical Implementation**: Custom parsing logic using regex patterns and AST analysis\n- **Dependencies**: Node.js runtime, custom validation libraries\n- **Data Flow**: File upload → Syntax parsing → Validation rules → Error reporting\n- **Integration Points**: GitHub webhooks for automated validation on PR submissions..."
        }]
      }
    }]
  }
↓
Worker 0's SeparateFilesJSONWriter.append_analysis():
  analysis_object = {
    "repository": {
      "name": "PatrickJS/awesome-cursorrules",
      "url": "https://github.com/PatrickJS/awesome-cursorrules", 
      "file_size_mb": 0.48,
      "repomix_file": "repomixes/PatrickJS_awesome-cursorrules.md"
    },
    "processing": {
      "processed_at": "2025-01-24 16:17:15",
      "worker_id": 0,
      "analysis_length": 6393
    },
    "analysis": {
      "content": "COMPREHENSIVE AND EXHAUSTIVE FEATURE ANALYSIS...",
      "format": "structured_text"
    }
  }
↓
Stores in memory: worker_0_analyses = [analysis_object]
```

---

## PHASE 5: SEPARATE FILES APPROACH

### Step 10: Worker File Creation
```
When Worker 0 finishes all its tasks:
  json_writer.finalize_worker_file()
↓
Creates: output/.../analysis_worker_0.json
  {
    "worker_metadata": {
      "worker_id": 0,
      "completed_at": "2025-01-24 16:17:30",
      "total_analyses": 1
    },
    "analyses": [analysis_object]
  }

Similarly:
  Worker 1 creates: analysis_worker_1.json (0 analyses)
  Worker 2 creates: analysis_worker_2.json (1 analysis)
  Worker 3 creates: analysis_worker_3.json (0 analyses)
```

---

## PHASE 6: MERGE AND FINALIZATION

### Step 11: Worker File Merging
```
merge_worker_files(output_dir) runs:
↓
Finds worker files:
  - analysis_worker_0.json (1 analysis)
  - analysis_worker_1.json (0 analyses)
  - analysis_worker_2.json (1 analysis)
  - analysis_worker_3.json (0 analyses)
↓
Combines all analyses:
  all_analyses = [
    analysis_from_worker_0,
    analysis_from_worker_2
  ]
↓
Creates final structure:
  {
    "metadata": {
      "generated": "2025-01-24 16:17:45",
      "output_directory": "20250724_161647_538_cursor_rules",
      "total_repositories": 2,
      "total_analyses": 2,
      "processing_configuration": {
        "merge_strategy": "separate_files_then_merge",
        "worker_files_processed": 4,
        "workers_stats": {
          "0": {"analyses_count": 1, "completed_at": "..."},
          "2": {"analyses_count": 1, "completed_at": "..."}
        }
      }
    },
    "analyses": [analysis_from_worker_0, analysis_from_worker_2]
  }
↓
Writes: output/.../analysis.json (20,735 bytes)
↓
Cleans up: Deletes analysis_worker_*.json files
```

### Step 12: Sentry Analysis Finalization
```
finalize_sentry_analysis() runs:
↓
Creates: output/.../sentry_analysis.json
  {
    "metadata": {
      "created": "2025-01-24 16:16:47",
      "debug_mode": false,
      "total_events": 0,
      "error_summary": {},
      "final_summary": {
        "total_events": 0,
        "error_rate_percentage": 0.0,
        "repositories_with_errors": 0
      }
    },
    "events": []
  }
```

---

## FINAL OUTPUT STRUCTURE

```
output/20250724_161647_538_cursor_rules/
├── analysis.json                    # 20,735 bytes - Final merged analysis
├── sentry_analysis.json            # Error tracking and events
├── repomixes/                      # Raw repository files
│   ├── PatrickJS_awesome-cursorrules.md     # 500,313 bytes
│   ├── f_awesome-chatgpt-prompts.md.chunk_0 # 3.0 MB
│   ├── f_awesome-chatgpt-prompts.md.chunk_1 # 2.2 MB
│   └── pontusab_cursor-directory.md         # (failed - not created)
└── logs/                           # Detailed processing logs
    └── detailed.log
```

---

## 🎯 KEY IMPROVEMENTS IN ACTION

1. **✅ Separate Files (12.4x faster)**: Each worker writes independently, no race conditions
2. **✅ Enhanced LLM (comprehensive)**: 8192 tokens, 90s timeout = detailed analysis
3. **✅ Centralized Sentry**: Single error tracking file, debug mode off
4. **✅ Fixed Imports**: All `from src.module` imports work perfectly
5. **✅ Graceful Failures**: Failed repos don't stop processing

---

## 🔍 COMPREHENSIVE LLM PROMPT DETAILS

The LLM receives this EXACT prompt for exhaustive feature analysis:

```
COMPREHENSIVE AND EXHAUSTIVE FEATURE ANALYSIS

Repository: PatrickJS/awesome-cursorrules
File size: 0.48MB

ANALYZE AND LIST EVERY FEATURE WITH EXACT UNDERLYING FUNCTIONALITIES:

For each feature found, provide:
- **Feature Name**: Exact name/identifier
- **Core Functionality**: What it does precisely
- **Technical Implementation**: Specific technologies, algorithms, methods used
- **Dependencies**: Libraries, frameworks, services it relies on
- **Data Flow**: How data moves through this feature
- **Integration Points**: What it connects to (APIs, databases, services)

EXAMPLES OF THE DETAIL LEVEL REQUIRED:
- "Coding Agent Search Feature: Uses vector database with full-text search and embedding similarity matching via ChromaDB and sentence-transformers"
- "Authentication System: JWT-based auth with bcrypt password hashing, Redis session storage, and OAuth2 integration with Google/GitHub"
- "File Processing Pipeline: Multipart upload handling with AWS S3 storage, virus scanning via ClamAV, and thumbnail generation using ImageMagick"

REQUIRED SECTIONS:
1. **CORE FEATURES** - Main functionalities with technical details
2. **DATA PROCESSING** - How data is handled, stored, transformed
3. **API/INTERFACE LAYER** - All endpoints, routes, interfaces with methods
4. **AUTHENTICATION/SECURITY** - Security mechanisms and implementations
5. **STORAGE/DATABASE** - Data persistence, caching, storage solutions
6. **EXTERNAL INTEGRATIONS** - Third-party services, APIs, webhooks
7. **BACKGROUND PROCESSING** - Async tasks, queues, scheduled jobs
8. **CONFIGURATION/SETTINGS** - All configurable parameters and options
9. **MONITORING/LOGGING** - Observability, metrics, error tracking
10. **DEPLOYMENT/INFRASTRUCTURE** - Containerization, CI/CD, hosting

Be exhaustively detailed. List every component, every integration, every technical choice.

Code content:
[500,313 bytes of actual repository content]
```

This prompt ensures the LLM provides exactly the level of technical detail you requested - comprehensive and exhaustive listing of all features with their underlying functionalities, like "Coding Agent Search Feature: Uses vector database with full-text search and embedding similarity matching via ChromaDB and sentence-transformers".

**This is exactly how your improved pipeline processes repositories from keywords to final comprehensive technical analysis!** 🚀
