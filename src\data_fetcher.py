#!/usr/bin/env python3
"""
Data fetcher for Repository Research Tool.
Auto-fetches analysis files from cloud storage to local output directory.
"""

import os
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class DataFetcher:
    """
    Fetches analysis files from cloud storage to local directory.
    Provides easy access to analysis results for review and processing.
    """
    
    def __init__(self, config=None, storage_manager=None, supabase_client=None):
        """
        Initialize data fetcher.
        
        Args:
            config: ScraperConfig instance
            storage_manager: StorageManager instance
            supabase_client: Supabase client instance
        """
        self.config = config
        self.storage_manager = storage_manager
        self.supabase_client = supabase_client
        
        # Local output directory
        self.local_output_dir = Path("output")
        self.local_output_dir.mkdir(exist_ok=True)
        
        # Downloaded files tracking
        self.downloads_dir = self.local_output_dir / "downloads"
        self.downloads_dir.mkdir(exist_ok=True)
        
        logger.info(f"Data fetcher initialized, output directory: {self.local_output_dir}")
    
    def fetch_job_analysis(self, job_id: str, force_refresh: bool = False) -> Optional[Path]:
        """
        Fetch analysis file for a specific job.
        
        Args:
            job_id: Job ID to fetch analysis for
            force_refresh: Force re-download even if file exists locally
            
        Returns:
            Path to local analysis file, or None if not found
        """
        try:
            # Create job-specific directory
            job_dir = self.downloads_dir / job_id
            job_dir.mkdir(exist_ok=True)
            
            # Check if already downloaded
            local_analysis_file = job_dir / "analysis.json"
            if local_analysis_file.exists() and not force_refresh:
                logger.info(f"Analysis file already exists locally: {local_analysis_file}")
                return local_analysis_file
            
            # Get job information from database
            if not self.supabase_client:
                logger.error("Supabase client not available for fetching job data")
                return None
            
            job_record = self.supabase_client.get_job(job_id)
            if not job_record:
                logger.error(f"Job {job_id} not found in database")
                return None
            
            # Check if job has analysis file URL
            analysis_file_url = getattr(job_record, 'analysis_file_url', None)
            if not analysis_file_url:
                logger.warning(f"Job {job_id} has no analysis file URL")
                return None
            
            # Download analysis file from storage
            if not self.storage_manager:
                logger.error("Storage manager not available for downloading files")
                return None
            
            logger.info(f"Downloading analysis file for job {job_id}...")
            analysis_content = self.storage_manager.download_analysis_file(analysis_file_url)
            
            if not analysis_content:
                logger.error(f"Failed to download analysis file for job {job_id}")
                return None
            
            # Save to local file
            with open(local_analysis_file, 'w', encoding='utf-8') as f:
                if isinstance(analysis_content, str):
                    f.write(analysis_content)
                else:
                    json.dump(analysis_content, f, indent=2)
            
            # Create metadata file
            metadata = {
                "job_id": job_id,
                "downloaded_at": datetime.now().isoformat(),
                "source_url": analysis_file_url,
                "file_size": local_analysis_file.stat().st_size,
                "job_status": getattr(job_record, 'status', 'unknown'),
                "created_at": getattr(job_record, 'created_at', None)
            }
            
            metadata_file = job_dir / "metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"✅ Analysis file downloaded: {local_analysis_file}")
            return local_analysis_file
            
        except Exception as e:
            logger.error(f"Error fetching analysis for job {job_id}: {e}")
            return None
    
    def fetch_recent_analyses(self, limit: int = 10, days: int = 7) -> List[Path]:
        """
        Fetch recent analysis files.
        
        Args:
            limit: Maximum number of analyses to fetch
            days: Number of days to look back
            
        Returns:
            List of paths to downloaded analysis files
        """
        try:
            if not self.supabase_client:
                logger.error("Supabase client not available")
                return []
            
            # Get recent completed jobs
            recent_jobs = self.supabase_client.get_recent_jobs(limit=limit, days=days)
            
            downloaded_files = []
            for job in recent_jobs:
                job_id = getattr(job, 'id', None)
                if job_id:
                    analysis_file = self.fetch_job_analysis(job_id)
                    if analysis_file:
                        downloaded_files.append(analysis_file)
            
            logger.info(f"✅ Downloaded {len(downloaded_files)} recent analysis files")
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Error fetching recent analyses: {e}")
            return []
    
    def fetch_all_analyses(self) -> List[Path]:
        """
        Fetch all available analysis files.
        
        Returns:
            List of paths to downloaded analysis files
        """
        try:
            if not self.supabase_client:
                logger.error("Supabase client not available")
                return []
            
            # Get all completed jobs
            all_jobs = self.supabase_client.get_all_completed_jobs()
            
            downloaded_files = []
            for job in all_jobs:
                job_id = getattr(job, 'id', None)
                if job_id:
                    analysis_file = self.fetch_job_analysis(job_id)
                    if analysis_file:
                        downloaded_files.append(analysis_file)
            
            logger.info(f"✅ Downloaded {len(downloaded_files)} analysis files")
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Error fetching all analyses: {e}")
            return []
    
    def list_local_analyses(self) -> List[Dict]:
        """
        List all locally downloaded analysis files.
        
        Returns:
            List of dictionaries with analysis file information
        """
        analyses = []
        
        try:
            for job_dir in self.downloads_dir.iterdir():
                if not job_dir.is_dir():
                    continue
                
                analysis_file = job_dir / "analysis.json"
                metadata_file = job_dir / "metadata.json"
                
                if analysis_file.exists():
                    # Load metadata if available
                    metadata = {}
                    if metadata_file.exists():
                        try:
                            with open(metadata_file) as f:
                                metadata = json.load(f)
                        except:
                            pass
                    
                    # Get file stats
                    stat = analysis_file.stat()
                    
                    analyses.append({
                        "job_id": job_dir.name,
                        "analysis_file": str(analysis_file),
                        "file_size": stat.st_size,
                        "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "metadata": metadata
                    })
            
            # Sort by modification time (newest first)
            analyses.sort(key=lambda x: x["modified_at"], reverse=True)
            
            return analyses
            
        except Exception as e:
            logger.error(f"Error listing local analyses: {e}")
            return []
    
    def cleanup_old_downloads(self, days: int = 30):
        """
        Clean up old downloaded files.
        
        Args:
            days: Remove files older than this many days
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
            removed_count = 0
            
            for job_dir in self.downloads_dir.iterdir():
                if not job_dir.is_dir():
                    continue
                
                # Check if directory is old
                if job_dir.stat().st_mtime < cutoff_time:
                    shutil.rmtree(job_dir)
                    removed_count += 1
                    logger.debug(f"Removed old download directory: {job_dir}")
            
            if removed_count > 0:
                logger.info(f"✅ Cleaned up {removed_count} old download directories")
            
        except Exception as e:
            logger.error(f"Error cleaning up old downloads: {e}")
    
    def create_analysis_index(self) -> Path:
        """
        Create an index file of all local analyses.
        
        Returns:
            Path to the index file
        """
        try:
            analyses = self.list_local_analyses()
            
            index = {
                "generated_at": datetime.now().isoformat(),
                "total_analyses": len(analyses),
                "analyses": analyses
            }
            
            index_file = self.downloads_dir / "index.json"
            with open(index_file, 'w') as f:
                json.dump(index, f, indent=2)
            
            logger.info(f"✅ Created analysis index: {index_file}")
            return index_file
            
        except Exception as e:
            logger.error(f"Error creating analysis index: {e}")
            return None
