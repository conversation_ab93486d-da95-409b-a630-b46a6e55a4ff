#!/usr/bin/env python3
import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
token = os.getenv('GITHUB_TOKEN')
if token and token != 'your_github_token_here':
    headers = {'Authorization': f'token {token}'}
    response = requests.get('https://api.github.com/rate_limit', headers=headers)
    if response.status_code == 200:
        data = response.json()
        print('✅ GitHub Token Working!')
        print(f'   Rate Limit: {data["rate"]["remaining"]}/{data["rate"]["limit"]}')
        if "search" in data:
            print(f'   Search Limit: {data["search"]["remaining"]}/{data["search"]["limit"]}')
        else:
            print('   Search Limit: Available')
    else:
        print(f'❌ Token Error: {response.status_code}')
else:
    print('❌ No valid token found')
