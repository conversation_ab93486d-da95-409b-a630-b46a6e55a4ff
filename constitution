The System Constitution (v3 - Final)
This document supersedes all previous instructions. It is the single source of truth for the Repository Research Tool.
I. Guiding Principles (The Prime Directives)
Real Implementation Only: All code must be real, functional, and testable. No placeholder or simulated logic is permitted.
PRD is Law: The PRD.md file is the master specification. All implementation must directly map to its requirements. Any deviation is a bug.
Component-First, Integration-Second: Each component must be developed and tested in isolation first. Only after a component is validated should it be integrated into the main pipeline.
Coherence Above All: The system must maintain a single, Unified Architecture. There is one pipeline that runs on different environments (local Docker vs. cloud services) based on .env configuration.
II. Core System Architecture
Unified Pipeline (src/pipeline.py): The system uses a single, cloud-native pipeline for all operations, always relying on Redis and Supabase.
Decoupled Services:
service_unified.py (API): Its only responsibilities are to receive HTTP requests, create job/repository records in Supabase, and enqueue initial tasks in Redis.
worker_unified.py (Worker Manager): Its only responsibility is to start, monitor, and manage the lifecycle of the standalone worker processes.
Standalone Workers: All processing logic is contained in standalone functions (repomix_worker, llm_worker). These workers are process-safe, initialize their own resources, and communicate only through Redis queues.
III. End-to-End Workflow
GitHub Search: Search repositories for each keyword, sorting by last updated. Apply min_stars and max_repos_per_keyword filters.
Deduplication: Remove duplicate repositories found across different keywords.
Enqueue for Repomix: Create records in Supabase and add tasks to the repositories Redis queue.
Repomix Processing: A repomix_worker pulls a task, runs npx repomix --remote, and saves the full output to a temporary local file.
Local Retrieval & Token-Based Chunking:
The worker checks the token count of the local repomix output.
If > 850,000 tokens: The file is chunked into parts, ensuring no chunk exceeds 850k tokens. Only the first 3 chunks are kept.
If <= 850,000 tokens: The original file is used.
Upload to Storage: The resulting file(s) (original or chunks) are uploaded to Supabase Storage.
Enqueue for LLM: A task for each uploaded file/chunk is added to the files Redis queue.
LLM Analysis: An llm_worker pulls a task, downloads the file from Supabase Storage, and sends its content to the Gemini API for analysis.
Save Analysis: The individual analysis is saved as a record in the Supabase analyses table.
Final Report: After all tasks are complete, a final process queries all analyses from Supabase, merges them into a single JSON file, and saves it to both the local output directory and Supabase Storage.
IV. Component-Specific Requirements
Repomix Processing:
Workers: 15 parallel workers.
Command: Must use npx repomix --remote with the include argument: '**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md'.
LLM Analysis:
Model: Gemini 2.0 Flash.
Rate Limit: Target a 3 Million TPM capacity.
Configuration: 90s timeouts and 8192 max output tokens.
Prompt: Must use the comprehensive and exhaustive feature analysis prompt as the default.
V. Logging & Deployment
Logging: The process must be completely logged to a local file. Sentry is used for error tracking and validation.
Deployment: The final application must be containerized (Docker) and deployable to Google Cloud. The local development environment must mirror the cloud architecture.