This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/pipeline.py, src/config.py, src/redis_queue.py, src/supabase_client.py, main.py, service.py, worker.py, docker-compose.local.yml, setup_local_dev.py
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
docker-compose.local.yml
main.py
service.py
setup_local_dev.py
src/config.py
src/pipeline.py
src/redis_queue.py
src/supabase_client.py
worker.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="docker-compose.local.yml">
version: '3.8'

services:
  # Redis for local development
  redis:
    image: redis:7-alpine
    container_name: repo-research-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # Supabase local development stack
  supabase-db:
    image: supabase/postgres:**********
    container_name: repo-research-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./src/database_schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Supabase API (PostgREST)
  supabase-rest:
    image: postgrest/postgrest:v11.2.0
    container_name: repo-research-postgrest
    ports:
      - "3000:3000"
    environment:
      PGRST_DB_URI: *********************************************/postgres
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
    depends_on:
      supabase-db:
        condition: service_healthy

  # Supabase Storage
  supabase-storage:
    image: supabase/storage-api:v0.40.4
    container_name: repo-research-storage
    ports:
      - "5000:5000"
    environment:
      ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      POSTGREST_URL: http://supabase-rest:3000
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      DATABASE_URL: *********************************************/postgres
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
    volumes:
      - storage_data:/var/lib/storage
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-rest:
        condition: service_started

volumes:
  redis_data:
  postgres_data:
  storage_data:
</file>

<file path="setup_local_dev.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Local Development Setup

This script sets up the local development environment with Docker containers
for Redis and Supabase, following the unified architecture.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def run_command(cmd, check=True, capture_output=False):
    """Run a shell command with proper error handling."""
    print(f"Running: {cmd}")
    result = subprocess.run(
        cmd, shell=True, check=check, 
        capture_output=capture_output, text=True
    )
    if capture_output:
        return result.stdout.strip()
    return result.returncode == 0

def check_docker():
    """Check if Docker is available and running."""
    try:
        run_command("docker --version", capture_output=True)
        run_command("docker-compose --version", capture_output=True)
        print("✅ Docker and Docker Compose are available")
        return True
    except subprocess.CalledProcessError:
        print("❌ Docker or Docker Compose not found")
        print("Please install Docker Desktop: https://www.docker.com/products/docker-desktop")
        return False

def setup_environment():
    """Set up the local environment file."""
    env_local = Path(".env.local")
    env_file = Path(".env")
    
    if not env_file.exists():
        if env_local.exists():
            print("📄 Copying .env.local to .env")
            import shutil
            shutil.copy(env_local, env_file)
        else:
            print("❌ .env.local not found. Please create it first.")
            return False
    
    print("✅ Environment file ready")
    return True

def start_services():
    """Start the local development services."""
    print("🚀 Starting local development services...")
    
    # Stop any existing services
    run_command("docker-compose -f docker-compose.local.yml down", check=False)
    
    # Start services
    if not run_command("docker-compose -f docker-compose.local.yml up -d"):
        print("❌ Failed to start services")
        return False
    
    print("⏳ Waiting for services to be ready...")
    time.sleep(10)
    
    return True

def wait_for_services():
    """Wait for all services to be healthy."""
    services = {
        "Redis": ("localhost", 6379),
        "PostgreSQL": ("localhost", 5432),
        "Supabase REST": ("localhost", 3000),
        "Supabase Storage": ("localhost", 5000)
    }
    
    for service_name, (host, port) in services.items():
        print(f"⏳ Waiting for {service_name} on {host}:{port}...")
        
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                if service_name == "Supabase REST":
                    response = requests.get(f"http://{host}:{port}/", timeout=2)
                    if response.status_code in [200, 404]:  # 404 is OK for PostgREST root
                        break
                elif service_name == "Supabase Storage":
                    response = requests.get(f"http://{host}:{port}/status", timeout=2)
                    if response.status_code == 200:
                        break
                else:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2)
                    result = sock.connect_ex((host, port))
                    sock.close()
                    if result == 0:
                        break
            except:
                pass
            
            time.sleep(2)
            if attempt == max_attempts - 1:
                print(f"❌ {service_name} failed to start")
                return False
        
        print(f"✅ {service_name} is ready")
    
    return True

def initialize_database():
    """Initialize the database schema."""
    print("🗄️ Initializing database schema...")
    
    # The schema is automatically loaded via docker-entrypoint-initdb.d
    # But we can verify it worked by checking if tables exist
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="postgres",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
        tables = cursor.fetchall()
        cursor.close()
        conn.close()
        
        if tables:
            print(f"✅ Database initialized with {len(tables)} tables")
            return True
        else:
            print("⚠️ No tables found, schema may not have loaded")
            return False
    except Exception as e:
        print(f"❌ Database initialization check failed: {e}")
        return False

def test_connection():
    """Test the unified pipeline connection."""
    print("🧪 Testing unified pipeline connection...")
    
    try:
        # Add src to path
        sys.path.insert(0, 'src')
        
        from config import ScraperConfig
        from pipeline import RepositoryPipeline
        
        config = ScraperConfig()
        pipeline = RepositoryPipeline(config=config)
        
        print("✅ Unified pipeline initialized successfully")
        print(f"   Redis URL: {config.REDIS_URL}")
        print(f"   Supabase URL: {config.SUPABASE_URL}")
        
        return True
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🔧 Repository Research Tool - Local Development Setup")
    print("=" * 60)
    
    # Check prerequisites
    if not check_docker():
        return 1
    
    # Setup environment
    if not setup_environment():
        return 1
    
    # Start services
    if not start_services():
        return 1
    
    # Wait for services
    if not wait_for_services():
        return 1
    
    # Initialize database
    if not initialize_database():
        print("⚠️ Database initialization had issues, but continuing...")
    
    # Test connection
    if not test_connection():
        return 1
    
    print("\n🎉 Local development environment is ready!")
    print("\nNext steps:")
    print("1. Update .env with your GitHub token and Gemini API key")
    print("2. Run: python main.py --keywords 'test' --max-repos 1 --min-stars 100")
    print("3. Or start the service: python service.py")
    print("\nTo stop services: docker-compose -f docker-compose.local.yml down")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="worker.py">
#!/usr/bin/env python3
"""
Worker service for Repository Research Tool.
Runs distributed workers that process repositories and LLM analysis tasks.
"""

import os
import sys
import time
import signal
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from config import ScraperConfig
from cloud_pipeline import CloudRepositoryPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorkerService:
    """
    Worker service that runs distributed processing workers.
    Processes repositories and LLM analysis tasks from Redis queues.
    """
    
    def __init__(self):
        """Initialize the worker service."""
        self.config = ScraperConfig()
        self.pipeline = None
        self.running = False
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Worker service initialized")
        logger.info(f"Configuration: {self.config.REPOMIX_WORKERS} repomix workers, {self.config.LLM_WORKERS} LLM workers")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    def start(self):
        """Start the worker service."""
        logger.info("🚀 Starting Repository Research Tool Worker Service")
        logger.info(f"   Deployment mode: {self.config.DEPLOYMENT_MODE}")
        logger.info(f"   Redis URL: {self.config.REDIS_URL}")
        logger.info(f"   Workers: {self.config.REPOMIX_WORKERS} repomix + {self.config.LLM_WORKERS} LLM")
        
        try:
            # Initialize cloud pipeline for worker operations
            self.pipeline = CloudRepositoryPipeline(config=self.config)
            
            # Start worker processes
            self.pipeline.start_workers()
            
            self.running = True
            logger.info("✅ Worker service started successfully")
            
            # Keep the service running
            self._run_worker_loop()
            
        except Exception as e:
            logger.error(f"❌ Failed to start worker service: {e}")
            raise
        finally:
            self._shutdown()
    
    def _run_worker_loop(self):
        """Main worker loop that keeps the service running."""
        logger.info("Worker service running... Press Ctrl+C to stop")
        
        try:
            while self.running:
                # Monitor worker health and restart if needed
                self._check_worker_health()
                
                # Sleep for a short interval
                time.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
            self.running = False
    
    def _check_worker_health(self):
        """Check worker process health and restart if needed."""
        if not self.pipeline:
            return
        
        try:
            # Check repomix workers
            dead_repomix = []
            for i, process in enumerate(self.pipeline.repomix_processes):
                if not process.is_alive():
                    dead_repomix.append(i)
            
            # Check LLM workers
            dead_llm = []
            for i, process in enumerate(self.pipeline.llm_processes):
                if not process.is_alive():
                    dead_llm.append(i)
            
            # Log any dead workers
            if dead_repomix:
                logger.warning(f"Dead repomix workers detected: {dead_repomix}")
            if dead_llm:
                logger.warning(f"Dead LLM workers detected: {dead_llm}")
            
            # In a production system, you might restart dead workers here
            # For now, we just log the issue
            
        except Exception as e:
            logger.error(f"Error checking worker health: {e}")
    
    def _shutdown(self):
        """Shutdown the worker service gracefully."""
        logger.info("🛑 Shutting down worker service...")
        
        try:
            if self.pipeline:
                self.pipeline.stop_workers()
                logger.info("✅ All workers stopped")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        logger.info("Worker service shutdown complete")

def main():
    """Main entry point for the worker service."""
    try:
        # Check if we're in cloud mode
        config = ScraperConfig()
        if not config.USE_CLOUD_SERVICES:
            logger.error("❌ Worker service requires cloud services to be enabled")
            logger.error("   Set DEPLOYMENT_MODE=cloud or USE_CLOUD_SERVICES=true")
            return 1
        
        # Start the worker service
        worker_service = WorkerService()
        worker_service.start()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Worker service interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"❌ Worker service failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="src/redis_queue.py">
"""
Redis-based distributed queue implementation.
Replaces in-memory JoinableQueue with Redis-backed queues for cloud deployment.
"""

import json
import time
import redis
import logging
from typing import Any, Dict, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class QueueItem:
    """Base class for queue items with metadata."""
    id: str
    created_at: str
    data: Dict[str, Any]
    retries: int = 0
    max_retries: int = 3

    def to_json(self) -> str:
        """Convert to JSON string for Redis storage."""
        return json.dumps(asdict(self))
    
    @classmethod
    def from_json(cls, json_str: str) -> 'QueueItem':
        """Create from JSON string."""
        data = json.loads(json_str)
        return cls(**data)

class RepositoryQueueItem(QueueItem):
    """Queue item for repository processing tasks."""

    def __init__(self, repository_url: str, repository_name: str, job_id: str,
                 item_id: Optional[str] = None, retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"repo_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "repository_url": repository_url,
                "repository_name": repository_name,
                "job_id": job_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.repository_url = repository_url
        self.repository_name = repository_name
        self.job_id = job_id

class FileQueueItem(QueueItem):
    """Queue item for LLM processing tasks."""

    def __init__(self, file_path: str, repository_name: str, job_id: str,
                 chunk_id: Optional[str] = None, item_id: Optional[str] = None,
                 retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"file_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "file_path": file_path,
                "repository_name": repository_name,
                "job_id": job_id,
                "chunk_id": chunk_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.file_path = file_path
        self.repository_name = repository_name
        self.job_id = job_id
        self.chunk_id = chunk_id

class RedisQueue:
    """Redis-backed distributed queue implementation."""
    
    def __init__(self, redis_url: str, queue_name: str, 
                 processing_timeout: int = 300):
        """
        Initialize Redis queue.
        
        Args:
            redis_url: Redis connection URL
            queue_name: Name of the queue
            processing_timeout: Timeout for processing items (seconds)
        """
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.queue_name = queue_name
        self.processing_queue = f"{queue_name}:processing"
        self.failed_queue = f"{queue_name}:failed"
        self.processing_timeout = processing_timeout
        
        # Test connection
        try:
            self.redis_client.ping()
            logger.info(f"Connected to Redis queue: {queue_name}")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def put(self, item: QueueItem) -> None:
        """Add item to queue."""
        try:
            self.redis_client.lpush(self.queue_name, item.to_json())
            logger.debug(f"Added item {item.id} to queue {self.queue_name}")
        except Exception as e:
            logger.error(f"Failed to add item to queue: {e}")
            raise
    
    def get(self, timeout: Optional[int] = None) -> Optional[QueueItem]:
        """
        Get item from queue (blocking).
        
        Args:
            timeout: Timeout in seconds (None for indefinite)
            
        Returns:
            QueueItem or None if timeout
        """
        try:
            # Use BRPOPLPUSH for atomic move to processing queue
            result = self.redis_client.brpoplpush(
                self.queue_name, 
                self.processing_queue, 
                timeout=timeout or 0
            )
            
            if result:
                item = QueueItem.from_json(result)
                # Add processing timestamp
                self.redis_client.hset(
                    f"{self.processing_queue}:meta:{item.id}",
                    "started_at", 
                    datetime.now().isoformat()
                )
                logger.debug(f"Retrieved item {item.id} from queue {self.queue_name}")
                return item
            return None
            
        except Exception as e:
            logger.error(f"Failed to get item from queue: {e}")
            raise
    
    def task_done(self, item: QueueItem) -> None:
        """Mark task as completed."""
        try:
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            # Remove metadata
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            logger.debug(f"Marked item {item.id} as done")
        except Exception as e:
            logger.error(f"Failed to mark task as done: {e}")
            raise
    
    def task_failed(self, item: QueueItem, error: str) -> None:
        """Mark task as failed and handle retry logic."""
        try:
            item.retries += 1
            
            if item.retries < item.max_retries:
                # Retry: put back in main queue
                self.redis_client.lpush(self.queue_name, item.to_json())
                logger.warning(f"Retrying item {item.id} (attempt {item.retries})")
            else:
                # Max retries reached: move to failed queue
                failed_item = {
                    "item": asdict(item),
                    "error": error,
                    "failed_at": datetime.now().isoformat()
                }
                self.redis_client.lpush(self.failed_queue, json.dumps(failed_item))
                logger.error(f"Item {item.id} failed permanently: {error}")
            
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            
        except Exception as e:
            logger.error(f"Failed to handle task failure: {e}")
            raise
    
    def size(self) -> int:
        """Get queue size."""
        return self.redis_client.llen(self.queue_name)
    
    def processing_size(self) -> int:
        """Get processing queue size."""
        return self.redis_client.llen(self.processing_queue)
    
    def failed_size(self) -> int:
        """Get failed queue size."""
        return self.redis_client.llen(self.failed_queue)
    
    def cleanup_stale_items(self) -> int:
        """Clean up items that have been processing too long."""
        try:
            stale_count = 0
            processing_items = self.redis_client.lrange(self.processing_queue, 0, -1)
            
            for item_json in processing_items:
                item = QueueItem.from_json(item_json)
                meta_key = f"{self.processing_queue}:meta:{item.id}"
                started_at_str = self.redis_client.hget(meta_key, "started_at")
                
                if started_at_str:
                    started_at = datetime.fromisoformat(started_at_str)
                    if datetime.now() - started_at > timedelta(seconds=self.processing_timeout):
                        # Item is stale, move back to main queue
                        self.redis_client.lrem(self.processing_queue, 1, item_json)
                        self.redis_client.delete(meta_key)
                        self.redis_client.lpush(self.queue_name, item_json)
                        stale_count += 1
                        logger.warning(f"Moved stale item {item.id} back to queue")
            
            return stale_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup stale items: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get queue statistics."""
        return {
            "pending": self.size(),
            "processing": self.processing_size(),
            "failed": self.failed_size()
        }

class QueueManager:
    """Manages multiple Redis queues for the application."""
    
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.queues: Dict[str, RedisQueue] = {}
        # Create a shared Redis client for rate limiting and other operations
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
    
    def get_queue(self, queue_name: str) -> RedisQueue:
        """Get or create a queue."""
        if queue_name not in self.queues:
            self.queues[queue_name] = RedisQueue(self.redis_url, queue_name)
        return self.queues[queue_name]
    
    def get_repository_queue(self) -> RedisQueue:
        """Get the repository processing queue."""
        return self.get_queue("repositories")
    
    def get_file_queue(self) -> RedisQueue:
        """Get the file processing queue."""
        return self.get_queue("files")
    
    def cleanup_all_stale_items(self) -> Dict[str, int]:
        """Cleanup stale items in all queues."""
        results = {}
        for name, queue in self.queues.items():
            results[name] = queue.cleanup_stale_items()
        return results
    
    def get_all_stats(self) -> Dict[str, Dict[str, int]]:
        """Get statistics for all queues."""
        stats = {}
        for name, queue in self.queues.items():
            stats[name] = queue.get_stats()
        return stats
</file>

<file path="src/supabase_client.py">
"""
Supabase client integration for database and storage operations.
Handles PostgreSQL database operations and S3-compatible storage.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from supabase import create_client, Client
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class JobRecord:
    """Database record for analysis jobs."""
    id: str
    keywords: str
    min_stars: int
    max_repos: int
    status: str  # pending, running, completed, failed
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    error_message: Optional[str] = None
    analysis_file_url: Optional[str] = None
    total_repositories: Optional[int] = None
    custom_prompt: Optional[str] = None
    debug: bool = False

@dataclass
class RepositoryRecord:
    """Database record for repository information."""
    id: str
    job_id: str
    name: str
    url: str
    stars: int
    file_size_mb: float
    repomix_file_url: Optional[str] = None
    processed_at: Optional[str] = None
    analysis_status: str = "pending"  # pending, processing, completed, failed
    error_message: Optional[str] = None
    created_at: Optional[str] = None

@dataclass
class AnalysisRecord:
    """Database record for analysis results."""
    id: str
    repository_id: str
    job_id: str
    worker_id: str
    processed_at: str
    analysis_content: str
    analysis_length: int
    chunk_id: Optional[str] = None

class SupabaseClient:
    """Supabase client for database and storage operations."""
    
    def __init__(self, url: str, key: str):
        """
        Initialize Supabase client.
        
        Args:
            url: Supabase project URL
            key: Supabase API key (anon or service role)
        """
        self.client: Client = create_client(url, key)
        self.url = url
        self.key = key
        
        # Test connection
        try:
            # Simple query to test connection
            result = self.client.table('jobs').select('id').limit(1).execute()
            logger.info("Connected to Supabase successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Supabase: {e}")
            raise
    
    # Job Management
    def create_job(self, job: JobRecord) -> JobRecord:
        """Create a new analysis job."""
        try:
            result = self.client.table('jobs').insert(asdict(job)).execute()
            logger.info(f"Created job {job.id}")
            return JobRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def get_job(self, job_id: str) -> Optional[JobRecord]:
        """Get job by ID."""
        try:
            result = self.client.table('jobs').select('*').eq('id', job_id).execute()
            if result.data:
                return JobRecord(**result.data[0])
            return None
        except Exception as e:
            logger.error(f"Failed to get job {job_id}: {e}")
            raise
    
    def update_job_status(self, job_id: str, status: str, 
                         error_message: Optional[str] = None,
                         analysis_file_url: Optional[str] = None,
                         total_repositories: Optional[int] = None) -> None:
        """Update job status and related fields."""
        try:
            update_data = {"status": status}
            
            if status == "running" and not self.get_job(job_id).started_at:
                update_data["started_at"] = datetime.now().isoformat()
            elif status in ["completed", "failed"]:
                update_data["completed_at"] = datetime.now().isoformat()
            
            if error_message:
                update_data["error_message"] = error_message
            if analysis_file_url:
                update_data["analysis_file_url"] = analysis_file_url
            if total_repositories is not None:
                update_data["total_repositories"] = total_repositories
            
            self.client.table('jobs').update(update_data).eq('id', job_id).execute()
            logger.debug(f"Updated job {job_id} status to {status}")
            
        except Exception as e:
            logger.error(f"Failed to update job status: {e}")
            raise
    
    def list_jobs(self, limit: int = 50, offset: int = 0) -> List[JobRecord]:
        """List jobs with pagination."""
        try:
            result = self.client.table('jobs')\
                .select('*')\
                .order('created_at', desc=True)\
                .range(offset, offset + limit - 1)\
                .execute()
            
            return [JobRecord(**job) for job in result.data]
        except Exception as e:
            logger.error(f"Failed to list jobs: {e}")
            raise
    
    # Repository Management
    def create_repository(self, repo: RepositoryRecord) -> RepositoryRecord:
        """Create a repository record."""
        try:
            result = self.client.table('repositories').insert(asdict(repo)).execute()
            logger.debug(f"Created repository record {repo.id}")
            return RepositoryRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create repository: {e}")
            raise
    
    def update_repository_status(self, repo_id: str, status: str,
                               repomix_file_url: Optional[str] = None,
                               error_message: Optional[str] = None) -> None:
        """Update repository processing status."""
        try:
            update_data = {
                "analysis_status": status,
                "processed_at": datetime.now().isoformat()
            }
            
            if repomix_file_url:
                update_data["repomix_file_url"] = repomix_file_url
            if error_message:
                update_data["error_message"] = error_message
            
            self.client.table('repositories').update(update_data).eq('id', repo_id).execute()
            logger.debug(f"Updated repository {repo_id} status to {status}")
            
        except Exception as e:
            logger.error(f"Failed to update repository status: {e}")
            raise
    
    def get_repositories_by_job(self, job_id: str) -> List[RepositoryRecord]:
        """Get all repositories for a job."""
        try:
            result = self.client.table('repositories')\
                .select('*')\
                .eq('job_id', job_id)\
                .order('created_at')\
                .execute()
            
            return [RepositoryRecord(**repo) for repo in result.data]
        except Exception as e:
            logger.error(f"Failed to get repositories for job {job_id}: {e}")
            raise
    
    # Analysis Management
    def create_analysis(self, analysis: AnalysisRecord) -> AnalysisRecord:
        """Create an analysis record."""
        try:
            result = self.client.table('analyses').insert(asdict(analysis)).execute()
            logger.debug(f"Created analysis record {analysis.id}")
            return AnalysisRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create analysis: {e}")
            raise
    
    def get_analyses_by_job(self, job_id: str) -> List[AnalysisRecord]:
        """Get all analyses for a job."""
        try:
            result = self.client.table('analyses')\
                .select('*')\
                .eq('job_id', job_id)\
                .order('processed_at')\
                .execute()
            
            return [AnalysisRecord(**analysis) for analysis in result.data]
        except Exception as e:
            logger.error(f"Failed to get analyses for job {job_id}: {e}")
            raise
    
    # Storage Operations
    def upload_file(self, bucket: str, file_path: str, file_content: bytes,
                   content_type: str = "application/octet-stream") -> str:
        """
        Upload file to Supabase storage.
        
        Returns:
            Public URL of uploaded file
        """
        try:
            # Upload file
            result = self.client.storage.from_(bucket).upload(
                file_path, 
                file_content,
                file_options={"content-type": content_type}
            )
            
            if result.error:
                raise Exception(f"Upload failed: {result.error}")
            
            # Get public URL
            public_url = self.client.storage.from_(bucket).get_public_url(file_path)
            logger.debug(f"Uploaded file to {public_url}")
            return public_url
            
        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            raise
    
    def download_file(self, bucket: str, file_path: str) -> bytes:
        """Download file from Supabase storage."""
        try:
            result = self.client.storage.from_(bucket).download(file_path)
            if result.error:
                raise Exception(f"Download failed: {result.error}")
            return result.data
        except Exception as e:
            logger.error(f"Failed to download file {file_path}: {e}")
            raise
    
    def delete_file(self, bucket: str, file_path: str) -> None:
        """Delete file from Supabase storage."""
        try:
            result = self.client.storage.from_(bucket).remove([file_path])
            if result.error:
                raise Exception(f"Delete failed: {result.error}")
            logger.debug(f"Deleted file {file_path}")
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            raise
    
    def list_files(self, bucket: str, folder: str = "") -> List[Dict[str, Any]]:
        """List files in a bucket/folder."""
        try:
            result = self.client.storage.from_(bucket).list(folder)
            if result.error:
                raise Exception(f"List failed: {result.error}")
            return result.data
        except Exception as e:
            logger.error(f"Failed to list files in {bucket}/{folder}: {e}")
            raise

def create_supabase_client() -> SupabaseClient:
    """Create Supabase client from environment variables."""
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")
    
    return SupabaseClient(url, key)
</file>

<file path="main.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Main Entry Point

Usage:
    python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
    python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze this code for AI patterns"
"""

import argparse
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_custom_prompt_template():
    """Create a template for custom prompts with placeholders"""
    return """CUSTOM REPOSITORY ANALYSIS

Repository: {repo_name}
File size: {file_size_mb:.2f}MB

[Your custom analysis instructions here]

Code content:
{content}
"""

def main():
    """Main entry point for the repository research tool"""
    parser = argparse.ArgumentParser(
        description="Repository Research Tool - Analyze GitHub repositories with AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
  python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze for AI patterns"
  python main.py --keywords "web frameworks" --min-stars 100 --max-repos 5 --debug

Custom Prompt Template:
  Use {repo_name}, {file_size_mb}, and {content} as placeholders in your custom prompt.
        """
    )
    
    # Required arguments
    parser.add_argument(
        '--keywords',
        required=True,
        help='Comma-separated list of keywords to search for (e.g., "cursor rules,code prompts")'
    )
    
    parser.add_argument(
        '--min-stars',
        type=int,
        default=30,
        help='Minimum number of stars for repositories (default: 30)'
    )
    
    parser.add_argument(
        '--max-repos',
        type=int,
        default=15,
        help='Maximum number of repositories per keyword (default: 15)'
    )
    
    # Optional arguments
    parser.add_argument(
        '--custom-prompt',
        help='Custom LLM analysis prompt. Use {repo_name}, {file_size_mb}, and {content} as placeholders.'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode with verbose logging'
    )
    
    parser.add_argument(
        '--output-dir',
        help='Custom output directory name (default: auto-generated timestamp)'
    )
    
    args = parser.parse_args()
    
    # Parse keywords
    keywords = [k.strip() for k in args.keywords.split(',')]
    
    print("REPOSITORY RESEARCH TOOL")
    print("=" * 60)
    print(f"Configuration:")
    print(f"   Keywords: {keywords}")
    print(f"   Min stars: {args.min_stars}")
    print(f"   Max repos per keyword: {args.max_repos}")
    print(f"   Expected total repos: ~{len(keywords) * args.max_repos}")
    print(f"   Custom prompt: {'Yes' if args.custom_prompt else 'No (using default)'}")
    print(f"   Debug mode: {'Yes' if args.debug else 'No'}")
    
    try:
        # Import and configure
        from config import ScraperConfig
        from pipeline import RepositoryPipeline
        from github_search import GitHubSearcher
        from sentry_config import init_sentry

        # Initialize Sentry monitoring
        init_sentry()

        # Load configuration
        config = ScraperConfig()
        
        # Set custom prompt if provided
        if args.custom_prompt:
            config.CUSTOM_LLM_PROMPT = args.custom_prompt
            print(f"Custom prompt configured")
        
        # Validate API key
        if not config.LLM_API_KEY:
            print("Error: LLM_API_KEY not configured in .env file")
            print("   Please add your Gemini API key to .env file")
            return 1

        print(f"Configuration loaded:")
        print(f"   API key: Configured")
        print(f"   Repomix workers: {config.REPOMIX_WORKERS}")
        print(f"   LLM workers: {config.LLM_WORKERS}")
        
        # Search for repositories
        print(f"\nSearching for repositories (sorted by last updated)...")
        searcher = GitHubSearcher(config)
        
        all_repositories = []
        for keyword in keywords:
            print(f"   Searching for: '{keyword}'")
            repos = searcher.search_repositories(
                keyword, 
                min_stars=args.min_stars, 
                max_repos=args.max_repos
            )
            print(f"   Found {len(repos)} repositories for '{keyword}'")
            
            # Show first few repos
            for i, repo in enumerate(repos[:3]):
                updated_at = repo.get('updated_at', 'unknown')
                print(f"      {i+1}. {repo.get('full_name', 'unknown')} ({repo.get('stargazers_count', 0)} stars, updated: {updated_at[:10]})")
            
            if len(repos) > 3:
                print(f"      ... and {len(repos) - 3} more repositories")
            
            all_repositories.extend(repos)
        
        print(f"\nTotal repositories to process: {len(all_repositories)}")

        if len(all_repositories) == 0:
            print("No repositories found - check keywords and min-stars filter")
            return 1

        # Initialize pipeline
        print(f"\nInitializing Pipeline...")
        pipeline = RepositoryPipeline(config=config)
        
        print(f"Pipeline initialized")
        print(f"   Output directory: {pipeline.output_dir}")
        
        # Run the pipeline
        print(f"\nRunning Complete Pipeline...")
        print(f"   Processing {len(all_repositories)} repositories")
        print(f"   Using {config.REPOMIX_WORKERS} repomix workers")
        print(f"   Using {config.LLM_WORKERS} LLM workers")
        print(f"   Expected time: ~{len(all_repositories) * 1.5} minutes")
        print("=" * 60)

        # Run the pipeline
        keywords_str = ','.join(keywords)
        pipeline.run(
            repositories=all_repositories,
            keywords=keywords_str,
            min_stars=args.min_stars,
            max_repos=args.max_repos,
            custom_prompt=args.custom_prompt
        )

        print("=" * 60)
        print(f"ANALYSIS COMPLETED!")
        
        # Show results
        analysis_file = pipeline.output_dir / "analysis.json"
        if analysis_file.exists():
            file_size = analysis_file.stat().st_size
            print(f"Results saved to: {pipeline.output_dir}")
            print(f"   Main analysis: analysis.json ({file_size:,} bytes)")
            print(f"   Repomix files: repomixes/ directory")
            print(f"   Error tracking: sentry_analysis.json")
        else:
            print(f"Analysis file not found - check for errors")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\nAnalysis interrupted by user")
        return 0

    except Exception as e:
        print(f"\nAnalysis failed: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="src/config.py">
import os
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    # Look for .env file in project root
    env_path = Path(__file__).parent.parent / '.env'
    load_dotenv(env_path)
    print(f"Loaded environment variables from {env_path}")
except ImportError:
    print("python-dotenv not installed, using system environment variables")
except Exception as e:
    print(f"Could not load .env file: {e}")

class ScraperConfig:
    # GitHub Configuration
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
    MAX_REPOS_PER_KEYWORD = int(os.getenv('MAX_REPOS_PER_KEYWORD', '20'))
    MIN_STARS = int(os.getenv('MIN_STARS', '30'))

    # Repomix Configuration
    REPOMIX_WORKERS = int(os.getenv('REPOMIX_WORKERS', '15'))
    FILE_INCLUDES = "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"

    # Chunking Configuration
    MAX_FILE_SIZE_MB = 3
    MAX_CHUNKS_TO_RETAIN = 3

    # LLM Configuration
    LLM_API_KEY = os.getenv('LLM_API_KEY')
    LLM_BASE_URL = os.getenv('LLM_BASE_URL', 'https://generativelanguage.googleapis.com/v1beta')
    LLM_MODEL = os.getenv('LLM_MODEL', 'gemini-2.0-flash')
    LLM_WORKERS = int(os.getenv('LLM_WORKERS', '4'))
    LLM_RATE_LIMIT_MB_PER_MIN = 12

    # Output Configuration
    OUTPUT_BASE = os.getenv('OUTPUT_BASE', 'output')

    # Cloud Configuration - Always use cloud services (Redis + Supabase)
    # Local development uses Docker containers, production uses managed services

    # Redis Configuration
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    REDIS_QUEUE_TIMEOUT = int(os.getenv('REDIS_QUEUE_TIMEOUT', '300'))

    # Supabase Configuration
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY')  # Service role key
    SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')  # Anonymous key

    # Storage Configuration
    STORAGE_BUCKET_REPOMIXES = os.getenv('STORAGE_BUCKET_REPOMIXES', 'repomixes')
    STORAGE_BUCKET_ANALYSES = os.getenv('STORAGE_BUCKET_ANALYSES', 'analyses')
    STORAGE_BUCKET_LOGS = os.getenv('STORAGE_BUCKET_LOGS', 'logs')

    # Enhanced LLM Configuration
    LLM_TIMEOUT = int(os.getenv('LLM_TIMEOUT', '90'))
    LLM_MAX_OUTPUT_TOKENS = int(os.getenv('LLM_MAX_OUTPUT_TOKENS', '8192'))

    # Custom prompt override
    CUSTOM_LLM_PROMPT = os.getenv('CUSTOM_LLM_PROMPT')

    # Default comprehensive analysis prompt
    DEFAULT_LLM_PROMPT = """Analyze this repository comprehensively and provide a detailed summary covering:

1. **Main Purpose and Functionality**: What does this repository do? What problem does it solve?

2. **Key Technologies and Frameworks Used**: List and briefly describe the main technologies, frameworks, libraries, and tools used.

3. **Architecture Overview**: Describe the overall architecture, design patterns, and structure of the codebase.

4. **Notable Features or Patterns**: Highlight interesting implementation details, design patterns, or unique approaches used.

5. **Potential Use Cases**: Describe scenarios where this repository could be useful or applied.

Please be comprehensive and specific, focusing on the actual implementation details and functionality rather than generic descriptions."""
</file>

<file path="src/pipeline.py">
"""
Cloud-integrated pipeline for Repository Research Tool.
Uses Redis queues, Supabase database, and cloud storage.
"""

import os
import uuid
import time
import json
import logging
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any

from config import ScraperConfig
from redis_queue import QueueManager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord, AnalysisRecord
from storage_manager import create_storage_manager
from monitoring import RepositoryMonitor
from llm_rate_limiter import LLMRateLimiter

# Standalone worker functions that initialize their own resources

def repomix_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone repomix worker function that initializes its own resources."""
    import subprocess
    import shutil
    import tempfile
    from pathlib import Path

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from redis_queue import QueueManager, FileQueueItem
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client

    config = ScraperConfig()  # Loads from .env
    queue_manager = QueueManager(config.REDIS_URL)
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    logger = logging.getLogger(f'repomix_worker_{worker_id}')
    logger.info(f"Repomix Worker {worker_id} for job {job_id} started")

    repo_queue = queue_manager.get_repository_queue()
    file_queue = queue_manager.get_file_queue()

    while True:
        try:
            # Get repository from Redis queue
            repo_item = repo_queue.get(timeout=30)
            if repo_item is None:
                break

            repo_name = repo_item.data['repository_name']
            repo_url = repo_item.data['repository_url']
            logger.info(f"Repomix Worker {worker_id}: Starting {repo_name}")

            # Create a temporary local file for repomix output
            temp_dir = Path(tempfile.gettempdir()) / job_id
            temp_dir.mkdir(parents=True, exist_ok=True)
            safe_repo_name = repo_name.replace('/', '_')
            local_output_file = temp_dir / f"{safe_repo_name}.md"

            # Run the actual repomix command
            npx_path = shutil.which('npx')
            if not npx_path:
                raise RuntimeError("npx not found in PATH")

            cmd = [
                npx_path, 'repomix', '--remote', repo_url,
                '--include', config.FILE_INCLUDES,
                '--output', str(local_output_file)
            ]
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=600,
                encoding='utf-8', errors='replace'
            )

            if result.returncode != 0 or not local_output_file.exists():
                logger.error(f"Repomix Worker {worker_id}: Failed {repo_name} - {result.stderr[:200]}")
                # Clean up and continue
                if local_output_file.exists():
                    local_output_file.unlink()
                repo_queue.task_done(repo_item)
                continue

            # Read content from successful repomix run
            with open(local_output_file, 'r', encoding='utf-8') as f:
                repomix_content = f.read()

            # Upload to cloud storage
            repomix_url = storage_manager.upload_repomix_file(
                job_id, safe_repo_name, repomix_content
            )

            # Update repository record in database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if repo_record:
                supabase_client.update_repository_status(
                    repo_record.id,
                    "completed",
                    repomix_file_url=repomix_url
                )

            # Add to LLM queue
            file_item = FileQueueItem(
                file_path=repomix_url,
                repository_name=repo_name,
                job_id=job_id
            )
            file_queue.put(file_item)

            # Clean up temporary file
            local_output_file.unlink()
            repo_queue.task_done(repo_item)

            logger.info(f"Repomix Worker {worker_id}: Completed {repo_name}")

        except Exception as e:
            logger.error(f"Repomix Worker {worker_id}: Error processing repository: {e}")
            if 'repo_item' in locals():
                repo_queue.task_done(repo_item)
            break


def llm_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone LLM worker function that initializes its own resources."""
    import requests
    import uuid
    from datetime import datetime

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from redis_queue import QueueManager
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client, AnalysisRecord
    from llm_rate_limiter import LLMRateLimiter

    config = ScraperConfig()  # Loads from .env
    queue_manager = QueueManager(config.REDIS_URL)
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    import redis
    redis_client = redis.from_url(config.REDIS_URL)
    rate_limiter = LLMRateLimiter(redis_client=redis_client)

    logger = logging.getLogger(f'llm_worker_{worker_id}')
    logger.info(f"LLM Worker {worker_id} for job {job_id} started")

    file_queue = queue_manager.get_file_queue()

    def build_analysis_prompt(repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(config, 'CUSTOM_PROMPT') and config.CUSTOM_PROMPT:
            return config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    while True:
        try:
            # Get file from Redis queue
            file_item = file_queue.get(timeout=30)
            if file_item is None:
                break

            repo_name = file_item.data['repository_name']
            file_url = file_item.data['file_path']
            logger.info(f"LLM Worker {worker_id}: Starting analysis of {repo_name}")

            # Download repomix content from cloud storage
            repomix_content = storage_manager.download_repomix_file(file_url)
            file_size_mb = len(repomix_content.encode('utf-8')) / (1024 * 1024)

            # Wait for rate limit slot
            if not rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300):
                logger.warning(f"LLM Worker {worker_id} timed out waiting for rate limit slot")
                continue

            # Prepare and make the actual Gemini API call
            prompt = build_analysis_prompt(repo_name, repomix_content)

            data = {
                'contents': [{'parts': [{'text': prompt}]}],
                'generationConfig': {
                    'maxOutputTokens': config.LLM_MAX_OUTPUT_TOKENS,
                    'temperature': 0.1
                }
            }

            response = requests.post(
                f"{config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent?key={config.LLM_API_KEY}",
                json=data,
                timeout=config.LLM_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(f"LLM Worker {worker_id}: Failed analysis for {repo_name} - API Status: {response.status_code}")
                file_queue.task_done(file_item)
                continue

            result = response.json()
            analysis_content = result['candidates'][0]['content']['parts'][0]['text']

            # Find the corresponding repository_id from the database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if not repo_record:
                logger.error(f"LLM Worker {worker_id}: Could not find repository record for {repo_name}")
                file_queue.task_done(file_item)
                continue

            # Create analysis record
            analysis_record = AnalysisRecord(
                id=str(uuid.uuid4()),
                repository_id=repo_record.id,
                job_id=job_id,
                worker_id=str(worker_id),
                processed_at=datetime.now().isoformat(),
                analysis_content=analysis_content,
                analysis_length=len(analysis_content)
            )

            supabase_client.create_analysis(analysis_record)
            file_queue.task_done(file_item)

            logger.info(f"LLM Worker {worker_id}: Completed analysis for {repo_name}")

        except Exception as e:
            logger.error(f"LLM Worker {worker_id}: Error processing file: {e}")
            if 'file_item' in locals():
                file_queue.task_done(file_item)
            break


logger = logging.getLogger(__name__)

class RepositoryPipeline:
    """
    Cloud-native pipeline for repository processing.
    Uses Redis for distributed queues, Supabase for persistence, and cloud storage.
    """
    
    def __init__(self, config=None, job_id=None):
        """
        Initialize the cloud pipeline.
        
        Args:
            config: Configuration object (defaults to ScraperConfig)
            job_id: Unique job identifier (generated if not provided)
        """
        self.config = config or ScraperConfig()
        self.job_id = job_id or str(uuid.uuid4())
        
        # Initialize cloud services
        self._initialize_cloud_services()
        
        # Initialize local components
        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None
        
        # Create local output directory for temporary files
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
        self.output_dir = Path(self.config.OUTPUT_BASE) / f"cloud_{timestamp_str}"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
        
        logger.info(f"Initialized cloud pipeline for job {self.job_id}")
    
    def _initialize_cloud_services(self):
        """Initialize cloud service connections."""
        try:
            # Initialize queue manager
            self.queue_manager = QueueManager(self.config.REDIS_URL)
            self.repo_queue = self.queue_manager.get_repository_queue()
            self.file_queue = self.queue_manager.get_file_queue()

            # Initialize distributed rate limiter with Redis client
            self.rate_limiter = LLMRateLimiter(
                redis_client=self.queue_manager.redis_client,
                rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN,
                rate_limit_requests_per_min=getattr(self.config, 'LLM_RATE_LIMIT_REQUESTS_PER_MIN', 1000)
            )

            # Initialize Supabase client
            self.supabase_client = create_supabase_client()

            # Initialize storage manager
            self.storage_manager = create_storage_manager()
            
            logger.info("Cloud services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cloud services: {e}")
            raise
    
    def create_job(self, keywords: str, min_stars: int, max_repos: int,
                   custom_prompt: Optional[str] = None, debug: bool = False) -> JobRecord:
        """
        Create a new analysis job in the database.
        
        Args:
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            custom_prompt: Optional custom LLM prompt
            debug: Debug mode flag
            
        Returns:
            Created job record
        """
        try:
            job = JobRecord(
                id=self.job_id,
                keywords=keywords,
                min_stars=min_stars,
                max_repos=max_repos,
                status="pending",
                created_at=datetime.now().isoformat(),
                custom_prompt=custom_prompt,
                debug=debug
            )
            
            created_job = self.supabase_client.create_job(job)
            logger.info(f"Created job {self.job_id} in database")
            return created_job
            
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def add_repositories(self, repositories: List[Dict[str, Any]]) -> int:
        """
        Add repositories to the processing queue and database.
        
        Args:
            repositories: List of repository information dictionaries
            
        Returns:
            Number of repositories added
        """
        try:
            added_count = 0
            
            for repo_info in repositories:
                # Create repository record in database
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=self.job_id,
                    name=repo_info.get('full_name', 'unknown'),
                    url=repo_info.get('html_url', ''),
                    stars=repo_info.get('stargazers_count', 0),
                    file_size_mb=0.0  # Will be updated after repomix processing
                )
                
                self.supabase_client.create_repository(repo_record)
                
                # Add to Redis queue
                queue_item = RepositoryQueueItem(
                    repository_url=repo_info.get('html_url', ''),
                    repository_name=repo_info.get('full_name', 'unknown'),
                    job_id=self.job_id
                )
                
                self.repo_queue.put(queue_item)
                added_count += 1
            
            # Update job with total repositories
            self.supabase_client.update_job_status(
                self.job_id, 
                "running", 
                total_repositories=added_count
            )
            
            logger.info(f"Added {added_count} repositories to queue and database")
            return added_count
            
        except Exception as e:
            logger.error(f"Failed to add repositories: {e}")
            raise
    
    def start_workers(self):
        """Start repomix and LLM worker processes."""
        try:
            # Convert config to dict for passing to workers
            config_dict = {
                'REDIS_URL': self.config.REDIS_URL,
                'SUPABASE_URL': self.config.SUPABASE_URL,
                'SUPABASE_KEY': self.config.SUPABASE_KEY,
                'LLM_API_KEY': self.config.LLM_API_KEY,
                'LLM_BASE_URL': self.config.LLM_BASE_URL,
                'LLM_MODEL': self.config.LLM_MODEL,
                'FILE_INCLUDES': self.config.FILE_INCLUDES
            }

            # Start repomix workers
            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=repomix_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.repomix_processes.append(p)

            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=llm_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.llm_processes.append(p)

            logger.info(f"Started {len(self.repomix_processes)} repomix and {len(self.llm_processes)} LLM workers")

        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            raise

    def wait_for_completion(self, timeout: Optional[int] = None):
        """
        Cloud-integrated LLM worker.
        
        Args:
            worker_id: Worker identifier
        """
        logger.info(f"Starting cloud LLM worker {worker_id}")
        
        while True:
            try:
                # Get file from queue
                file_item = self.file_queue.get(timeout=30)
                if file_item is None:
                    break
                
                logger.info(f"LLM worker {worker_id} processing {file_item.repository_name}")
                
                # Download file content
                repomix_content = self.storage_manager.download_repomix_file(file_item.file_path)

                # Calculate file size for rate limiting
                file_size_mb = len(repomix_content.encode('utf-8')) / (1024 * 1024)

                # Wait for rate limit slot
                if not self.rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300):
                    logger.warning(f"LLM worker {worker_id} timed out waiting for rate limit slot")
                    continue

                logger.info(f"LLM worker {worker_id} acquired rate limit slot for {file_item.repository_name} ({file_size_mb:.2f}MB)")

                # Process with LLM - REAL IMPLEMENTATION
                import requests

                # Prepare and make the actual Gemini API call
                prompt = self._build_analysis_prompt(file_item.repository_name, repomix_content)

                data = {
                    'contents': [{'parts': [{'text': prompt}]}],
                    'generationConfig': {
                        'maxOutputTokens': self.config.LLM_MAX_OUTPUT_TOKENS,
                        'temperature': 0.1
                    }
                }

                response = requests.post(
                    f"{self.config.LLM_BASE_URL}/models/{self.config.LLM_MODEL}:generateContent?key={self.config.LLM_API_KEY}",
                    json=data,
                    timeout=self.config.LLM_TIMEOUT
                )

                if response.status_code != 200:
                    logger.error(f"LLM Worker {worker_id}: ❌ Failed analysis for {file_item.repository_name} - API Status: {response.status_code}")
                    self.file_queue.task_done(file_item)
                    continue

                result = response.json()
                analysis_content = result['candidates'][0]['content']['parts'][0]['text']

                # Find the corresponding repository_id from the database
                repositories = self.supabase_client.get_repositories_by_job(self.job_id)
                repo_record = next((r for r in repositories if r.name == file_item.repository_name), None)

                if not repo_record:
                    logger.error(f"LLM Worker {worker_id}: Could not find repository record for {file_item.repository_name}")
                    self.file_queue.task_done(file_item)
                    continue

                # Create analysis record
                analysis_record = AnalysisRecord(
                    id=str(uuid.uuid4()),
                    repository_id=repo_record.id,
                    job_id=self.job_id,
                    worker_id=str(worker_id),
                    processed_at=datetime.now().isoformat(),
                    analysis_content=analysis_content,
                    analysis_length=len(analysis_content)
                )
                
                self.supabase_client.create_analysis(analysis_record)
                
                # Mark task as done
                self.file_queue.task_done(file_item)
                
                logger.info(f"LLM worker {worker_id} completed {file_item.repository_name}")
                
            except Exception as e:
                logger.error(f"LLM worker {worker_id} error: {e}")
                if 'file_item' in locals():
                    self.file_queue.task_failed(file_item, str(e))
                break
    
    def wait_for_completion(self, timeout: Optional[int] = None):
        """
        Wait for all processing to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
        """
        try:
            start_time = time.time()
            
            while True:
                # Check queue statistics
                stats = self.queue_manager.get_all_stats()
                repo_pending = stats.get('repositories', {}).get('pending', 0)
                file_pending = stats.get('files', {}).get('pending', 0)
                
                if repo_pending == 0 and file_pending == 0:
                    logger.info("All processing completed")
                    break
                
                if timeout and (time.time() - start_time) > timeout:
                    logger.warning(f"Timeout reached after {timeout} seconds")
                    break
                
                logger.info(f"Waiting... Repos: {repo_pending}, Files: {file_pending}")
                time.sleep(10)
            
        except Exception as e:
            logger.error(f"Error waiting for completion: {e}")
            raise
    
    def finalize_job(self) -> str:
        """
        Finalize the job by creating the final analysis file.
        
        Returns:
            URL of the final analysis file
        """
        try:
            # Get all analyses for this job
            analyses = self.supabase_client.get_analyses_by_job(self.job_id)
            repositories = self.supabase_client.get_repositories_by_job(self.job_id)
            
            # Create final analysis data
            analysis_data = {
                "metadata": {
                    "job_id": self.job_id,
                    "generated": datetime.now().isoformat(),
                    "total_repositories": len(repositories),
                    "total_analyses": len(analyses),
                    "processing_configuration": {
                        "repomix_workers": self.config.REPOMIX_WORKERS,
                        "llm_workers": self.config.LLM_WORKERS,
                        "deployment_mode": self.config.DEPLOYMENT_MODE
                    }
                },
                "analyses": []
            }
            
            # Add analyses to final data
            for analysis in analyses:
                repo = next((r for r in repositories if r.id == analysis.repository_id), None)
                if repo:
                    analysis_data["analyses"].append({
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url
                        },
                        "processing": {
                            "worker_id": analysis.worker_id,
                            "processed_at": analysis.processed_at,
                            "analysis_length": analysis.analysis_length
                        },
                        "analysis": {
                            "content": analysis.analysis_content
                        }
                    })
            
            # Upload final analysis file
            analysis_url = self.storage_manager.upload_analysis_file(
                job_id=self.job_id,
                analysis_data=analysis_data,
                filename="final_analysis.json"
            )
            
            # Update job status
            self.supabase_client.update_job_status(
                self.job_id,
                "completed",
                analysis_file_url=analysis_url
            )
            
            logger.info(f"Job {self.job_id} finalized with analysis file: {analysis_url}")
            return analysis_url
            
        except Exception as e:
            logger.error(f"Failed to finalize job: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise

    def _build_analysis_prompt(self, repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(self.config, 'CUSTOM_PROMPT') and self.config.CUSTOM_PROMPT:
            return self.config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    def stop_workers(self):
        """Stop all worker processes."""
        try:
            # Send poison pills to stop workers
            for _ in range(len(self.repomix_processes)):
                self.repo_queue.put(None)
            
            for _ in range(len(self.llm_processes)):
                self.file_queue.put(None)
            
            # Wait for processes to finish
            for p in self.repomix_processes + self.llm_processes:
                p.join(timeout=10)
                if p.is_alive():
                    p.terminate()
            
            logger.info("All workers stopped")
            
        except Exception as e:
            logger.error(f"Error stopping workers: {e}")
    
    def run(self, repositories: List[Dict[str, Any]], keywords: str, 
            min_stars: int, max_repos: int, **kwargs) -> str:
        """
        Run the complete cloud pipeline.
        
        Args:
            repositories: List of repository information
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            **kwargs: Additional job parameters
            
        Returns:
            URL of the final analysis file
        """
        try:
            # Create job
            self.create_job(keywords, min_stars, max_repos, **kwargs)
            
            # Add repositories
            self.add_repositories(repositories)
            
            # Start workers
            self.start_workers()
            
            # Wait for completion
            self.wait_for_completion(timeout=3600)  # 1 hour timeout
            
            # Finalize job
            analysis_url = self.finalize_job()
            
            return analysis_url
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
        finally:
            # Always stop workers
            self.stop_workers()
</file>

<file path="service.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Web Service
A Flask-based web service for running repository analysis jobs.
"""

import os
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_file
from werkzeug.exceptions import BadRequest
import subprocess
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import unified pipeline components
try:
    from pipeline import RepositoryPipeline
    from supabase_client import create_supabase_client
    from storage_manager import create_storage_manager
    from config import ScraperConfig
    PIPELINE_AVAILABLE = True
    print(f"Pipeline components loaded successfully")
except ImportError as e:
    print(f"Pipeline components not available: {e}")
    PIPELINE_AVAILABLE = False

app = Flask(__name__)

# Global job storage (fallback for local mode)
jobs = {}
job_lock = threading.Lock()

# Initialize cloud services if available
supabase_client = None
storage_manager = None

if CLOUD_AVAILABLE:
    try:
        supabase_client = create_supabase_client()
        storage_manager = create_storage_manager()
        print("Cloud services initialized successfully")
    except Exception as e:
        print(f"Failed to initialize cloud services: {e}")
        CLOUD_AVAILABLE = False

class JobStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

def run_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt=None, debug=False):
    """Run the repository analysis in a separate thread."""
    import logging

    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    logger.info(f"🚀 SERVICE FLOW: STARTING ANALYSIS JOB: {job_id}")
    logger.info(f"📋 SERVICE FLOW: Job parameters:")
    logger.info(f"   Keywords: {keywords}")
    logger.info(f"   Min stars: {min_stars}")
    logger.info(f"   Max repos: {max_repos} PER KEYWORD")
    logger.info(f"   Custom prompt: {custom_prompt is not None}")
    logger.info(f"   Debug mode: {debug}")

    # Calculate expected maximum repositories
    keyword_count = len([k.strip() for k in keywords.split(',')])
    expected_max = keyword_count * max_repos
    logger.info(f"🎯 SERVICE FLOW: Expected maximum repositories: {keyword_count} keywords × {max_repos} = {expected_max}")

    with job_lock:
        jobs[job_id]["status"] = JobStatus.RUNNING
        jobs[job_id]["started_at"] = datetime.now().isoformat()
        logger.info(f"✅ Job status updated to 'running'")

    try:
        # Import search components
        logger.info("📦 Importing search components...")
        from github_search import search_repositories

        # Search for repositories
        logger.info("🔍 SERVICE FLOW: Starting repository search...")
        logger.info(f"🌐 SERVICE FLOW: Searching GitHub with per-keyword logic...")
        repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos
        )

        logger.info(f"📊 SERVICE FLOW: Search completed: {len(repositories)} repositories found")
        logger.info(f"✅ SERVICE FLOW: Per-keyword logic working: {len(repositories)} ≤ {expected_max} (expected max)")

        if not repositories:
            logger.error("❌ No repositories found matching the criteria")
            raise Exception("No repositories found matching the criteria")

        # Update job with repository count
        with job_lock:
            jobs[job_id]["total_repositories"] = len(repositories)
            logger.info(f"✅ Job updated with repository count: {len(repositories)}")

        # Initialize and run unified pipeline
        logger.info("🏭 SERVICE FLOW: Initializing repository pipeline...")
        logger.info(f"📦 SERVICE FLOW: Pipeline will process {len(repositories)} repositories")
        logger.info(f"🔧 SERVICE FLOW: Chunking enabled: 3MB limit with max 3 chunks per repo")

        config = ScraperConfig()
        pipeline = RepositoryPipeline(config=config, job_id=job_id)

        logger.info("🚀 SERVICE FLOW: Starting pipeline execution...")
        logger.info(f"⚡ SERVICE FLOW: Processing repositories with repomix + LLM analysis...")
        result_file = pipeline.run(repositories)

        logger.info(f"✅ SERVICE FLOW: Pipeline execution completed")
        logger.info(f"📄 SERVICE FLOW: Result file: {result_file}")
        
        with job_lock:
            jobs[job_id]["completed_at"] = datetime.now().isoformat()

            if result_file:
                jobs[job_id]["status"] = JobStatus.COMPLETED
                jobs[job_id]["analysis_file"] = str(result_file)

                # Get file size and directory info
                result_path = Path(result_file)
                if result_path.exists():
                    jobs[job_id]["analysis_size"] = result_path.stat().st_size
                    jobs[job_id]["output_directory"] = str(result_path.parent)

                print(f"✅ Local analysis job {job_id} completed: {result_file}")
            else:
                jobs[job_id]["status"] = JobStatus.FAILED
                jobs[job_id]["error"] = "Pipeline returned no result file"
                
    except Exception as e:
        with job_lock:
            jobs[job_id]["status"] = JobStatus.FAILED
            jobs[job_id]["error"] = str(e)
            jobs[job_id]["completed_at"] = datetime.now().isoformat()

def start_cloud_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt=None, debug=False):
    """Start cloud analysis by adding repositories to the processing queue."""
    try:
        # Import required modules
        from github_search import search_repositories
        from redis_queue import QueueManager, RepositoryQueueItem
        import uuid

        # Search for repositories
        repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos
        )

        if not repositories:
            raise Exception("No repositories found matching the criteria")

        # Initialize queue manager
        queue_manager = QueueManager(config.REDIS_URL)
        repo_queue = queue_manager.get_repository_queue()

        # Add repositories to the processing queue
        for repo in repositories:
            repo_item = RepositoryQueueItem(
                repository_url=repo['url'],
                repository_name=repo['name'],
                job_id=job_id
            )
            repo_queue.put(repo_item)

        # Update job status to running (workers will process the repositories)
        if supabase_client:
            supabase_client.update_job_status(job_id, JobStatus.RUNNING)

            # Create repository records in database
            from supabase_client import RepositoryRecord
            for repo in repositories:
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=job_id,
                    name=repo['name'],
                    url=repo['url'],
                    stars=repo.get('stars', 0),
                    status='pending'
                )
                supabase_client.create_repository(repo_record)
        else:
            with job_lock:
                jobs[job_id]["status"] = JobStatus.RUNNING
                jobs[job_id]["started_at"] = datetime.now().isoformat()
                jobs[job_id]["total_repositories"] = len(repositories)

        print(f"✅ Added {len(repositories)} repositories to processing queue for job {job_id}")

    except Exception as e:
        print(f"Failed to start cloud analysis job {job_id}: {e}")

        # Update job status to failed
        if supabase_client:
            supabase_client.update_job_status(job_id, JobStatus.FAILED, error_message=str(e))
        else:
            with job_lock:
                jobs[job_id]["status"] = JobStatus.FAILED
                jobs[job_id]["error"] = str(e)
                jobs[job_id]["completed_at"] = datetime.now().isoformat()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "Repository Research Tool",
        "version": "1.0",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/start', methods=['POST'])
def start_analysis():
    """Start a new repository analysis job."""
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("JSON data required")
        
        # Validate required parameters
        keywords = data.get('keywords')
        if not keywords:
            raise BadRequest("keywords parameter is required")
        
        min_stars = data.get('min_stars', 30)
        max_repos = data.get('max_repos', 20)
        custom_prompt = data.get('custom_prompt')
        debug = data.get('debug', False)
        
        # Create job
        job_id = str(uuid.uuid4())

        if CLOUD_AVAILABLE and supabase_client:
            # Use cloud services
            try:
                from supabase_client import JobRecord
                job_record = JobRecord(
                    id=job_id,
                    keywords=keywords,
                    min_stars=min_stars,
                    max_repos=max_repos,
                    status=JobStatus.PENDING,
                    created_at=datetime.now().isoformat(),
                    custom_prompt=custom_prompt,
                    debug=debug
                )
                supabase_client.create_job(job_record)
                print(f"Created cloud job {job_id}")
            except Exception as e:
                print(f"Failed to create cloud job, falling back to local: {e}")
                # Fallback to local storage
                with job_lock:
                    jobs[job_id] = {
                        "id": job_id,
                        "status": JobStatus.PENDING,
                        "created_at": datetime.now().isoformat(),
                        "parameters": {
                            "keywords": keywords,
                            "min_stars": min_stars,
                            "max_repos": max_repos,
                            "custom_prompt": custom_prompt,
                            "debug": debug
                        }
                    }
        else:
            # Use local storage
            with job_lock:
                jobs[job_id] = {
                    "id": job_id,
                    "status": JobStatus.PENDING,
                    "created_at": datetime.now().isoformat(),
                    "parameters": {
                        "keywords": keywords,
                        "min_stars": min_stars,
                        "max_repos": max_repos,
                        "custom_prompt": custom_prompt,
                        "debug": debug
                    }
                }

        # Start analysis based on deployment mode
        if CLOUD_AVAILABLE:
            # In cloud mode, add job to queue for worker processing
            start_cloud_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt, debug)
        else:
            # In local mode, run analysis in background thread
            thread = threading.Thread(
                target=run_analysis_job,
                args=(job_id, keywords, min_stars, max_repos, custom_prompt, debug)
            )
            thread.daemon = True
            thread.start()
        
        return jsonify({
            "job_id": job_id,
            "status": JobStatus.PENDING,
            "message": "Analysis job started"
        }), 202
        
    except BadRequest as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a specific job."""
    if CLOUD_AVAILABLE and supabase_client:
        try:
            # Get job from cloud database
            job_record = supabase_client.get_job(job_id)
            if job_record:
                job_data = {
                    "id": job_record.id,
                    "status": job_record.status,
                    "created_at": job_record.created_at,
                    "started_at": job_record.started_at,
                    "completed_at": job_record.completed_at,
                    "parameters": {
                        "keywords": job_record.keywords,
                        "min_stars": job_record.min_stars,
                        "max_repos": job_record.max_repos,
                        "custom_prompt": job_record.custom_prompt,
                        "debug": job_record.debug
                    },
                    "total_repositories": job_record.total_repositories,
                    "analysis_file_url": job_record.analysis_file_url,
                    "error_message": job_record.error_message
                }
                return jsonify(job_data)
        except Exception as e:
            print(f"Error getting job from cloud: {e}")
            # Fall back to local storage

    # Use local storage
    with job_lock:
        job = jobs.get(job_id)

    if not job:
        return jsonify({"error": "Job not found"}), 404

    return jsonify(job)

@app.route('/results/<job_id>', methods=['GET'])
def get_job_results(job_id):
    """Get the results of a completed job."""
    with job_lock:
        job = jobs.get(job_id)
    
    if not job:
        return jsonify({"error": "Job not found"}), 404
    
    if job["status"] != JobStatus.COMPLETED:
        return jsonify({"error": "Job not completed"}), 400
    
    analysis_file = job.get("analysis_file")
    if not analysis_file or not Path(analysis_file).exists():
        return jsonify({"error": "Analysis file not found"}), 404
    
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis_data = json.load(f)
        return jsonify(analysis_data)
    except Exception as e:
        return jsonify({"error": f"Error reading analysis file: {str(e)}"}), 500

@app.route('/download/<job_id>', methods=['GET'])
def download_analysis(job_id):
    """Download the analysis file for a completed job."""
    with job_lock:
        job = jobs.get(job_id)
    
    if not job:
        return jsonify({"error": "Job not found"}), 404
    
    if job["status"] != JobStatus.COMPLETED:
        return jsonify({"error": "Job not completed"}), 400
    
    analysis_file = job.get("analysis_file")
    if not analysis_file or not Path(analysis_file).exists():
        return jsonify({"error": "Analysis file not found"}), 404
    
    return send_file(
        analysis_file,
        as_attachment=True,
        download_name=f"repository_analysis_{job_id}.json"
    )

@app.route('/jobs', methods=['GET'])
def list_jobs():
    """List all jobs."""
    with job_lock:
        job_list = list(jobs.values())
    
    return jsonify({
        "jobs": job_list,
        "total_jobs": len(job_list)
    })

@app.route('/config', methods=['GET'])
def get_config():
    """Get current service configuration."""
    try:
        from config import ScraperConfig
        config = ScraperConfig()
        
        return jsonify({
            "repomix_workers": config.REPOMIX_WORKERS,
            "llm_workers": config.LLM_WORKERS,
            "rate_limit_mb_per_min": config.LLM_RATE_LIMIT_MB_PER_MIN,
            "max_file_size_mb": config.MAX_FILE_SIZE_MB,
            "llm_model": config.LLM_MODEL,
            "api_configured": bool(config.LLM_API_KEY)
        })
    except Exception as e:
        return jsonify({"error": f"Error getting config: {str(e)}"}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    
    print(f"🚀 Starting Repository Research Tool Service")
    print(f"   Port: {port}")
    print(f"   Debug: {debug}")
    print(f"   Health check: http://localhost:{port}/health")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
</file>

</files>
