#!/usr/bin/env python3
"""
Test suite for cloud-integrated pipeline.
Tests Redis queue integration, Supabase operations, and storage management.
"""

import os
import sys
import uuid
import json
import pytest
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from redis_queue import QueueManager, RepositoryQueueItem, FileQueueItem
from supabase_client import SupabaseClient, JobRecord, RepositoryRecord
from storage_manager import StorageManager

class TestCloudPipelineIntegration:
    """Test cloud pipeline integration components."""
    
    @pytest.fixture
    def mock_redis_url(self):
        """Mock Redis URL for testing."""
        return "redis://localhost:6379"
    
    @pytest.fixture
    def mock_supabase_client(self):
        """Mock Supabase client for testing."""
        client = Mock(spec=SupabaseClient)
        client.create_job.return_value = JobRecord(
            id="test-job-123",
            keywords="test keywords",
            min_stars=30,
            max_repos=5,
            status="pending",
            created_at=datetime.now().isoformat()
        )
        client.get_job.return_value = JobRecord(
            id="test-job-123",
            keywords="test keywords",
            min_stars=30,
            max_repos=5,
            status="running",
            created_at=datetime.now().isoformat()
        )
        return client
    
    @pytest.fixture
    def mock_storage_manager(self):
        """Mock storage manager for testing."""
        storage = Mock(spec=StorageManager)
        storage.upload_repomix_file.return_value = "https://storage.supabase.co/repomixes/test.md"
        storage.upload_analysis_file.return_value = "https://storage.supabase.co/analyses/test.json"
        return storage
    
    @pytest.fixture
    def mock_queue_manager(self, mock_redis_url):
        """Mock queue manager for testing."""
        with patch('redis_queue.redis.from_url') as mock_redis:
            mock_redis_client = Mock()
            mock_redis_client.ping.return_value = True
            mock_redis_client.lpush.return_value = 1
            mock_redis_client.brpoplpush.return_value = None
            mock_redis_client.llen.return_value = 0
            mock_redis.return_value = mock_redis_client
            
            queue_manager = QueueManager(mock_redis_url)
            return queue_manager
    
    def test_queue_manager_initialization(self, mock_queue_manager):
        """Test queue manager initializes correctly."""
        assert mock_queue_manager is not None
        
        # Test getting queues
        repo_queue = mock_queue_manager.get_repository_queue()
        file_queue = mock_queue_manager.get_file_queue()
        
        assert repo_queue is not None
        assert file_queue is not None
    
    def test_repository_queue_operations(self, mock_queue_manager):
        """Test repository queue put/get operations."""
        repo_queue = mock_queue_manager.get_repository_queue()
        
        # Create test item
        test_item = RepositoryQueueItem(
            repository_url="https://github.com/test/repo",
            repository_name="test_repo",
            job_id="test-job-123"
        )
        
        # Test put operation
        repo_queue.put(test_item)
        
        # Verify Redis operations were called
        repo_queue.redis_client.lpush.assert_called()
    
    def test_file_queue_operations(self, mock_queue_manager):
        """Test file queue put/get operations."""
        file_queue = mock_queue_manager.get_file_queue()
        
        # Create test item
        test_item = FileQueueItem(
            file_path="https://storage.supabase.co/repomixes/test.md",
            repository_name="test_repo",
            job_id="test-job-123"
        )
        
        # Test put operation
        file_queue.put(test_item)
        
        # Verify Redis operations were called
        file_queue.redis_client.lpush.assert_called()
    
    def test_supabase_job_operations(self, mock_supabase_client):
        """Test Supabase job CRUD operations."""
        # Test job creation
        job = JobRecord(
            id="test-job-123",
            keywords="test keywords",
            min_stars=30,
            max_repos=5,
            status="pending",
            created_at=datetime.now().isoformat()
        )
        
        created_job = mock_supabase_client.create_job(job)
        assert created_job.id == "test-job-123"
        
        # Test job retrieval
        retrieved_job = mock_supabase_client.get_job("test-job-123")
        assert retrieved_job.id == "test-job-123"
        
        # Test job status update
        mock_supabase_client.update_job_status("test-job-123", "running")
        mock_supabase_client.update_job_status.assert_called_with("test-job-123", "running")
    
    def test_storage_operations(self, mock_storage_manager):
        """Test storage manager operations."""
        # Test repomix file upload
        repomix_url = mock_storage_manager.upload_repomix_file(
            job_id="test-job-123",
            repository_name="test_repo",
            content="# Test Repomix Content"
        )
        assert "repomixes" in repomix_url
        
        # Test analysis file upload
        analysis_data = {
            "metadata": {"job_id": "test-job-123"},
            "analyses": []
        }
        analysis_url = mock_storage_manager.upload_analysis_file(
            job_id="test-job-123",
            analysis_data=analysis_data
        )
        assert "analyses" in analysis_url
    
    def test_integrated_workflow_simulation(self, mock_queue_manager, 
                                          mock_supabase_client, mock_storage_manager):
        """Test integrated workflow with all cloud components."""
        job_id = "test-job-123"
        
        # 1. Create job in database
        job = JobRecord(
            id=job_id,
            keywords="test integration",
            min_stars=30,
            max_repos=1,
            status="pending",
            created_at=datetime.now().isoformat()
        )
        created_job = mock_supabase_client.create_job(job)
        assert created_job.id == job_id
        
        # 2. Add repository to queue
        repo_item = RepositoryQueueItem(
            repository_url="https://github.com/test/repo",
            repository_name="test_repo",
            job_id=job_id
        )
        repo_queue = mock_queue_manager.get_repository_queue()
        repo_queue.put(repo_item)
        
        # 3. Simulate repomix processing
        repomix_url = mock_storage_manager.upload_repomix_file(
            job_id=job_id,
            repository_name="test_repo",
            content="# Test Repository Content"
        )
        
        # 4. Add file to LLM queue
        file_item = FileQueueItem(
            file_path=repomix_url,
            repository_name="test_repo",
            job_id=job_id
        )
        file_queue = mock_queue_manager.get_file_queue()
        file_queue.put(file_item)
        
        # 5. Simulate analysis completion
        analysis_data = {
            "metadata": {"job_id": job_id},
            "analyses": [{
                "repository": {"name": "test_repo"},
                "analysis": {"content": "Test analysis completed"}
            }]
        }
        analysis_url = mock_storage_manager.upload_analysis_file(job_id, analysis_data)
        
        # 6. Update job status
        mock_supabase_client.update_job_status(job_id, "completed", 
                                             analysis_file_url=analysis_url)
        
        # Verify all operations were called
        mock_supabase_client.create_job.assert_called()
        mock_storage_manager.upload_repomix_file.assert_called()
        mock_storage_manager.upload_analysis_file.assert_called()
        mock_supabase_client.update_job_status.assert_called()

class TestCloudPipelineConfiguration:
    """Test cloud pipeline configuration and environment setup."""
    
    def test_cloud_configuration_loading(self):
        """Test cloud configuration loads correctly."""
        with patch.dict(os.environ, {
            'DEPLOYMENT_MODE': 'cloud',
            'REDIS_URL': 'redis://test:6379',
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_KEY': 'test-key'
        }):
            from config import ScraperConfig
            
            assert ScraperConfig.DEPLOYMENT_MODE == 'cloud'
            assert ScraperConfig.USE_CLOUD_SERVICES == True
            assert ScraperConfig.REDIS_URL == 'redis://test:6379'
            assert ScraperConfig.SUPABASE_URL == 'https://test.supabase.co'
    
    def test_local_fallback_configuration(self):
        """Test configuration falls back to local mode."""
        with patch.dict(os.environ, {}, clear=True):
            from config import ScraperConfig
            
            assert ScraperConfig.DEPLOYMENT_MODE == 'local'
            assert ScraperConfig.USE_CLOUD_SERVICES == False
            assert ScraperConfig.REDIS_URL == 'redis://localhost:6379'

class TestCloudPipelineErrorHandling:
    """Test error handling in cloud pipeline components."""
    
    def test_redis_connection_failure(self):
        """Test handling of Redis connection failures."""
        with patch('redis_queue.redis.from_url') as mock_redis:
            mock_redis.side_effect = Exception("Connection failed")
            
            with pytest.raises(Exception):
                QueueManager("redis://invalid:6379")
    
    def test_supabase_operation_failure(self, mock_supabase_client):
        """Test handling of Supabase operation failures."""
        mock_supabase_client.create_job.side_effect = Exception("Database error")
        
        with pytest.raises(Exception):
            job = JobRecord(
                id="test-job",
                keywords="test",
                min_stars=30,
                max_repos=5,
                status="pending",
                created_at=datetime.now().isoformat()
            )
            mock_supabase_client.create_job(job)
    
    def test_storage_operation_failure(self, mock_storage_manager):
        """Test handling of storage operation failures."""
        mock_storage_manager.upload_repomix_file.side_effect = Exception("Upload failed")
        
        with pytest.raises(Exception):
            mock_storage_manager.upload_repomix_file(
                job_id="test-job",
                repository_name="test_repo",
                content="test content"
            )

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
