# Product Requirements Document (PRD)
## GitHub Repository Research Tool

### **Product Overview**
A cloud-native research tool that automatically discovers, processes, and analyzes GitHub repositories using AI. The tool searches repositories by keywords, processes their content with repomix, and generates comprehensive AI-powered analysis reports.

---

## **Core Workflow**

### **1. Repository Discovery**
- **GitHub Search**: Search repositories by keywords, sorted by last updated
- **Per-Keyword Processing**: Each keyword gets separate search with configurable parameters
- **Deduplication**: Remove duplicate repositories across keyword searches
- **Parameters**: Minimum stars threshold, maximum repositories per keyword

### **2. Content Processing**
- **Repomix Processing**: Use `npx repomix --remote --include` to package repository content
- **Cloud Storage**: Save repomix outputs to Supabase storage
- **Size Validation**: Check file size before LLM processing

### **3. Chunking Logic** ⭐ **NEW FEATURE**
- **Size Check**: If repomix file > 3MB, trigger chunking process
- **Local Retrieval**: Download file from Supabase for local chunking
- **Chunking Rules**: 
  - Split into 3MB maximum chunks
  - Keep only first 3 chunks
  - Re-upload chunks to storage
- **Fallback**: Files ≤ 3MB proceed directly to LLM

### **4. AI Analysis**
- **LLM Processing**: Analyze repository content using Gemini API
- **Content Analysis**: Generate comprehensive feature and functionality summaries
- **Result Storage**: Save analysis results to database

### **5. Report Generation**
- **Consolidation**: Merge all individual analyses into single comprehensive report
- **Output Format**: JSON format with structured analysis data
- **Final Storage**: Save consolidated report to cloud storage

---

## **Technical Architecture**

### **Queue System**
- **Repomix Queue**: Redis queue for repository processing tasks
- **LLM Queue**: Redis queue for AI analysis tasks
- **Worker Separation**: Independent workers for each processing stage

### **Data Flow**
```
GitHub Search → Deduplication → Repomix Queue → Repomix Worker → 
Size Check → [Chunking if >3MB] → LLM Queue → LLM Worker → 
Analysis Storage → Report Consolidation → Final JSON Output
```

### **Storage Architecture**
- **Database**: Supabase (jobs, repositories, analyses)
- **File Storage**: Supabase Storage (repomix files, chunks, reports)
- **Queue System**: Redis (task management)

---

## **Key Features**

### **Intelligent Processing**
- ✅ Keyword-based repository discovery
- ✅ Automatic deduplication
- ✅ Size-aware chunking
- ✅ Parallel processing with workers

### **Scalability**
- ✅ Redis queue system for distributed processing
- ✅ Cloud-native storage
- ✅ Independent worker scaling
- ✅ Configurable processing limits

### **Reliability**
- ✅ Comprehensive logging throughout pipeline
- ✅ Error handling and retry mechanisms
- ✅ Component-based testing strategy
- ✅ Validation at each processing stage

---

## **Configuration Parameters**

### **Search Parameters**
- `keywords`: List of search terms
- `min_stars`: Minimum repository stars
- `max_repos_per_keyword`: Repository limit per keyword
- `file_includes`: File types for repomix processing

### **Processing Parameters**
- `max_file_size_mb`: Size threshold for chunking (3MB)
- `max_chunks`: Maximum chunks to retain (3)
- `chunk_size_mb`: Individual chunk size (3MB)

### **LLM Parameters**
- `model`: AI model for analysis
- `max_tokens`: Output token limit
- `temperature`: Analysis creativity level

---

## **Development Strategy**

### **Component-Based Testing**
Each component must be tested independently before integration:

1. **Chunking Component**: Validate 3MB chunking rules
2. **Repomix Component**: Test repository processing
3. **Supabase Component**: Verify database and storage operations
4. **LLM Component**: Validate AI analysis functionality
5. **Queue Component**: Test Redis queue operations
6. **Integration Testing**: End-to-end workflow validation

### **Validation Process**
- ✅ Unit tests for each component
- ✅ Integration tests for component interactions
- ✅ End-to-end workflow testing
- ✅ Performance and scalability testing

---

## **Success Metrics**

### **Functional Requirements**
- ✅ Successfully process repositories up to configured limits
- ✅ Maintain 3MB chunking compliance
- ✅ Generate comprehensive AI analysis reports
- ✅ Produce consolidated JSON output

### **Performance Requirements**
- ✅ Process multiple repositories concurrently
- ✅ Handle files of varying sizes efficiently
- ✅ Maintain system responsiveness under load
- ✅ Complete full workflow within reasonable timeframes

### **Quality Requirements**
- ✅ Comprehensive logging for debugging
- ✅ Error recovery and graceful degradation
- ✅ Data integrity throughout pipeline
- ✅ Consistent output format and quality

---

## **Implementation Phases**

### **Phase 1: Core Components** 
- [x] Chunking logic implementation
- [x] Repomix processing
- [x] Supabase integration
- [x] LLM integration

### **Phase 2: Queue System** 🔄 **CURRENT**
- [ ] Redis queue implementation
- [ ] Worker process architecture
- [ ] Local retrieval for chunking
- [ ] Queue monitoring and management

### **Phase 3: Integration & Testing**
- [ ] End-to-end workflow integration
- [ ] Comprehensive testing suite
- [ ] Performance optimization
- [ ] Documentation and deployment

### **Phase 4: Production Deployment**
- [ ] Cloud infrastructure setup
- [ ] Monitoring and alerting
- [ ] Scaling configuration
- [ ] Production validation

---

## **Risk Mitigation**

### **Technical Risks**
- **API Rate Limits**: Implement exponential backoff and retry logic
- **Large File Processing**: Robust chunking with size validation
- **Queue Failures**: Dead letter queues and error recovery
- **Storage Limits**: Cleanup policies and size monitoring

### **Operational Risks**
- **Cost Management**: Processing limits and budget controls
- **Data Quality**: Validation at each processing stage
- **System Reliability**: Health checks and monitoring
- **Scalability**: Load testing and capacity planning

---

This PRD serves as the blueprint for building a robust, scalable GitHub repository research tool that meets all specified requirements while maintaining high quality and reliability standards.
