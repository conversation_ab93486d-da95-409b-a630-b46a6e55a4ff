#!/usr/bin/env python3
"""
Token-Based Chunking System for Repository Research Tool

Replaces file-based chunking (3MB) with token-based chunking (850K tokens)
using tiktoken library for accurate token counting and optimal Gemini API utilization.

Key Features:
- 850K token chunks (not 3MB file chunks)
- Stay under 1.8M TPM limit
- Maximum 3 chunks per repository
- Accurate token counting with tiktoken
- Dynamic worker allocation for optimal TPM utilization
"""

import os
import tempfile
import logging
from typing import List, Optional, Tuple
from pathlib import Path

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False
    logging.warning("tiktoken not available, falling back to estimation")

logger = logging.getLogger(__name__)

# Token chunking configuration
DEFAULT_MAX_TOKENS_PER_CHUNK = 850000  # 850K tokens per chunk
DEFAULT_MAX_CHUNKS = 3  # Maximum 3 chunks per repository
TPM_LIMIT = 1800000  # Target 1.8M TPM to stay under 2M limit

class TokenChunker:
    """
    Token-based chunking system using tiktoken for accurate token counting.
    """
    
    def __init__(self, max_tokens_per_chunk: int = DEFAULT_MAX_TOKENS_PER_CHUNK, 
                 max_chunks: int = DEFAULT_MAX_CHUNKS):
        """
        Initialize token chunker.
        
        Args:
            max_tokens_per_chunk: Maximum tokens per chunk (default: 850K)
            max_chunks: Maximum number of chunks to create (default: 3)
        """
        self.max_tokens_per_chunk = max_tokens_per_chunk
        self.max_chunks = max_chunks
        
        # Initialize tiktoken encoding
        if TIKTOKEN_AVAILABLE:
            try:
                self.encoding = tiktoken.get_encoding("cl100k_base")
                logger.info(f"🔧 TokenChunker initialized with tiktoken encoding")
            except Exception as e:
                logger.warning(f"Failed to initialize tiktoken: {e}, using estimation")
                self.encoding = None
        else:
            self.encoding = None
            
        logger.info(f"📊 TokenChunker config: {max_tokens_per_chunk:,} tokens/chunk, max {max_chunks} chunks")
    
    def count_tokens(self, text: str) -> int:
        """
        Count tokens in text using tiktoken or estimation.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        if self.encoding:
            try:
                return len(self.encoding.encode(text))
            except Exception as e:
                logger.warning(f"tiktoken encoding failed: {e}, using estimation")
        
        # Fallback estimation: ~1.3 tokens per word
        return int(len(text.split()) * 1.3)
    
    def estimate_tokens_from_file_size(self, file_size_mb: float) -> int:
        """
        Estimate tokens from file size (rough approximation).
        
        Args:
            file_size_mb: File size in megabytes
            
        Returns:
            Estimated number of tokens
        """
        # Rough estimation: ~1MB = ~200K tokens for code/text files
        return int(file_size_mb * 200000)
    
    def should_chunk_file(self, file_path: str) -> Tuple[bool, int]:
        """
        Determine if a file should be chunked based on token count.
        
        Args:
            file_path: Path to the file to check
            
        Returns:
            Tuple of (should_chunk, token_count)
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            token_count = self.count_tokens(content)
            should_chunk = token_count > self.max_tokens_per_chunk
            
            logger.info(f"📏 TOKEN CHECK: {os.path.basename(file_path)} = {token_count:,} tokens")
            
            if should_chunk:
                logger.info(f"🔄 TOKEN CHECK: File exceeds limit ({token_count:,} > {self.max_tokens_per_chunk:,}) - chunking needed")
            else:
                logger.info(f"✅ TOKEN CHECK: File within limit ({token_count:,} ≤ {self.max_tokens_per_chunk:,}) - no chunking needed")
            
            return should_chunk, token_count
            
        except Exception as e:
            logger.error(f"Error checking file tokens: {e}")
            return False, 0
    
    def chunk_content_by_tokens(self, content: str) -> List[str]:
        """
        Chunk content by token count, preserving line boundaries.
        
        Args:
            content: Text content to chunk
            
        Returns:
            List of content chunks
        """
        total_tokens = self.count_tokens(content)
        
        if total_tokens <= self.max_tokens_per_chunk:
            logger.info(f"✅ TOKEN CHUNKING: Content within limit ({total_tokens:,} tokens) - no chunking needed")
            return [content]
        
        logger.info(f"🔄 TOKEN CHUNKING: Content exceeds limit ({total_tokens:,} tokens) - starting chunking")
        logger.info(f"📊 TOKEN CHUNKING: Target {self.max_tokens_per_chunk:,} tokens/chunk, max {self.max_chunks} chunks")
        
        # Split content by lines to preserve structure
        lines = content.split('\n')
        chunks = []
        current_chunk_lines = []
        current_chunk_tokens = 0
        
        for line in lines:
            line_tokens = self.count_tokens(line + '\n')  # Include newline in count
            
            # Check if adding this line would exceed the chunk limit
            if current_chunk_tokens + line_tokens > self.max_tokens_per_chunk and current_chunk_lines:
                # Save current chunk and start new one
                chunk_content = '\n'.join(current_chunk_lines)
                chunks.append(chunk_content)
                
                logger.info(f"📝 TOKEN CHUNKING: Created chunk {len(chunks)} with {current_chunk_tokens:,} tokens")
                
                # Check if we've reached max chunks
                if len(chunks) >= self.max_chunks:
                    logger.info(f"⏭️ TOKEN CHUNKING: Reached max chunks limit ({self.max_chunks}), stopping")
                    break
                
                # Start new chunk
                current_chunk_lines = [line]
                current_chunk_tokens = line_tokens
            else:
                # Add line to current chunk
                current_chunk_lines.append(line)
                current_chunk_tokens += line_tokens
        
        # Add final chunk if we have content and haven't reached max chunks
        if current_chunk_lines and len(chunks) < self.max_chunks:
            chunk_content = '\n'.join(current_chunk_lines)
            chunks.append(chunk_content)
            logger.info(f"📝 TOKEN CHUNKING: Created final chunk {len(chunks)} with {current_chunk_tokens:,} tokens")
        
        logger.info(f"🎯 TOKEN CHUNKING: Chunking complete - {len(chunks)} chunks created from {total_tokens:,} tokens")
        
        return chunks
    
    def chunk_file_by_tokens(self, file_path: str) -> List[str]:
        """
        Chunk a file by tokens and return list of chunk file paths.
        
        Args:
            file_path: Path to the file to chunk
            
        Returns:
            List of chunk file paths (or [file_path] if no chunking needed)
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.warning(f"📏 TOKEN CHUNKING: File not found: {file_path}")
                return []
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if chunking is needed
            should_chunk, token_count = self.should_chunk_file(file_path)
            
            if not should_chunk:
                return [file_path]
            
            # Chunk the content
            content_chunks = self.chunk_content_by_tokens(content)
            
            # Create chunk files
            chunk_paths = []
            base_name = file_path
            
            for i, chunk_content in enumerate(content_chunks):
                chunk_path = f"{base_name}.chunk_{i}"
                
                with open(chunk_path, 'w', encoding='utf-8') as f:
                    f.write(chunk_content)
                
                chunk_tokens = self.count_tokens(chunk_content)
                logger.info(f"✅ TOKEN CHUNKING: Chunk {i+1} created: {chunk_tokens:,} tokens -> {os.path.basename(chunk_path)}")
                chunk_paths.append(chunk_path)
            
            # Delete the original file
            logger.info(f"🗑️ TOKEN CHUNKING: Deleting original file: {os.path.basename(file_path)}")
            os.remove(file_path)
            
            logger.info(f"📋 TOKEN CHUNKING: Chunk files: {[os.path.basename(f) for f in chunk_paths]}")
            
            return chunk_paths
            
        except Exception as e:
            logger.error(f"Error chunking file by tokens: {e}")
            return [file_path] if os.path.exists(file_path) else []


# Global token chunker instance
_token_chunker = None

def get_token_chunker(max_tokens_per_chunk: int = DEFAULT_MAX_TOKENS_PER_CHUNK,
                     max_chunks: int = DEFAULT_MAX_CHUNKS) -> TokenChunker:
    """
    Get global token chunker instance.
    
    Args:
        max_tokens_per_chunk: Maximum tokens per chunk
        max_chunks: Maximum number of chunks
        
    Returns:
        TokenChunker instance
    """
    global _token_chunker
    if _token_chunker is None:
        _token_chunker = TokenChunker(max_tokens_per_chunk, max_chunks)
    return _token_chunker


# Compatibility functions for existing code
def chunk_and_retain_file_by_tokens(file_path: str, max_tokens_per_chunk: int = DEFAULT_MAX_TOKENS_PER_CHUNK,
                                   max_chunks: int = DEFAULT_MAX_CHUNKS) -> List[str]:
    """
    Chunk a file by tokens (replacement for chunk_and_retain_file).
    
    Args:
        file_path: Path to the file to chunk
        max_tokens_per_chunk: Maximum tokens per chunk (default: 850K)
        max_chunks: Maximum number of chunks (default: 3)
        
    Returns:
        List of chunk file paths
    """
    chunker = get_token_chunker(max_tokens_per_chunk, max_chunks)
    return chunker.chunk_file_by_tokens(file_path)


def count_tokens_in_file(file_path: str) -> int:
    """
    Count tokens in a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Number of tokens in the file
    """
    chunker = get_token_chunker()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return chunker.count_tokens(content)
    except Exception as e:
        logger.error(f"Error counting tokens in file: {e}")
        return 0


def count_tokens_in_text(text: str) -> int:
    """
    Count tokens in text.
    
    Args:
        text: Text to count tokens for
        
    Returns:
        Number of tokens
    """
    chunker = get_token_chunker()
    return chunker.count_tokens(text)
