#!/usr/bin/env python3
"""
Repository Research Tool v3.0 - Unified Architecture Test

This test bypasses GitHub API rate limits by using a predefined list of 
proxy-related repositories to test the complete unified architecture.
"""

import os
import sys
import uuid
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config import ScraperConfig
from pipeline import RepositoryPipeline

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Test the unified architecture with predefined proxy repositories."""
    
    print("🚀 REPOSITORY RESEARCH TOOL v3.0 - UNIFIED ARCHITECTURE TEST")
    print("=" * 70)
    print("Testing with proxy-related repositories (bypassing GitHub API)")
    print()
    
    # Predefined proxy repositories for testing
    test_repositories = [
        {
            'name': 'clarketm/proxy-list',
            'full_name': 'clarketm/proxy-list',
            'html_url': 'https://github.com/clarketm/proxy-list',
            'stargazers_count': 2847,
            'updated_at': '2025-01-15T10:30:00Z'
        },
        {
            'name': 'TheSpeedX/PROXY-List',
            'full_name': 'TheSpeedX/PROXY-List', 
            'html_url': 'https://github.com/TheSpeedX/PROXY-List',
            'stargazers_count': 2156,
            'updated_at': '2025-01-14T15:45:00Z'
        },
        {
            'name': 'hookzof/socks5_list',
            'full_name': 'hookzof/socks5_list',
            'html_url': 'https://github.com/hookzof/socks5_list',
            'stargazers_count': 1923,
            'updated_at': '2025-01-13T08:20:00Z'
        },
        {
            'name': 'monosans/proxy-list',
            'full_name': 'monosans/proxy-list',
            'html_url': 'https://github.com/monosans/proxy-list',
            'stargazers_count': 1654,
            'updated_at': '2025-01-12T12:15:00Z'
        },
        {
            'name': 'sunny9577/proxy-scraper',
            'full_name': 'sunny9577/proxy-scraper',
            'html_url': 'https://github.com/sunny9577/proxy-scraper',
            'stargazers_count': 892,
            'updated_at': '2025-01-11T16:30:00Z'
        }
    ]
    
    print(f"📊 Test Configuration:")
    print(f"   Keywords: proxy list, free proxy, proxy scrape")
    print(f"   Test repositories: {len(test_repositories)}")
    print(f"   Min stars: 30 (all test repos exceed this)")
    print(f"   Architecture: Unified (Redis + Supabase)")
    print()
    
    # Load configuration
    config = ScraperConfig()
    print("⚙️ Configuration loaded:")
    print(f"   Repomix workers: {config.REPOMIX_WORKERS}")
    print(f"   LLM workers: {config.LLM_WORKERS}")
    print(f"   Redis URL: {config.REDIS_URL}")
    print(f"   Supabase URL: {config.SUPABASE_URL}")
    print()
    
    try:
        # Initialize unified pipeline
        print("🏭 Initializing Unified Pipeline...")
        pipeline = RepositoryPipeline(config=config)
        print(f"✅ Pipeline initialized successfully")
        print(f"   Job ID: {pipeline.job_id}")
        print(f"   Output directory: {pipeline.output_dir}")
        print()
        
        # Run the complete pipeline
        print("🚀 Running Complete Unified Pipeline...")
        print(f"   Processing {len(test_repositories)} repositories")
        print(f"   Using {config.REPOMIX_WORKERS} repomix workers")
        print(f"   Using {config.LLM_WORKERS} LLM workers")
        print("=" * 70)
        
        start_time = datetime.now()
        
        # Execute the pipeline
        result_url = pipeline.run(
            repositories=test_repositories,
            keywords="proxy list,free proxy,proxy scrape",
            min_stars=30,
            max_repos=60
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("=" * 70)
        print("🎉 UNIFIED ARCHITECTURE TEST COMPLETED!")
        print(f"   Duration: {duration:.1f} seconds")
        print(f"   Repositories processed: {len(test_repositories)}")
        print(f"   Result URL: {result_url}")
        print(f"   Output directory: {pipeline.output_dir}")
        
        # Show results summary
        analysis_file = pipeline.output_dir / "analysis.json"
        if analysis_file.exists():
            file_size = analysis_file.stat().st_size
            print(f"   Analysis file: analysis.json ({file_size:,} bytes)")
        
        repomix_dir = pipeline.output_dir / "repomixes"
        if repomix_dir.exists():
            repomix_files = list(repomix_dir.glob("*.md"))
            print(f"   Repomix files: {len(repomix_files)} files generated")
        
        print()
        print("✅ UNIFIED ARCHITECTURE v3.0 VALIDATION SUCCESSFUL!")
        print("   - Single pipeline architecture working")
        print("   - Redis distributed queues functional")
        print("   - Supabase database integration successful")
        print("   - Multiprocessing workers operational")
        print("   - End-to-end processing completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Unified architecture test failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
