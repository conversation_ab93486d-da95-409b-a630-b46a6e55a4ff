#!/usr/bin/env python3
"""
Chunking Worker for Repository Research Tool

Handles the local retrieval and chunking workflow:
1. Download files from Supabase storage
2. Check file size (>3MB triggers chunking)
3. Chunk locally if needed (3MB chunks, max 3 chunks)
4. Re-upload chunks to storage
5. Queue chunks for LLM analysis

This implements the PRD requirement for post-upload chunking.
"""

import os
import tempfile
import logging
from pathlib import Path
from typing import List, Optional
from datetime import datetime

from queue_manager import get_queue_manager, FileQueueItem
from storage_manager import StorageManager
from utils import get_file_size_mb
from token_chunking import TokenChunker, count_tokens_in_text, count_tokens_in_file
from config import ScraperConfig

logger = logging.getLogger(__name__)

class ChunkingWorker:
    """
    Worker that handles post-upload file chunking.
    
    Implements the PRD workflow:
    Repomix Worker → Upload Full File → Chunking Worker (size check) → LLM Worker
    """
    
    def __init__(self, worker_id: str, config: ScraperConfig):
        """Initialize chunking worker."""
        self.worker_id = worker_id
        self.config = config
        self.storage_manager = StorageManager(config)

        # Initialize token chunker with 850K token limit
        self.token_chunker = TokenChunker(
            max_tokens_per_chunk=850000,  # 850K tokens per chunk
            max_chunks=3  # Maximum 3 chunks per repository
        )
        
        # Get queue manager
        queue_manager = get_queue_manager(config.REDIS_URL)
        
        # Get queues
        self.repomix_validated_queue = queue_manager.get_repomix_validated_queue()
        self.file_queue = queue_manager.get_file_queue()
        
        logger.info(f"Chunking Worker {worker_id} initialized")
    
    def process_file_for_chunking(self, file_item: FileQueueItem) -> bool:
        """
        Process a file for potential chunking.
        
        Args:
            file_item: File item from repomix_validated_queue
            
        Returns:
            bool: True if processing successful, False otherwise
        """
        try:
            repo_name = file_item.repository_name
            file_url = file_item.file_path
            job_id = file_item.job_id
            
            logger.info(f"Chunking Worker {self.worker_id}: Processing {repo_name}")
            logger.info(f"Chunking Worker {self.worker_id}: File URL: {file_url}")
            
            # Step 1: Download file from Supabase storage
            logger.info(f"Chunking Worker {self.worker_id}: Downloading file for size check...")
            file_content = self.storage_manager.download_file_content(file_url)
            
            # Step 2: Create temporary file to check size
            with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Step 3: Check token count (not file size)
                token_count = count_tokens_in_file(temp_file_path)
                file_size_mb = get_file_size_mb(temp_file_path)  # For logging only
                logger.info(f"Chunking Worker {self.worker_id}: {repo_name} - {token_count:,} tokens ({file_size_mb:.2f}MB)")

                if token_count <= self.token_chunker.max_tokens_per_chunk:
                    # File is within token limit - queue directly for LLM
                    logger.info(f"Chunking Worker {self.worker_id}: {repo_name} within token limit ({token_count:,} ≤ {self.token_chunker.max_tokens_per_chunk:,}) - queuing for LLM")
                    
                    llm_file_item = FileQueueItem(
                        file_path=file_url,  # Use original URL
                        repository_name=repo_name,
                        job_id=job_id
                    )
                    self.file_queue.put(llm_file_item)
                    logger.info(f"Chunking Worker {self.worker_id}: {repo_name} queued for LLM analysis")
                    
                else:
                    # File exceeds token limit - chunk it
                    logger.info(f"Chunking Worker {self.worker_id}: {repo_name} exceeds token limit ({token_count:,} > {self.token_chunker.max_tokens_per_chunk:,}) - starting token-based chunking")

                    # Step 4: Chunk the file by tokens
                    chunk_paths = self.token_chunker.chunk_file_by_tokens(temp_file_path)
                    
                    logger.info(f"Chunking Worker {self.worker_id}: {repo_name} chunked into {len(chunk_paths)} parts")
                    
                    # Step 5: Upload chunks and queue for LLM
                    uploaded_chunk_urls = []
                    for i, chunk_path in enumerate(chunk_paths):
                        # Read chunk content
                        with open(chunk_path, 'r', encoding='utf-8') as f:
                            chunk_content = f.read()

                        chunk_tokens = count_tokens_in_text(chunk_content)
                        chunk_size_mb = get_file_size_mb(chunk_path)

                        # Create unique filename for chunk
                        safe_repo_name = repo_name.replace('/', '_').replace('\\', '_')
                        chunk_filename = f"{safe_repo_name}_chunk_{i}.md"
                        
                        # Upload chunk to storage
                        chunk_url = self.storage_manager.upload_repomix_file(
                            job_id, chunk_filename, chunk_content
                        )
                        uploaded_chunk_urls.append(chunk_url)
                        
                        logger.info(f"Chunking Worker {self.worker_id}: Uploaded chunk {i+1}/{len(chunk_paths)} ({chunk_tokens:,} tokens, {chunk_size_mb:.2f}MB): {chunk_filename}")
                        
                        # Queue chunk for LLM analysis
                        chunk_file_item = FileQueueItem(
                            file_path=chunk_url,
                            repository_name=repo_name,
                            job_id=job_id,
                            chunk_id=str(i)
                        )
                        self.file_queue.put(chunk_file_item)
                        
                        # Clean up local chunk file
                        os.unlink(chunk_path)
                    
                    logger.info(f"Chunking Worker {self.worker_id}: {repo_name} - all {len(chunk_paths)} chunks uploaded and queued for LLM")
                
                return True
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            
        except Exception as e:
            logger.error(f"Chunking Worker {self.worker_id}: Failed to process {repo_name}: {e}", exc_info=True)
            return False
    
    def run(self):
        """
        Main worker loop.
        
        Continuously processes files from repomix_validated_queue.
        """
        logger.info(f"Chunking Worker {self.worker_id}: Starting worker loop")
        
        while True:
            try:
                # Get file item from repomix validated queue
                logger.info(f"Chunking Worker {self.worker_id}: Waiting for files to process...")
                file_item = self.repomix_validated_queue.get(timeout=30)
                
                if file_item is None:
                    # Sentinel value - shutdown worker
                    logger.info(f"Chunking Worker {self.worker_id}: Received shutdown signal")
                    break
                
                # Process the file
                success = self.process_file_for_chunking(file_item)
                
                if success:
                    # Mark task as completed
                    self.repomix_validated_queue.task_done(file_item)
                    logger.info(f"Chunking Worker {self.worker_id}: Successfully processed file")
                else:
                    # Mark task as failed
                    self.repomix_validated_queue.task_failed(file_item, "Chunking processing failed")
                    logger.error(f"Chunking Worker {self.worker_id}: Failed to process file")
                
            except Exception as e:
                logger.error(f"Chunking Worker {self.worker_id}: Error in worker loop: {e}", exc_info=True)
                # Continue processing other items
                continue
        
        logger.info(f"Chunking Worker {self.worker_id}: Worker stopped")


def run_chunking_worker(worker_id: str, config: ScraperConfig):
    """
    Entry point for running a chunking worker.
    
    Args:
        worker_id: Unique identifier for this worker
        config: Configuration object
    """
    worker = ChunkingWorker(worker_id, config)
    worker.run()


if __name__ == "__main__":
    # For testing purposes
    import sys
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    
    from config import ScraperConfig

    config = ScraperConfig()
    run_chunking_worker("chunking_test", config)
