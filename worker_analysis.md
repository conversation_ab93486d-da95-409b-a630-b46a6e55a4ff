This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/pipeline.py, src/queue_manager.py, src/local_queue.py, src/redis_queue.py, run_exact_test.py, test_complete_workflow.py
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
run_exact_test.py
src/local_queue.py
src/pipeline.py
src/queue_manager.py
src/redis_queue.py
test_complete_workflow.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="run_exact_test.py">
#!/usr/bin/env python3
"""
EXACT TEST AS REQUESTED:
- Keywords: "proxy list", "free proxy", "proxy scrape"
- Min stars: 30  
- Max repos per keyword: 60
- Expected total: ~180 repositories
- Sorted by last updated (fixed)
- Full unified architecture with local queues (Redis alternative)
- Proper logging files
- Merged output to single JSON file
- Supabase storage integration
"""

import os
import sys
import uuid
import json
import logging
import threading
import time
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from github_search import search_repositories
from config import ScraperConfig
from queue_manager import get_queue_manager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord
from pipeline import repomix_worker, llm_worker
import multiprocessing

def setup_logging(output_dir: Path) -> logging.Logger:
    """Set up proper logging files."""
    
    # Create logs directory
    logs_dir = output_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Main log file
    main_log = logs_dir / "main.log"
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(main_log),
            logging.StreamHandler()
        ]
    )
    
    # Detailed debug log
    debug_log = logs_dir / "debug.log"
    debug_handler = logging.FileHandler(debug_log)
    debug_handler.setLevel(logging.DEBUG)
    debug_handler.setFormatter(logging.Formatter(log_format))
    
    # Get root logger and add debug handler
    root_logger = logging.getLogger()
    root_logger.addHandler(debug_handler)
    root_logger.setLevel(logging.DEBUG)
    
    logger = logging.getLogger(__name__)
    logger.info(f"📝 Logging configured - Main: {main_log}, Debug: {debug_log}")
    
    return logger

def run_workers(config, job_id: str, num_repomix_workers: int = 3, num_llm_workers: int = 2):
    """Run worker processes to handle the queued repositories."""
    
    logger = logging.getLogger(__name__)
    logger.info(f"🔧 Starting {num_repomix_workers} repomix workers and {num_llm_workers} LLM workers")
    
    # Convert config to dict for multiprocessing
    config_dict = {
        'GITHUB_TOKEN': config.GITHUB_TOKEN,
        'LLM_API_KEY': config.LLM_API_KEY,  # Fixed: Use LLM_API_KEY not GEMINI_API_KEY
        'LLM_BASE_URL': config.LLM_BASE_URL,
        'LLM_MODEL': config.LLM_MODEL,
        'LLM_MAX_OUTPUT_TOKENS': config.LLM_MAX_OUTPUT_TOKENS,
        'LLM_TIMEOUT': config.LLM_TIMEOUT,
        'SUPABASE_URL': config.SUPABASE_URL,
        'SUPABASE_KEY': config.SUPABASE_KEY,
        'REDIS_URL': getattr(config, 'REDIS_URL', 'local://memory')
    }
    
    # Start repomix workers
    repomix_processes = []
    for i in range(num_repomix_workers):
        p = multiprocessing.Process(
            target=repomix_worker,
            args=(f"repomix_worker_{i}", config_dict, job_id)
        )
        p.start()
        repomix_processes.append(p)
        logger.info(f"🔧 Started repomix worker {i}")
    
    # Start LLM workers  
    llm_processes = []
    for i in range(num_llm_workers):
        p = multiprocessing.Process(
            target=llm_worker,
            args=(f"llm_worker_{i}", config_dict, job_id)
        )
        p.start()
        llm_processes.append(p)
        logger.info(f"🤖 Started LLM worker {i}")
    
    return repomix_processes + llm_processes

def wait_for_completion(queue_manager, job_id: str, total_repos: int, timeout: int = 1800):
    """Wait for all repositories to be processed."""
    
    logger = logging.getLogger(__name__)
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        # Check queue status
        repo_queue = queue_manager.get_repository_queue()
        file_queue = queue_manager.get_file_queue()
        
        repo_size = repo_queue.size() if hasattr(repo_queue, 'size') else 0
        file_size = file_queue.size() if hasattr(file_queue, 'size') else 0
        
        # Check Supabase for completed analyses
        try:
            supabase_client = create_supabase_client()
            completed = supabase_client.get_completed_analyses_count(job_id)
            
            logger.info(f"📊 Progress: {completed}/{total_repos} completed, "
                       f"repo_queue: {repo_size}, file_queue: {file_size}")
            
            if completed >= total_repos:
                logger.info("🎉 All repositories processed!")
                return True
                
        except Exception as e:
            logger.error(f"Error checking progress: {e}")
        
        time.sleep(10)  # Check every 10 seconds
    
    logger.warning(f"⏰ Timeout reached after {timeout} seconds")
    return False

def create_final_analysis(job_id: str, output_dir: Path) -> str:
    """Create the final merged analysis JSON file."""
    
    logger = logging.getLogger(__name__)
    logger.info("📋 Creating final merged analysis file...")
    
    try:
        supabase_client = create_supabase_client()
        
        # Get all analyses for this job
        analyses = supabase_client.get_job_analyses(job_id)
        repositories = supabase_client.get_job_repositories(job_id)
        
        # Create comprehensive analysis
        final_analysis = {
            "metadata": {
                "job_id": job_id,
                "generated": datetime.now().isoformat(),
                "keywords": "proxy list,free proxy,proxy scrape",
                "min_stars": 30,
                "max_repos_per_keyword": 60,
                "total_repositories": len(repositories),
                "successful_analyses": len(analyses),
                "processing_mode": "unified_architecture",
                "sorted_by": "last_updated"
            },
            "summary": {
                "repositories_found": len(repositories),
                "analyses_completed": len(analyses),
                "success_rate": len(analyses) / len(repositories) * 100 if repositories else 0,
                "languages": {},
                "average_stars": sum(r.get('stars', 0) for r in repositories) / len(repositories) if repositories else 0,
                "total_analysis_size": sum(len(a.get('analysis_content', '')) for a in analyses)
            },
            "repositories": repositories,
            "analyses": analyses
        }
        
        # Calculate language distribution
        for repo in repositories:
            lang = repo.get('language', 'Unknown')
            final_analysis["summary"]["languages"][lang] = final_analysis["summary"]["languages"].get(lang, 0) + 1
        
        # Save final analysis
        final_file = output_dir / "final_merged_analysis.json"
        with open(final_file, 'w', encoding='utf-8') as f:
            json.dump(final_analysis, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Final analysis saved: {final_file}")
        return str(final_file)
        
    except Exception as e:
        logger.error(f"❌ Failed to create final analysis: {e}")
        return None

def main():
    """Run the EXACT test as specified."""
    
    print("🚀 EXACT REPOSITORY RESEARCH TEST")
    print("=" * 60)
    print("SPECIFICATIONS:")
    print("   Keywords: 'proxy list', 'free proxy', 'proxy scrape'")
    print("   Min stars: 30")
    print("   Max repos per keyword: 60") 
    print("   Expected total: ~180 repositories")
    print("   Sorting: By last updated")
    print("   Architecture: Unified with local queues")
    print("   Output: Merged to single JSON file")
    print("   Storage: Supabase integration")
    print("   Logging: Proper log files")
    print()
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
    output_dir = Path(f"output/exact_test_{timestamp}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set up logging
    logger = setup_logging(output_dir)
    
    try:
        # Load configuration
        config = ScraperConfig()
        logger.info("✅ Configuration loaded")
        
        # Initialize queue manager (will use local queues)
        queue_manager = get_queue_manager()
        logger.info(f"✅ Queue manager initialized: {queue_manager.get_queue_type()}")
        
        # Search for repositories with EXACT parameters
        logger.info("🔍 Searching GitHub repositories...")
        repositories = search_repositories(
            keywords="proxy list,free proxy,proxy scrape",
            min_stars=30,
            max_repos=60  # This is per keyword
        )
        
        logger.info(f"✅ Found {len(repositories)} repositories")
        
        if not repositories:
            logger.error("❌ No repositories found")
            return False
        
        # Create job in Supabase
        job_id = str(uuid.uuid4())
        logger.info(f"📝 Creating job: {job_id}")
        
        supabase_client = create_supabase_client()
        job_record = JobRecord(
            id=job_id,
            keywords="proxy list,free proxy,proxy scrape",
            min_stars=30,
            max_repos=60,
            status="running",
            created_at=datetime.now().isoformat(),
            custom_prompt=None,
            debug=True
        )
        supabase_client.create_job(job_record)
        
        # Queue all repositories
        logger.info(f"📤 Queuing {len(repositories)} repositories...")
        repo_queue = queue_manager.get_repository_queue()
        
        for repo in repositories:
            # Create repository record
            repo_record = RepositoryRecord(
                id=str(uuid.uuid4()),
                job_id=job_id,
                name=repo['full_name'],
                url=repo['html_url'],
                stars=repo['stars'],
                file_size_mb=0.0,
                analysis_status="pending"
            )
            supabase_client.create_repository(repo_record)
            
            # Queue for processing
            queue_item = RepositoryQueueItem(
                repository_name=repo['full_name'],
                repository_url=repo['html_url'],
                job_id=job_id
            )
            repo_queue.put(queue_item.to_dict())
        
        logger.info(f"✅ All {len(repositories)} repositories queued")
        
        # Start workers
        logger.info("🔧 Starting worker processes...")
        worker_processes = run_workers(config, job_id)
        
        # Wait for completion
        logger.info("⏳ Waiting for processing to complete...")
        success = wait_for_completion(queue_manager, job_id, len(repositories))
        
        # Create final merged analysis
        if success:
            final_file = create_final_analysis(job_id, output_dir)
            
            logger.info("🎉 EXACT TEST COMPLETED SUCCESSFULLY!")
            logger.info(f"📊 Results:")
            logger.info(f"   Repositories processed: {len(repositories)}")
            logger.info(f"   Final analysis: {final_file}")
            logger.info(f"   Logs directory: {output_dir / 'logs'}")
            logger.info(f"   Job ID: {job_id}")
            
            return True
        else:
            logger.error("❌ Test did not complete successfully")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        # Clean up worker processes
        try:
            for p in worker_processes:
                if p.is_alive():
                    p.terminate()
                    p.join(timeout=5)
        except:
            pass

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
</file>

<file path="src/local_queue.py">
#!/usr/bin/env python3
"""
Local in-memory queue implementation that mimics Redis functionality
for local development and testing. For production, we use GCP Redis.
"""

import json
import time
import threading
from typing import Dict, List, Any, Optional
from collections import deque
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class QueueItem:
    """Base class for queue items."""
    id: str
    created_at: float
    data: Dict[str, Any]

class LocalQueue:
    """Thread-safe local queue that mimics Redis queue functionality."""
    
    def __init__(self, name: str):
        self.name = name
        self._queue = deque()
        self._processing = {}  # Items currently being processed
        self._lock = threading.Lock()
        self._stats = {
            'total_added': 0,
            'total_processed': 0,
            'total_failed': 0
        }
    
    def put(self, item: Dict[str, Any], timeout: Optional[float] = None) -> bool:
        """Add item to queue."""
        with self._lock:
            queue_item = QueueItem(
                id=f"{self.name}_{int(time.time() * 1000)}_{len(self._queue)}",
                created_at=time.time(),
                data=item
            )
            self._queue.append(queue_item)
            self._stats['total_added'] += 1
            logger.debug(f"📤 Added item to {self.name} queue: {queue_item.id}")
            return True
    
    def get(self, timeout: Optional[float] = None) -> Optional['QueueItem']:
        """Get item from queue (blocking if timeout specified)."""
        start_time = time.time()

        while True:
            with self._lock:
                if self._queue:
                    item = self._queue.popleft()
                    # Move to processing
                    self._processing[item.id] = {
                        'item': item,
                        'started_at': time.time()
                    }
                    logger.debug(f"📥 Got item from {self.name} queue: {item.id}")
                    return item  # Return the QueueItem object itself, not just data
            
            # If no timeout, return None immediately
            if timeout is None:
                return None
            
            # Check timeout
            if time.time() - start_time >= timeout:
                return None
            
            # Wait a bit before checking again
            time.sleep(0.1)
    
    def ack(self, item_data: Dict[str, Any]) -> bool:
        """Acknowledge successful processing of item."""
        with self._lock:
            # Find the item in processing by matching data
            for item_id, proc_info in list(self._processing.items()):
                if proc_info['item'].data == item_data:
                    del self._processing[item_id]
                    self._stats['total_processed'] += 1
                    logger.debug(f"✅ Acknowledged item from {self.name} queue: {item_id}")
                    return True
            return False

    def task_done(self, item: 'QueueItem') -> bool:
        """Mark task as completed (for compatibility with Redis queue interface)."""
        return self.ack(item.data)

    def task_failed(self, item: 'QueueItem', error_message: str) -> bool:
        """Mark task as failed (for compatibility with Redis queue interface)."""
        with self._lock:
            # Find and remove from processing
            for item_id, proc_info in list(self._processing.items()):
                if proc_info['item'].data == item.data:
                    del self._processing[item_id]
                    self._stats['total_failed'] += 1
                    logger.warning(f"❌ Task failed for {self.name} queue: {item_id} - {error_message}")
                    # Could implement retry logic here if needed
                    return True
            return False
    
    def nack(self, item_data: Dict[str, Any], requeue: bool = True) -> bool:
        """Negative acknowledge - item failed processing."""
        with self._lock:
            # Find the item in processing
            for item_id, proc_info in list(self._processing.items()):
                if proc_info['item'].data == item_data:
                    item = proc_info['item']
                    del self._processing[item_id]
                    self._stats['total_failed'] += 1
                    
                    if requeue:
                        # Add back to front of queue for retry
                        self._queue.appendleft(item)
                        logger.debug(f"🔄 Requeued failed item in {self.name} queue: {item_id}")
                    else:
                        logger.debug(f"❌ Failed item removed from {self.name} queue: {item_id}")
                    return True
            return False
    
    def size(self) -> int:
        """Get queue size."""
        with self._lock:
            return len(self._queue)
    
    def processing_count(self) -> int:
        """Get count of items currently being processed."""
        with self._lock:
            return len(self._processing)
    
    def stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        with self._lock:
            return {
                'name': self.name,
                'queue_size': len(self._queue),
                'processing_count': len(self._processing),
                **self._stats
            }
    
    def clear(self) -> None:
        """Clear all items from queue."""
        with self._lock:
            self._queue.clear()
            self._processing.clear()
            logger.info(f"🗑️ Cleared {self.name} queue")

class LocalQueueManager:
    """Manages multiple local queues, mimicking Redis functionality."""
    
    def __init__(self):
        self._queues: Dict[str, LocalQueue] = {}
        self._lock = threading.Lock()
        logger.info("🔧 Initialized local queue manager (Redis alternative)")
    
    def get_queue(self, name: str) -> LocalQueue:
        """Get or create a queue by name."""
        with self._lock:
            if name not in self._queues:
                self._queues[name] = LocalQueue(name)
                logger.info(f"📋 Created local queue: {name}")
            return self._queues[name]
    
    def get_repository_queue(self) -> LocalQueue:
        """Get the repository processing queue."""
        return self.get_queue('repositories')
    
    def get_file_queue(self) -> LocalQueue:
        """Get the file processing queue."""
        return self.get_queue('files')
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all queues."""
        with self._lock:
            return {
                'queue_manager': 'local',
                'total_queues': len(self._queues),
                'queues': {name: queue.stats() for name, queue in self._queues.items()}
            }
    
    def clear_all(self) -> None:
        """Clear all queues."""
        with self._lock:
            for queue in self._queues.values():
                queue.clear()
            logger.info("🗑️ Cleared all local queues")

# Queue item classes for type safety
@dataclass
class RepositoryQueueItem:
    """Repository queue item."""
    repository_name: str
    repository_url: str
    job_id: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RepositoryQueueItem':
        return cls(**data)

@dataclass  
class FileQueueItem:
    """File queue item."""
    file_url: str
    repository_name: str
    job_id: str
    file_size: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileQueueItem':
        return cls(**data)

# Global instance for easy access
_local_queue_manager = None

def get_local_queue_manager() -> LocalQueueManager:
    """Get the global local queue manager instance."""
    global _local_queue_manager
    if _local_queue_manager is None:
        _local_queue_manager = LocalQueueManager()
    return _local_queue_manager

def create_queue_manager(redis_url: Optional[str] = None) -> LocalQueueManager:
    """
    Create appropriate queue manager based on environment.
    For local development: LocalQueueManager
    For GCP production: Would use Redis/Cloud Tasks
    """
    if redis_url and redis_url != 'redis://localhost:6379':
        # In production, we'd use actual Redis
        logger.info(f"🌐 Would use Redis at: {redis_url}")
        # For now, fall back to local for development
        logger.info("🔧 Using local queue manager for development")
        return get_local_queue_manager()
    else:
        logger.info("🔧 Using local queue manager for development")
        return get_local_queue_manager()
</file>

<file path="src/queue_manager.py">
#!/usr/bin/env python3
"""
Unified queue manager that automatically chooses between Redis and local queues.
For local development: Uses in-memory queues
For GCP production: Uses Redis/Cloud Tasks
"""

import os
import logging
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

# Try to import Redis, fall back to local if not available
try:
    import redis
    from redis_queue import QueueManager as RedisQueueManager, RepositoryQueueItem, FileQueueItem
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from local_queue import LocalQueueManager, RepositoryQueueItem, FileQueueItem

class UnifiedQueueManager:
    """
    Unified queue manager that automatically chooses the best queue implementation.
    
    Priority:
    1. Redis (if available and configured)
    2. Local in-memory queues (for development)
    """
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379')
        self._manager = None
        self._queue_type = None
        
        self._initialize_queue_manager()
    
    def _initialize_queue_manager(self):
        """Initialize the appropriate queue manager."""
        
        # Try Redis first if available
        if REDIS_AVAILABLE and self._try_redis():
            logger.info(f"🌐 Using Redis queue manager: {self.redis_url}")
            self._manager = RedisQueueManager(self.redis_url)
            self._queue_type = "redis"
            return
        
        # Fall back to local queues
        logger.info("🔧 Using local in-memory queue manager")
        self._manager = LocalQueueManager()
        self._queue_type = "local"
    
    def _try_redis(self) -> bool:
        """Test if Redis is available and working."""
        try:
            client = redis.from_url(self.redis_url, socket_timeout=5)
            client.ping()
            return True
        except Exception as e:
            logger.debug(f"Redis not available: {e}")
            return False
    
    def get_repository_queue(self):
        """Get the repository processing queue."""
        return self._manager.get_repository_queue()
    
    def get_file_queue(self):
        """Get the file processing queue."""
        return self._manager.get_file_queue()
    
    def get_queue_type(self) -> str:
        """Get the type of queue manager being used."""
        return self._queue_type
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        if hasattr(self._manager, 'get_all_stats'):
            stats = self._manager.get_all_stats()
        else:
            stats = {"queue_type": self._queue_type}

        stats["queue_manager_type"] = self._queue_type
        return stats

    def get_all_stats(self) -> Dict[str, Any]:
        """Get all queue statistics (alias for get_stats for compatibility)."""
        return self.get_stats()
    
    def is_redis(self) -> bool:
        """Check if using Redis queues."""
        return self._queue_type == "redis"
    
    def is_local(self) -> bool:
        """Check if using local queues."""
        return self._queue_type == "local"

# Global instance
_unified_queue_manager = None

def get_queue_manager(redis_url: Optional[str] = None) -> UnifiedQueueManager:
    """Get the global unified queue manager instance."""
    global _unified_queue_manager
    if _unified_queue_manager is None:
        _unified_queue_manager = UnifiedQueueManager(redis_url)
    return _unified_queue_manager

def create_queue_manager(redis_url: Optional[str] = None) -> UnifiedQueueManager:
    """Create a new unified queue manager instance."""
    return UnifiedQueueManager(redis_url)

# For backward compatibility, export the queue item classes
__all__ = [
    'UnifiedQueueManager',
    'get_queue_manager', 
    'create_queue_manager',
    'RepositoryQueueItem',
    'FileQueueItem'
]
</file>

<file path="test_complete_workflow.py">
#!/usr/bin/env python3
"""
Test the complete workflow with just 3 repositories to verify everything works
and get the analysis file quickly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Run a small complete workflow test."""
    print("🚀 COMPLETE WORKFLOW TEST - 3 REPOSITORIES")
    print("=" * 60)
    
    # Test with very small parameters for quick completion
    test_params = {
        'keywords': 'python test',
        'min_stars': 1000,
        'max_repos': 3  # Very small for quick test
    }
    
    print(f"🔍 Test Parameters:")
    print(f"   Keywords: {test_params['keywords']}")
    print(f"   Min stars: {test_params['min_stars']}")
    print(f"   Max repos: {test_params['max_repos']}")
    print()
    
    try:
        # Import main function
        from main import main as run_main
        import sys
        
        # Set up arguments
        original_argv = sys.argv.copy()
        sys.argv = [
            'main.py',
            '--keywords', test_params['keywords'],
            '--min-stars', str(test_params['min_stars']),
            '--max-repos', str(test_params['max_repos']),
            '--debug'
        ]
        
        print("📋 Running main pipeline...")
        print(f"   Command: {' '.join(sys.argv)}")
        print()
        
        # Run the main pipeline
        result = run_main()
        
        # Restore original argv
        sys.argv = original_argv
        
        if result:
            print("\n🎉 COMPLETE WORKFLOW TEST SUCCESSFUL!")
            print("✅ Check output directory for analysis files")
            print("✅ Check Supabase for database records")
        else:
            print("\n❌ Workflow test failed")
            
        return result
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
</file>

<file path="src/redis_queue.py">
"""
Redis-based distributed queue implementation.
Replaces in-memory JoinableQueue with Redis-backed queues for cloud deployment.
"""

import json
import time
import redis
import logging
from typing import Any, Dict, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class QueueItem:
    """Base class for queue items with metadata."""
    id: str
    created_at: str
    data: Dict[str, Any]
    retries: int = 0
    max_retries: int = 3

    def to_json(self) -> str:
        """Convert to JSON string for Redis storage."""
        return json.dumps(asdict(self))
    
    @classmethod
    def from_json(cls, json_str: str) -> 'QueueItem':
        """Create from JSON string."""
        data = json.loads(json_str)
        return cls(**data)

class RepositoryQueueItem(QueueItem):
    """Queue item for repository processing tasks."""

    def __init__(self, repository_url: str, repository_name: str, job_id: str,
                 item_id: Optional[str] = None, retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"repo_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "repository_url": repository_url,
                "repository_name": repository_name,
                "job_id": job_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.repository_url = repository_url
        self.repository_name = repository_name
        self.job_id = job_id

class FileQueueItem(QueueItem):
    """Queue item for LLM processing tasks."""

    def __init__(self, file_path: str, repository_name: str, job_id: str,
                 chunk_id: Optional[str] = None, item_id: Optional[str] = None,
                 retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"file_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "file_path": file_path,
                "repository_name": repository_name,
                "job_id": job_id,
                "chunk_id": chunk_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.file_path = file_path
        self.repository_name = repository_name
        self.job_id = job_id
        self.chunk_id = chunk_id

class RedisQueue:
    """Redis-backed distributed queue implementation."""
    
    def __init__(self, redis_url: str, queue_name: str, 
                 processing_timeout: int = 300):
        """
        Initialize Redis queue.
        
        Args:
            redis_url: Redis connection URL
            queue_name: Name of the queue
            processing_timeout: Timeout for processing items (seconds)
        """
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.queue_name = queue_name
        self.processing_queue = f"{queue_name}:processing"
        self.failed_queue = f"{queue_name}:failed"
        self.processing_timeout = processing_timeout
        
        # Test connection
        try:
            self.redis_client.ping()
            logger.info(f"Connected to Redis queue: {queue_name}")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def put(self, item: QueueItem) -> None:
        """Add item to queue."""
        try:
            self.redis_client.lpush(self.queue_name, item.to_json())
            logger.debug(f"Added item {item.id} to queue {self.queue_name}")
        except Exception as e:
            logger.error(f"Failed to add item to queue: {e}")
            raise
    
    def get(self, timeout: Optional[int] = None) -> Optional[QueueItem]:
        """
        Get item from queue (blocking).
        
        Args:
            timeout: Timeout in seconds (None for indefinite)
            
        Returns:
            QueueItem or None if timeout
        """
        try:
            # Use BRPOPLPUSH for atomic move to processing queue
            result = self.redis_client.brpoplpush(
                self.queue_name, 
                self.processing_queue, 
                timeout=timeout or 0
            )
            
            if result:
                item = QueueItem.from_json(result)
                # Add processing timestamp
                self.redis_client.hset(
                    f"{self.processing_queue}:meta:{item.id}",
                    "started_at", 
                    datetime.now().isoformat()
                )
                logger.debug(f"Retrieved item {item.id} from queue {self.queue_name}")
                return item
            return None
            
        except Exception as e:
            logger.error(f"Failed to get item from queue: {e}")
            raise
    
    def task_done(self, item: QueueItem) -> None:
        """Mark task as completed."""
        try:
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            # Remove metadata
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            logger.debug(f"Marked item {item.id} as done")
        except Exception as e:
            logger.error(f"Failed to mark task as done: {e}")
            raise
    
    def task_failed(self, item: QueueItem, error: str) -> None:
        """Mark task as failed and handle retry logic."""
        try:
            item.retries += 1
            
            if item.retries < item.max_retries:
                # Retry: put back in main queue
                self.redis_client.lpush(self.queue_name, item.to_json())
                logger.warning(f"Retrying item {item.id} (attempt {item.retries})")
            else:
                # Max retries reached: move to failed queue
                failed_item = {
                    "item": asdict(item),
                    "error": error,
                    "failed_at": datetime.now().isoformat()
                }
                self.redis_client.lpush(self.failed_queue, json.dumps(failed_item))
                logger.error(f"Item {item.id} failed permanently: {error}")
            
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            
        except Exception as e:
            logger.error(f"Failed to handle task failure: {e}")
            raise
    
    def size(self) -> int:
        """Get queue size."""
        return self.redis_client.llen(self.queue_name)
    
    def processing_size(self) -> int:
        """Get processing queue size."""
        return self.redis_client.llen(self.processing_queue)
    
    def failed_size(self) -> int:
        """Get failed queue size."""
        return self.redis_client.llen(self.failed_queue)
    
    def cleanup_stale_items(self) -> int:
        """Clean up items that have been processing too long."""
        try:
            stale_count = 0
            processing_items = self.redis_client.lrange(self.processing_queue, 0, -1)
            
            for item_json in processing_items:
                item = QueueItem.from_json(item_json)
                meta_key = f"{self.processing_queue}:meta:{item.id}"
                started_at_str = self.redis_client.hget(meta_key, "started_at")
                
                if started_at_str:
                    started_at = datetime.fromisoformat(started_at_str)
                    if datetime.now() - started_at > timedelta(seconds=self.processing_timeout):
                        # Item is stale, move back to main queue
                        self.redis_client.lrem(self.processing_queue, 1, item_json)
                        self.redis_client.delete(meta_key)
                        self.redis_client.lpush(self.queue_name, item_json)
                        stale_count += 1
                        logger.warning(f"Moved stale item {item.id} back to queue")
            
            return stale_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup stale items: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get queue statistics."""
        return {
            "pending": self.size(),
            "processing": self.processing_size(),
            "failed": self.failed_size()
        }

class QueueManager:
    """Manages multiple Redis queues for the application."""
    
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.queues: Dict[str, RedisQueue] = {}
        # Create a shared Redis client for rate limiting and other operations
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
    
    def get_queue(self, queue_name: str) -> RedisQueue:
        """Get or create a queue."""
        if queue_name not in self.queues:
            self.queues[queue_name] = RedisQueue(self.redis_url, queue_name)
        return self.queues[queue_name]
    
    def get_repository_queue(self) -> RedisQueue:
        """Get the repository processing queue."""
        return self.get_queue("repositories")
    
    def get_file_queue(self) -> RedisQueue:
        """Get the file processing queue."""
        return self.get_queue("files")
    
    def cleanup_all_stale_items(self) -> Dict[str, int]:
        """Cleanup stale items in all queues."""
        results = {}
        for name, queue in self.queues.items():
            results[name] = queue.cleanup_stale_items()
        return results
    
    def get_all_stats(self) -> Dict[str, Dict[str, int]]:
        """Get statistics for all queues."""
        stats = {}
        for name, queue in self.queues.items():
            stats[name] = queue.get_stats()
        return stats
</file>

<file path="src/pipeline.py">
"""
Cloud-integrated pipeline for Repository Research Tool.
Uses Redis queues, Supabase database, and cloud storage.
"""

import os
import uuid
import time
import json
import logging
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any

from config import ScraperConfig
from redis_queue import QueueManager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord, AnalysisRecord
from storage_manager import create_storage_manager
from monitoring import RepositoryMonitor
from llm_rate_limiter import LLMRateLimiter

# Standalone worker functions that initialize their own resources

def repomix_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone repomix worker function that initializes its own resources."""
    import subprocess
    import shutil
    import tempfile
    from pathlib import Path

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from queue_manager import get_queue_manager, RepositoryQueueItem, FileQueueItem
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client
    from utils import chunk_and_retain_file, get_file_size_mb

    config = ScraperConfig()  # Loads from .env
    queue_manager = get_queue_manager(config.REDIS_URL)  # Use unified queue manager
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    logger = logging.getLogger(f'repomix_worker_{worker_id}')
    logger.info(f"Repomix Worker {worker_id} for job {job_id} started. Using queue type: {queue_manager.get_queue_type()}")

    repo_queue = queue_manager.get_repository_queue()
    file_queue = queue_manager.get_file_queue()

    while True:
        try:
            # Get repository from Redis queue
            repo_item = repo_queue.get(timeout=30)
            if repo_item is None:
                break

            repo_name = repo_item.repository_name  # Access properties directly from QueueItem
            repo_url = repo_item.repository_url   # Access properties directly from QueueItem
            logger.info(f"Repomix Worker {worker_id}: Starting {repo_name}")

            # Create a temporary local file for repomix output
            temp_dir = Path(tempfile.gettempdir()) / job_id
            temp_dir.mkdir(parents=True, exist_ok=True)
            safe_repo_name = repo_name.replace('/', '_')
            local_output_file = temp_dir / f"{safe_repo_name}.md"

            # Run the actual repomix command
            npx_path = shutil.which('npx')
            if not npx_path:
                raise RuntimeError("npx not found in PATH")

            cmd = [
                npx_path, 'repomix', '--remote', repo_url,
                '--include', config.FILE_INCLUDES,
                '--output', str(local_output_file)
            ]
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=600,
                encoding='utf-8', errors='replace'
            )

            if result.returncode != 0 or not local_output_file.exists():
                logger.error(f"Repomix Worker {worker_id}: Failed {repo_name} - {result.stderr[:200]}")
                # Clean up and continue
                if local_output_file.exists():
                    local_output_file.unlink()
                repo_queue.task_done(repo_item)
                continue

            # Apply chunking logic as per user requirements
            file_size_mb = get_file_size_mb(str(local_output_file))
            logger.info(f"Repomix Worker {worker_id}: {repo_name} file size: {file_size_mb:.2f}MB")

            # Chunk file if >3MB, keep first 3 chunks only
            chunk_paths = chunk_and_retain_file(
                str(local_output_file),
                config.MAX_FILE_SIZE_MB,
                config.MAX_CHUNKS_TO_RETAIN
            )

            uploaded_chunk_urls = []
            total_chunked_size_mb = 0.0

            # Process each chunk
            for i, chunk_path in enumerate(chunk_paths):
                with open(chunk_path, 'r', encoding='utf-8') as f:
                    chunk_content = f.read()

                chunk_size_mb = get_file_size_mb(chunk_path)
                total_chunked_size_mb += chunk_size_mb

                # Create unique filename for each chunk
                chunk_filename = f"{safe_repo_name}_chunk_{i}.md" if len(chunk_paths) > 1 else f"{safe_repo_name}.md"

                # Upload chunk to Supabase storage
                chunk_url = storage_manager.upload_repomix_file(
                    job_id, chunk_filename, chunk_content
                )
                uploaded_chunk_urls.append(chunk_url)

                # Queue each chunk for LLM analysis
                file_item = FileQueueItem(
                    file_path=chunk_url,
                    repository_name=repo_name,
                    job_id=job_id,
                    chunk_id=str(i) if len(chunk_paths) > 1 else None
                )
                file_queue.put(file_item)

                # Clean up temporary chunk file
                Path(chunk_path).unlink()

                logger.info(f"Repomix Worker {worker_id}: Uploaded chunk {i} for {repo_name} ({chunk_size_mb:.2f}MB)")

            # Update repository record in database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if repo_record:
                supabase_client.update_repository_status(
                    repo_record.id,
                    "repomix_processed",
                    repomix_file_url=uploaded_chunk_urls[0],  # First chunk URL as primary
                    file_size_mb=total_chunked_size_mb
                )

            # Clean up temporary file
            local_output_file.unlink()
            repo_queue.task_done(repo_item)

            logger.info(f"Repomix Worker {worker_id}: Completed {repo_name} - {len(chunk_paths)} chunks, {total_chunked_size_mb:.2f}MB total")

        except Exception as e:
            logger.error(f"Repomix Worker {worker_id}: Error processing repository: {e}", exc_info=True)
            if 'repo_item' in locals() and repo_item is not None:
                repo_queue.task_failed(repo_item, str(e))  # Use task_failed for proper error handling
            continue  # Continue to next item instead of breaking


def llm_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone LLM worker function that initializes its own resources."""
    import requests
    import uuid
    from datetime import datetime

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from queue_manager import get_queue_manager, FileQueueItem
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client, AnalysisRecord
    from llm_rate_limiter import LLMRateLimiter

    config = ScraperConfig()  # Loads from .env
    queue_manager = get_queue_manager(config.REDIS_URL)  # Use unified queue manager
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    # Initialize Redis client for rate limiter with fallback
    import redis
    redis_client_for_limiter = None
    if config.REDIS_URL and config.REDIS_URL != 'local://memory':
        try:
            temp_redis_client = redis.from_url(config.REDIS_URL)
            temp_redis_client.ping()
            redis_client_for_limiter = temp_redis_client
            logger.info(f"LLM Worker {worker_id}: Connected to Redis for Rate Limiter.")
        except redis.ConnectionError:
            logger.warning(f"LLM Worker {worker_id}: Redis not available for Rate Limiter, using fallback.")
    else:
        logger.info(f"LLM Worker {worker_id}: Redis URL not set or is local, using file-based rate limiter fallback.")

    logger = logging.getLogger(f'llm_worker_{worker_id}')  # Move logger before rate limiter

    rate_limiter = LLMRateLimiter(redis_client=redis_client_for_limiter)

    logger.info(f"LLM Worker {worker_id} for job {job_id} started. Using queue type: {queue_manager.get_queue_type()}")

    file_queue = queue_manager.get_file_queue()

    def build_analysis_prompt(repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(config, 'CUSTOM_PROMPT') and config.CUSTOM_PROMPT:
            return config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    while True:
        try:
            # Get file from Redis queue
            file_item = file_queue.get(timeout=30)
            if file_item is None:
                break

            repo_name = file_item.repository_name  # Access properties directly from QueueItem
            file_url = file_item.file_path        # Access properties directly from QueueItem
            chunk_id = getattr(file_item, 'chunk_id', None)  # Get chunk_id if available
            logger.info(f"LLM Worker {worker_id}: Starting analysis of {repo_name}")

            # Download repomix content from cloud storage
            repomix_content = storage_manager.download_repomix_file(file_url)
            file_size_mb = len(repomix_content.encode('utf-8')) / (1024 * 1024)

            # Wait for rate limit slot
            if not rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300):
                logger.warning(f"LLM Worker {worker_id} timed out waiting for rate limit slot")
                continue

            # Prepare and make the actual Gemini API call
            prompt = build_analysis_prompt(repo_name, repomix_content)

            data = {
                'contents': [{'parts': [{'text': prompt}]}],
                'generationConfig': {
                    'maxOutputTokens': config.LLM_MAX_OUTPUT_TOKENS,
                    'temperature': 0.1
                }
            }

            response = requests.post(
                f"{config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent?key={config.LLM_API_KEY}",
                json=data,
                timeout=config.LLM_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(f"LLM Worker {worker_id}: Failed analysis for {repo_name} - API Status: {response.status_code}")
                file_queue.task_done(file_item)
                continue

            result = response.json()
            analysis_content = result['candidates'][0]['content']['parts'][0]['text']

            # Find the corresponding repository_id from the database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if not repo_record:
                logger.error(f"LLM Worker {worker_id}: Could not find repository record for {repo_name}")
                file_queue.task_done(file_item)
                continue

            # Create analysis record
            analysis_record = AnalysisRecord(
                id=str(uuid.uuid4()),
                repository_id=repo_record.id,
                job_id=job_id,
                worker_id=str(worker_id),
                processed_at=datetime.now().isoformat(),
                analysis_content=analysis_content,
                analysis_length=len(analysis_content)
            )

            supabase_client.create_analysis(analysis_record)
            file_queue.task_done(file_item)

            logger.info(f"LLM Worker {worker_id}: Completed analysis for {repo_name}")

        except Exception as e:
            logger.error(f"LLM Worker {worker_id}: Error processing file: {e}", exc_info=True)
            if 'file_item' in locals() and file_item is not None:
                file_queue.task_failed(file_item, str(e))  # Use task_failed for proper error handling
            continue  # Continue to next item instead of breaking


logger = logging.getLogger(__name__)

class RepositoryPipeline:
    """
    Cloud-native pipeline for repository processing.
    Uses Redis for distributed queues, Supabase for persistence, and cloud storage.
    """
    
    def __init__(self, config=None, job_id=None):
        """
        Initialize the cloud pipeline.
        
        Args:
            config: Configuration object (defaults to ScraperConfig)
            job_id: Unique job identifier (generated if not provided)
        """
        self.config = config or ScraperConfig()
        self.job_id = job_id or str(uuid.uuid4())
        
        # Initialize cloud services
        self._initialize_cloud_services()
        
        # Initialize local components
        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None
        
        # Create local output directory for temporary files
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
        self.output_dir = Path(self.config.OUTPUT_BASE) / f"cloud_{timestamp_str}"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
        
        logger.info(f"Initialized cloud pipeline for job {self.job_id}")
    
    def _initialize_cloud_services(self):
        """Initialize cloud service connections."""
        try:
            # Initialize unified queue manager (handles Redis fallback to local)
            from queue_manager import get_queue_manager
            self.queue_manager = get_queue_manager(self.config.REDIS_URL)
            self.repo_queue = self.queue_manager.get_repository_queue()
            self.file_queue = self.queue_manager.get_file_queue()

            # Initialize distributed rate limiter with Redis client (if available)
            redis_client_for_limiter = None
            if hasattr(self.queue_manager, '_manager') and hasattr(self.queue_manager._manager, 'redis_client'):
                redis_client_for_limiter = self.queue_manager._manager.redis_client

            self.rate_limiter = LLMRateLimiter(
                redis_client=redis_client_for_limiter,
                rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN,
                rate_limit_requests_per_min=getattr(self.config, 'LLM_RATE_LIMIT_REQUESTS_PER_MIN', 1000)
            )

            # Initialize Supabase client
            self.supabase_client = create_supabase_client()

            # Initialize storage manager
            self.storage_manager = create_storage_manager()
            
            logger.info("Cloud services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cloud services: {e}")
            raise
    
    def create_job(self, keywords: str, min_stars: int, max_repos: int,
                   custom_prompt: Optional[str] = None, debug: bool = False) -> JobRecord:
        """
        Create a new analysis job in the database.
        
        Args:
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            custom_prompt: Optional custom LLM prompt
            debug: Debug mode flag
            
        Returns:
            Created job record
        """
        try:
            job = JobRecord(
                id=self.job_id,
                keywords=keywords,
                min_stars=min_stars,
                max_repos=max_repos,
                status="pending",
                created_at=datetime.now().isoformat(),
                custom_prompt=custom_prompt,
                debug=debug
            )
            
            created_job = self.supabase_client.create_job(job)
            logger.info(f"Created job {self.job_id} in database")
            return created_job
            
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def add_repositories(self, repositories: List[Dict[str, Any]]) -> int:
        """
        Add repositories to the processing queue and database.
        
        Args:
            repositories: List of repository information dictionaries
            
        Returns:
            Number of repositories added
        """
        try:
            added_count = 0
            
            for repo_info in repositories:
                # Create repository record in database
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=self.job_id,
                    name=repo_info.get('full_name', 'unknown'),
                    url=repo_info.get('html_url', ''),
                    stars=repo_info.get('stargazers_count', 0),
                    file_size_mb=0.0  # Will be updated after repomix processing
                )
                
                self.supabase_client.create_repository(repo_record)
                
                # Add to Redis queue
                queue_item = RepositoryQueueItem(
                    repository_url=repo_info.get('html_url', ''),
                    repository_name=repo_info.get('full_name', 'unknown'),
                    job_id=self.job_id
                )
                
                self.repo_queue.put(queue_item)
                added_count += 1
            
            # Update job with total repositories
            self.supabase_client.update_job_status(
                self.job_id, 
                "running", 
                total_repositories=added_count
            )
            
            logger.info(f"Added {added_count} repositories to queue and database")
            return added_count
            
        except Exception as e:
            logger.error(f"Failed to add repositories: {e}")
            raise
    
    def start_workers(self):
        """Start repomix and LLM worker processes."""
        try:
            # Convert config to dict for passing to workers
            config_dict = {
                'REDIS_URL': self.config.REDIS_URL,
                'SUPABASE_URL': self.config.SUPABASE_URL,
                'SUPABASE_KEY': self.config.SUPABASE_KEY,
                'LLM_API_KEY': self.config.LLM_API_KEY,
                'LLM_BASE_URL': self.config.LLM_BASE_URL,
                'LLM_MODEL': self.config.LLM_MODEL,
                'FILE_INCLUDES': self.config.FILE_INCLUDES
            }

            # Start repomix workers
            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=repomix_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.repomix_processes.append(p)

            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=llm_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.llm_processes.append(p)

            logger.info(f"Started {len(self.repomix_processes)} repomix and {len(self.llm_processes)} LLM workers")

        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            raise


    
    def wait_for_completion(self, timeout: Optional[int] = None):
        """
        Wait for all processing to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
        """
        try:
            start_time = time.time()
            
            while True:
                # Check queue statistics
                stats = self.queue_manager.get_all_stats()
                repo_pending = stats.get('repositories', {}).get('pending', 0)
                file_pending = stats.get('files', {}).get('pending', 0)
                
                if repo_pending == 0 and file_pending == 0:
                    logger.info("All processing completed")
                    break
                
                if timeout and (time.time() - start_time) > timeout:
                    logger.warning(f"Timeout reached after {timeout} seconds")
                    break
                
                logger.info(f"Waiting... Repos: {repo_pending}, Files: {file_pending}")
                time.sleep(10)
            
        except Exception as e:
            logger.error(f"Error waiting for completion: {e}")
            raise
    
    def finalize_job(self) -> str:
        """
        Finalize the job by creating the final analysis file.
        
        Returns:
            URL of the final analysis file
        """
        try:
            # Get all analyses for this job
            analyses = self.supabase_client.get_analyses_by_job(self.job_id)
            repositories = self.supabase_client.get_repositories_by_job(self.job_id)
            
            # Calculate chunking statistics
            total_chunks = len(analyses)
            chunked_repos = len([r for r in repositories if r.file_size_mb and r.file_size_mb > 3.0])

            # Create final analysis data
            analysis_data = {
                "metadata": {
                    "job_id": self.job_id,
                    "generated": datetime.now().isoformat(),
                    "total_repositories": len(repositories),
                    "total_analyses": len(analyses),
                    "total_chunks": total_chunks,
                    "chunked_repositories": chunked_repos,
                    "processing_configuration": {
                        "repomix_workers": self.config.REPOMIX_WORKERS,
                        "llm_workers": self.config.LLM_WORKERS,
                        "deployment_mode": "unified",
                        "max_file_size_mb": self.config.MAX_FILE_SIZE_MB,
                        "max_chunks_retained": self.config.MAX_CHUNKS_TO_RETAIN
                    }
                },
                "analyses": []
            }
            
            # Group analyses by repository to handle chunks
            repo_analyses_map = {}
            for analysis in analyses:
                repo_id = analysis.repository_id
                if repo_id not in repo_analyses_map:
                    repo_analyses_map[repo_id] = []
                repo_analyses_map[repo_id].append(analysis)

            # Add grouped analyses to final data
            for repo in repositories:
                repo_analyses = repo_analyses_map.get(repo.id, [])

                if repo_analyses:
                    # Sort analyses by chunk_id (None values first for non-chunked)
                    repo_analyses.sort(key=lambda x: int(x.chunk_id) if x.chunk_id else 0)

                    # Create repository entry with all its chunks
                    repo_entry = {
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url,
                            "file_size_mb": repo.file_size_mb,
                            "total_chunks": len(repo_analyses)
                        },
                        "analysis_chunks": [],
                        "combined_analysis": ""
                    }

                    # Add each chunk analysis
                    combined_content = []
                    for analysis in repo_analyses:
                        chunk_entry = {
                            "chunk_id": analysis.chunk_id,
                            "worker_id": analysis.worker_id,
                            "processed_at": analysis.processed_at,
                            "analysis_length": analysis.analysis_length,
                            "content": analysis.analysis_content
                        }
                        repo_entry["analysis_chunks"].append(chunk_entry)
                        combined_content.append(analysis.analysis_content)

                    # Create combined analysis for easy reading
                    repo_entry["combined_analysis"] = "\n\n--- CHUNK SEPARATOR ---\n\n".join(combined_content)

                    analysis_data["analyses"].append(repo_entry)
                else:
                    # Repository without analysis (failed processing)
                    analysis_data["analyses"].append({
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url,
                            "file_size_mb": repo.file_size_mb,
                            "total_chunks": 0
                        },
                        "analysis_chunks": [],
                        "combined_analysis": "No analysis available - processing may have failed"
                    })
            
            # Upload final analysis file
            analysis_url = self.storage_manager.upload_analysis_file(
                job_id=self.job_id,
                analysis_data=analysis_data,
                filename="final_analysis.json"
            )
            
            # Update job status
            self.supabase_client.update_job_status(
                self.job_id,
                "completed",
                analysis_file_url=analysis_url
            )
            
            logger.info(f"Job {self.job_id} finalized with analysis file: {analysis_url}")
            return analysis_url
            
        except Exception as e:
            logger.error(f"Failed to finalize job: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise

    def _build_analysis_prompt(self, repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(self.config, 'CUSTOM_PROMPT') and self.config.CUSTOM_PROMPT:
            return self.config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    def stop_workers(self):
        """Stop all worker processes."""
        try:
            # Send poison pills to stop workers
            for _ in range(len(self.repomix_processes)):
                self.repo_queue.put(None)
            
            for _ in range(len(self.llm_processes)):
                self.file_queue.put(None)
            
            # Wait for processes to finish
            for p in self.repomix_processes + self.llm_processes:
                p.join(timeout=10)
                if p.is_alive():
                    p.terminate()
            
            logger.info("All workers stopped")
            
        except Exception as e:
            logger.error(f"Error stopping workers: {e}")
    
    def run(self, repositories: List[Dict[str, Any]], keywords: str, 
            min_stars: int, max_repos: int, **kwargs) -> str:
        """
        Run the complete cloud pipeline.
        
        Args:
            repositories: List of repository information
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            **kwargs: Additional job parameters
            
        Returns:
            URL of the final analysis file
        """
        try:
            # Create job
            self.create_job(keywords, min_stars, max_repos, **kwargs)
            
            # Add repositories
            self.add_repositories(repositories)
            
            # Start workers
            self.start_workers()
            
            # Wait for completion
            self.wait_for_completion(timeout=3600)  # 1 hour timeout
            
            # Finalize job
            analysis_url = self.finalize_job()
            
            return analysis_url
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
        finally:
            # Always stop workers
            self.stop_workers()
</file>

</files>
