#!/usr/bin/env python3
"""
Local in-memory queue implementation that mimics Redis functionality
for local development and testing. For production, we use GCP Redis.
"""

import json
import time
import threading
from typing import Dict, List, Any, Optional
from collections import deque
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class QueueItem:
    """Base class for queue items."""
    id: str
    created_at: float
    data: Dict[str, Any]

class LocalQueue:
    """Thread-safe local queue that mimics Redis queue functionality."""
    
    def __init__(self, name: str):
        self.name = name
        self._queue = deque()
        self._processing = {}  # Items currently being processed
        self._lock = threading.Lock()
        self._stats = {
            'total_added': 0,
            'total_processed': 0,
            'total_failed': 0
        }
    
    def put(self, item: Dict[str, Any], timeout: Optional[float] = None) -> bool:
        """Add item to queue."""
        with self._lock:
            queue_item = QueueItem(
                id=f"{self.name}_{int(time.time() * 1000)}_{len(self._queue)}",
                created_at=time.time(),
                data=item
            )
            self._queue.append(queue_item)
            self._stats['total_added'] += 1
            logger.debug(f"📤 Added item to {self.name} queue: {queue_item.id}")
            return True
    
    def get(self, timeout: Optional[float] = None) -> Optional['QueueItem']:
        """Get item from queue (blocking if timeout specified)."""
        start_time = time.time()

        while True:
            with self._lock:
                if self._queue:
                    item = self._queue.popleft()
                    # Move to processing
                    self._processing[item.id] = {
                        'item': item,
                        'started_at': time.time()
                    }
                    logger.debug(f"📥 Got item from {self.name} queue: {item.id}")
                    return item  # Return the QueueItem object itself, not just data
            
            # If no timeout, return None immediately
            if timeout is None:
                return None
            
            # Check timeout
            if time.time() - start_time >= timeout:
                return None
            
            # Wait a bit before checking again
            time.sleep(0.1)
    
    def ack(self, item_data: Dict[str, Any]) -> bool:
        """Acknowledge successful processing of item."""
        with self._lock:
            # Find the item in processing by matching data
            for item_id, proc_info in list(self._processing.items()):
                if proc_info['item'].data == item_data:
                    del self._processing[item_id]
                    self._stats['total_processed'] += 1
                    logger.debug(f"✅ Acknowledged item from {self.name} queue: {item_id}")
                    return True
            return False

    def task_done(self, item: 'QueueItem') -> bool:
        """Mark task as completed (for compatibility with Redis queue interface)."""
        return self.ack(item.data)

    def task_failed(self, item: 'QueueItem', error_message: str) -> bool:
        """Mark task as failed (for compatibility with Redis queue interface)."""
        with self._lock:
            # Find and remove from processing
            for item_id, proc_info in list(self._processing.items()):
                if proc_info['item'].data == item.data:
                    del self._processing[item_id]
                    self._stats['total_failed'] += 1
                    logger.warning(f"❌ Task failed for {self.name} queue: {item_id} - {error_message}")
                    # Could implement retry logic here if needed
                    return True
            return False
    
    def nack(self, item_data: Dict[str, Any], requeue: bool = True) -> bool:
        """Negative acknowledge - item failed processing."""
        with self._lock:
            # Find the item in processing
            for item_id, proc_info in list(self._processing.items()):
                if proc_info['item'].data == item_data:
                    item = proc_info['item']
                    del self._processing[item_id]
                    self._stats['total_failed'] += 1
                    
                    if requeue:
                        # Add back to front of queue for retry
                        self._queue.appendleft(item)
                        logger.debug(f"🔄 Requeued failed item in {self.name} queue: {item_id}")
                    else:
                        logger.debug(f"❌ Failed item removed from {self.name} queue: {item_id}")
                    return True
            return False
    
    def size(self) -> int:
        """Get queue size."""
        with self._lock:
            return len(self._queue)
    
    def processing_count(self) -> int:
        """Get count of items currently being processed."""
        with self._lock:
            return len(self._processing)
    
    def stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        with self._lock:
            return {
                'name': self.name,
                'queue_size': len(self._queue),
                'processing_count': len(self._processing),
                **self._stats
            }
    
    def clear(self) -> None:
        """Clear all items from queue."""
        with self._lock:
            self._queue.clear()
            self._processing.clear()
            logger.info(f"🗑️ Cleared {self.name} queue")

class LocalQueueManager:
    """Manages multiple local queues, mimicking Redis functionality."""
    
    def __init__(self):
        self._queues: Dict[str, LocalQueue] = {}
        self._lock = threading.Lock()
        logger.info("🔧 Initialized local queue manager (Redis alternative)")
    
    def get_queue(self, name: str) -> LocalQueue:
        """Get or create a queue by name."""
        with self._lock:
            if name not in self._queues:
                self._queues[name] = LocalQueue(name)
                logger.info(f"📋 Created local queue: {name}")
            return self._queues[name]
    
    def get_repository_queue(self) -> LocalQueue:
        """Get the repository processing queue."""
        return self.get_queue('repositories')
    
    def get_file_queue(self) -> LocalQueue:
        """Get the file processing queue."""
        return self.get_queue('files')

    def get_repomix_validated_queue(self) -> LocalQueue:
        """Get the repomix validated queue for chunking workflow."""
        return self.get_queue('repomix_validated')
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all queues."""
        with self._lock:
            return {
                'queue_manager': 'local',
                'total_queues': len(self._queues),
                'queues': {name: queue.stats() for name, queue in self._queues.items()}
            }
    
    def clear_all(self) -> None:
        """Clear all queues."""
        with self._lock:
            for queue in self._queues.values():
                queue.clear()
            logger.info("🗑️ Cleared all local queues")

# Queue item classes for type safety
@dataclass
class RepositoryQueueItem:
    """Repository queue item."""
    repository_name: str
    repository_url: str
    job_id: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RepositoryQueueItem':
        return cls(**data)

@dataclass  
class FileQueueItem:
    """File queue item."""
    file_url: str
    repository_name: str
    job_id: str
    file_size: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileQueueItem':
        return cls(**data)

# Global instance for easy access
_local_queue_manager = None

def get_local_queue_manager() -> LocalQueueManager:
    """Get the global local queue manager instance."""
    global _local_queue_manager
    if _local_queue_manager is None:
        _local_queue_manager = LocalQueueManager()
    return _local_queue_manager

def create_queue_manager(redis_url: Optional[str] = None) -> LocalQueueManager:
    """
    Create appropriate queue manager based on environment.
    For local development: LocalQueueManager
    For GCP production: Would use Redis/Cloud Tasks
    """
    if redis_url and redis_url != 'redis://localhost:6379':
        # In production, we'd use actual Redis
        logger.info(f"🌐 Would use Redis at: {redis_url}")
        # For now, fall back to local for development
        logger.info("🔧 Using local queue manager for development")
        return get_local_queue_manager()
    else:
        logger.info("🔧 Using local queue manager for development")
        return get_local_queue_manager()
