#!/usr/bin/env python3
"""
Test Gemini API for the complete repository scraping workflow
Simulates: chunking, rate limiting, batch processing
"""

import requests
import json
import time
import tiktoken
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# API Configuration
API_KEY = "AIzaSyDmNPg_95eUXq8beQuGcGm2iKPwxYuQv20"
BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models"
MODEL = "gemini-2.5-flash"

# Rate limiting configuration
RATE_LIMIT_LOCK = threading.Lock()
LAST_REQUEST_TIME = 0
MIN_REQUEST_INTERVAL = 0.1  # 100ms between requests

def count_tokens(text):
    """Count tokens using tiktoken (approximation for Gemini)"""
    try:
        encoding = tiktoken.get_encoding("cl100k_base")
        return len(encoding.encode(text))
    except:
        # Fallback: rough estimation
        return len(text.split()) * 1.3

def rate_limited_request(url, payload, headers, timeout=60):
    """Make a rate-limited request to Gemini API"""
    global LAST_REQUEST_TIME
    
    with RATE_LIMIT_LOCK:
        current_time = time.time()
        time_since_last = current_time - LAST_REQUEST_TIME
        
        if time_since_last < MIN_REQUEST_INTERVAL:
            sleep_time = MIN_REQUEST_INTERVAL - time_since_last
            time.sleep(sleep_time)
        
        LAST_REQUEST_TIME = time.time()
    
    return requests.post(url, json=payload, headers=headers, timeout=timeout)

def chunk_content(content, max_tokens=850000):
    """Chunk content to stay under token limits"""
    tokens = count_tokens(content)
    
    if tokens <= max_tokens:
        return [content]
    
    # Simple chunking by splitting content
    lines = content.split('\n')
    chunks = []
    current_chunk = []
    current_tokens = 0
    
    for line in lines:
        line_tokens = count_tokens(line)
        
        if current_tokens + line_tokens > max_tokens and current_chunk:
            chunks.append('\n'.join(current_chunk))
            current_chunk = [line]
            current_tokens = line_tokens
        else:
            current_chunk.append(line)
            current_tokens += line_tokens
    
    if current_chunk:
        chunks.append('\n'.join(current_chunk))
    
    # Limit to max 3 chunks as per requirements
    return chunks[:3]

def analyze_chunk(chunk_id, chunk_content, repo_name="test-repo"):
    """Analyze a single chunk with Gemini"""
    print(f"  📝 Processing chunk {chunk_id}...")
    
    url = f"{BASE_URL}/{MODEL}:generateContent?key={API_KEY}"
    
    prompt = f"""
Analyze this repository chunk and provide a comprehensive summary:

Repository: {repo_name}
Chunk: {chunk_id}

Please provide:
1. **Features Found**: List all features and functionalities in this chunk
2. **Technologies**: Frameworks, libraries, and technologies used
3. **Architecture Components**: Describe architectural patterns and components
4. **Key Functions/Classes**: Important code structures
5. **Dependencies**: External dependencies and integrations

Be comprehensive and focus on functionality analysis.

Content:
{chunk_content}
"""
    
    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 4000,
            "topP": 0.8,
            "topK": 40
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        start_time = time.time()
        response = rate_limited_request(url, payload, headers)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            candidates = data.get('candidates', [])
            if candidates:
                content = candidates[0].get('content', {})
                parts = content.get('parts', [])
                if parts:
                    analysis = parts[0].get('text', '')
                    
                    result = {
                        'chunk_id': chunk_id,
                        'success': True,
                        'analysis': analysis,
                        'response_time': end_time - start_time,
                        'tokens_estimated': count_tokens(chunk_content)
                    }
                    
                    print(f"  ✅ Chunk {chunk_id} completed ({end_time - start_time:.2f}s)")
                    return result
        
        print(f"  ❌ Chunk {chunk_id} failed: {response.status_code}")
        return {
            'chunk_id': chunk_id,
            'success': False,
            'error': f"HTTP {response.status_code}",
            'response_time': end_time - start_time
        }
        
    except Exception as e:
        print(f"  ❌ Chunk {chunk_id} exception: {e}")
        return {
            'chunk_id': chunk_id,
            'success': False,
            'error': str(e),
            'response_time': 0
        }

def test_chunking_workflow():
    """Test the complete chunking and analysis workflow"""
    print("🔄 Testing Chunking and Analysis Workflow...")
    
    # Create a large sample repository content
    large_repo_content = """
# Large Repository Example

## File: src/main.py
""" + """
def process_data(data):
    '''Process incoming data with validation and transformation'''
    if not data:
        return None
    
    processed = []
    for item in data:
        if validate_item(item):
            transformed = transform_item(item)
            processed.append(transformed)
    
    return processed

def validate_item(item):
    '''Validate individual data items'''
    required_fields = ['id', 'name', 'type']
    return all(field in item for field in required_fields)

def transform_item(item):
    '''Transform item to standard format'''
    return {
        'id': str(item['id']),
        'name': item['name'].strip().title(),
        'type': item['type'].lower(),
        'processed_at': time.time()
    }
""" * 100  # Repeat to make it large
    
    large_repo_content += """

## File: src/database.py
""" + """
import sqlite3
from contextlib import contextmanager

class DatabaseManager:
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        conn = sqlite3.connect(self.db_path)
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        with self.get_connection() as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS items (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    processed_at REAL
                )
            ''')
            conn.commit()
    
    def insert_item(self, item):
        with self.get_connection() as conn:
            conn.execute(
                'INSERT OR REPLACE INTO items VALUES (?, ?, ?, ?)',
                (item['id'], item['name'], item['type'], item['processed_at'])
            )
            conn.commit()
""" * 50  # Repeat to make it large
    
    print(f"📊 Total content size: {len(large_repo_content):,} characters")
    print(f"📊 Estimated tokens: {count_tokens(large_repo_content):,}")
    
    # Chunk the content
    chunks = chunk_content(large_repo_content, max_tokens=850000)
    print(f"📦 Created {len(chunks)} chunks")
    
    for i, chunk in enumerate(chunks, 1):
        tokens = count_tokens(chunk)
        print(f"  Chunk {i}: {tokens:,} tokens ({len(chunk):,} chars)")
    
    return chunks

def test_parallel_processing():
    """Test parallel processing of multiple chunks"""
    print("\n⚡ Testing Parallel Processing...")
    
    # Get chunks from chunking test
    chunks = test_chunking_workflow()
    
    if not chunks:
        print("❌ No chunks to process")
        return False
    
    # Process chunks in parallel (max 2 workers as per requirements)
    max_workers = 2
    results = []
    
    print(f"\n🔄 Processing {len(chunks)} chunks with {max_workers} workers...")
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all chunk analysis tasks
        future_to_chunk = {
            executor.submit(analyze_chunk, i+1, chunk): i+1 
            for i, chunk in enumerate(chunks)
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"❌ Chunk {chunk_id} failed with exception: {e}")
                results.append({
                    'chunk_id': chunk_id,
                    'success': False,
                    'error': str(e)
                })
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Analyze results
    successful = [r for r in results if r.get('success', False)]
    failed = [r for r in results if not r.get('success', False)]
    
    print(f"\n📊 Processing Results:")
    print(f"  Total time: {total_time:.2f}s")
    print(f"  Successful: {len(successful)}/{len(results)}")
    print(f"  Failed: {len(failed)}/{len(results)}")
    
    if successful:
        avg_response_time = sum(r['response_time'] for r in successful) / len(successful)
        total_tokens = sum(r.get('tokens_estimated', 0) for r in successful)
        print(f"  Average response time: {avg_response_time:.2f}s")
        print(f"  Total tokens processed: {total_tokens:,}")
    
    # Save consolidated analysis
    if successful:
        consolidated_analysis = "# Consolidated Repository Analysis\n\n"
        consolidated_analysis += f"**Processing Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        consolidated_analysis += f"**Total Chunks:** {len(chunks)}\n"
        consolidated_analysis += f"**Successful Analyses:** {len(successful)}\n"
        consolidated_analysis += f"**Total Processing Time:** {total_time:.2f}s\n\n"
        
        for result in successful:
            consolidated_analysis += f"## Chunk {result['chunk_id']} Analysis\n\n"
            consolidated_analysis += result['analysis']
            consolidated_analysis += "\n\n---\n\n"
        
        with open('consolidated_analysis.md', 'w', encoding='utf-8') as f:
            f.write(consolidated_analysis)
        
        print(f"💾 Consolidated analysis saved to: consolidated_analysis.md")
    
    return len(successful) == len(results)

def main():
    """Run complete workflow tests"""
    print("🚀 Starting Gemini Workflow Tests\n")
    
    # Test parallel processing workflow
    workflow_ok = test_parallel_processing()
    
    # Summary
    print("\n" + "="*50)
    print("📋 WORKFLOW TEST SUMMARY:")
    print(f"  Parallel Processing: {'✅ PASS' if workflow_ok else '❌ FAIL'}")
    
    print(f"\n🎯 Overall: {'✅ WORKFLOW TESTS PASSED' if workflow_ok else '❌ WORKFLOW TESTS FAILED'}")
    
    if workflow_ok:
        print("\n📄 Check 'consolidated_analysis.md' for the complete analysis!")
    
    return workflow_ok

if __name__ == "__main__":
    main()
