This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/**/*.py, main.py, service.py, worker.py, local_mode_analysis.json, cloud_mode_analysis.json, local_mode_detailed.log, cloud_mode_detailed.log, successful_*.json, *_test_log_example.log
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
cloud_mode_analysis.json
local_mode_analysis.json
main.py
service.py
src/__init__.py
src/cloud_pipeline.py
src/config.py
src/data_fetcher.py
src/github_search.py
src/llm_rate_limiter.py
src/monitoring.py
src/pipeline.py
src/redis_queue.py
src/sentry_analyzer.py
src/sentry_config.py
src/storage_manager.py
src/supabase_client.py
src/utils.py
successful_analysis_example.json
worker.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="cloud_mode_analysis.json">
{
  "metadata": {
    "generated": "2025-07-25 07:27:06",
    "output_directory": "20250725_072706_270_redis",
    "total_repositories": 1,
    "processing_configuration": {
      "repomix_workers": 15,
      "llm_workers": 4,
      "rate_limit_mb_per_min": 12
    }
  },
  "analyses": []
}
</file>

<file path="local_mode_analysis.json">
{
  "metadata": {
    "generated": "2025-07-25 04:45:01",
    "output_directory": "20250725_044501_511",
    "total_repositories": 20,
    "processing_configuration": {
      "repomix_workers": 15,
      "llm_workers": 4,
      "rate_limit_mb_per_min": 12
    }
  },
  "analyses": [
    {
      "repository": {
        "name": "zszszszsz/.config",
        "url": "https://github.com/zszszszsz/.config",
        "file_size_mb": 0.0,
        "repomix_file": "repomixes/zszszszsz_.config.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:18",
        "worker_id": 18072,
        "analysis_length": 3509
      },
      "analysis": {
        "content": "Okay, let's analyze the provided repository code summary.\n\n**1. Main Purpose and Functionality:**\n\nThe repository, `zszszszsz/.config`,  is a template repository designed to build OpenWrt firmware using GitHub Actions.  It leverages GitHub's CI/CD capabilities to automate the process of compiling OpenWrt based on a user-provided `.config` file. The `.config` file defines the desired configuration and packages for the OpenWrt build.  The core functionality is to take this configuration, build the OpenWrt firmware, and make the resulting binaries available as downloadable artifacts.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **GitHub Actions:**  The primary technology.  It's the CI/CD platform used to automate the build process.\n*   **OpenWrt:** The embedded operating system being built.\n*   **Lean's OpenWrt (coolsnowwolf/lede):**  Used as the source code base for OpenWrt.  The `.config` file is generated based on this source.\n*   **tmate, mxschmitt/action-tmate, csexton/debugger-action:** Likely used for debugging purposes within the GitHub Actions workflow. `tmate` allows for remote terminal sharing, which can be helpful for troubleshooting build issues.\n*   **Cowtransfer, WeTransfer, Mikubill/transfer:** Used for transferring the built firmware binaries.\n\n**3. Architecture Overview:**\n\nThe architecture is centered around a GitHub Actions workflow.\n\n1.  **User Action:** The user creates a new repository from the template and pushes their `.config` file.\n2.  **GitHub Actions Trigger:** The push triggers a predefined workflow in the repository.\n3.  **Build Process:** The workflow uses the `.config` file to build OpenWrt using Lean's OpenWrt source code.\n4.  **Artifact Creation:** The built firmware binaries are packaged as artifacts.\n5.  **Download:** The user can download the artifacts from the GitHub Actions page.\n\n**4. Notable Features or Patterns:**\n\n*   **Template Repository:**  The repository is designed to be used as a template, making it easy for users to create their own OpenWrt build environments.\n*   **Configuration-Driven Build:** The `.config` file is the central configuration element, allowing users to customize the build.\n*   **Automated Build Process:** GitHub Actions automates the entire build process, from compiling the code to creating the artifacts.\n*   **Artifact Delivery:** The built firmware is delivered as downloadable artifacts, making it easy for users to access the final product.\n*   **Debugging Tools:** The inclusion of `tmate` and related actions suggests a focus on providing debugging capabilities within the automated build environment.\n\n**5. Potential Use Cases:**\n\n*   **Custom OpenWrt Firmware:** Users can create custom OpenWrt firmware with specific packages and configurations tailored to their needs.\n*   **Automated Firmware Builds:**  The repository can be used to automate the process of building OpenWrt firmware for various devices.\n*   **OpenWrt Development:** Developers can use the repository to test and build OpenWrt firmware during development.\n*   **Creating OpenWrt Distributions:**  The repository can be used as a base for creating custom OpenWrt distributions.\n*   **Reproducible Builds:** By version controlling the `.config` file, users can ensure reproducible builds of their OpenWrt firmware.\n\nIn summary, this repository provides a convenient and automated way to build custom OpenWrt firmware using GitHub Actions. It simplifies the build process and makes it accessible to a wider range of users.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "e2b-dev/awesome-ai-sdks",
        "url": "https://github.com/e2b-dev/awesome-ai-sdks",
        "file_size_mb": 0.01,
        "repomix_file": "repomixes/e2b-dev_awesome-ai-sdks.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:19",
        "worker_id": 17344,
        "analysis_length": 3331
      },
      "analysis": {
        "content": "Okay, let's analyze the provided repository information.\n\n**1. Main Purpose and Functionality:**\n\nThe repository \"e2b-dev/awesome-ai-sdks\" is a curated list of SDKs, frameworks, libraries, and tools specifically designed for creating, monitoring, debugging, and deploying autonomous AI agents.  It serves as a central resource for developers looking for tools to build and manage AI agents.\n\n**2. Key Technologies and Frameworks Used (Based on the README):**\n\nThe repository itself doesn't *use* these technologies, but it *lists* them.  Based on the README, some of the key technologies and frameworks mentioned include:\n\n*   **LangChain:** A framework for building applications using large language models.\n*   **Vercel AI SDK:** A library for building AI-powered user interfaces.\n*   **Chidori:** A reactive runtime for building AI agents.\n*   **Steamship:** A platform for building, scaling, and monitoring AI agents.\n*   **Helicone:** An observability platform for GPT-3.\n*   **AgentOps:** Tools for agent monitoring and analytics.\n*   **Fixie:** A platform for conversational AI.\n*   **Langfuse:** An open-source analytics for LLM apps.\n*   **LangSmith:** A platform for debugging, testing, evaluating, and monitoring LLM applications.\n*   **SID:** Data infrastructure for AI.\n*   **E2B:** An operating system for AI agents.\n\n**3. Architecture Overview:**\n\nThe repository's architecture is very simple. It consists of a single `README.md` file. This file acts as a structured list or directory, providing links and brief descriptions of various AI agent SDKs and related tools.  It's essentially a curated list, not a software project with a complex architecture.\n\n**4. Notable Features or Patterns:**\n\n*   **Curated List:** The primary feature is the curated list itself.  It provides a single point of reference for discovering relevant tools.\n*   **Categorization:** The list is implicitly categorized by the type of tool (e.g., frameworks, observability platforms, data infrastructure).\n*   **Links and Descriptions:** Each entry includes a link to the project's website, GitHub repository, or other relevant resources, along with a brief description.\n*   **Community Focus:** The repository encourages contributions and feedback via pull requests, fostering a community-driven approach to maintaining the list.\n*   **E2B Promotion:** The repository is maintained by the E2B team, and the README prominently features E2B's own offerings for AI agent development.\n\n**5. Potential Use Cases:**\n\n*   **Discovery of AI Agent Tools:** Developers can use this repository to discover new SDKs, frameworks, and tools for building AI agents.\n*   **Tool Selection:** The list can help developers compare different tools and choose the ones that best fit their needs.\n*   **Staying Up-to-Date:** The repository can serve as a resource for staying up-to-date on the latest developments in the AI agent tooling landscape.\n*   **Community Engagement:** Developers can contribute to the list by adding new tools or improving existing entries, fostering collaboration and knowledge sharing.\n*   **Benchmarking:** The list can be used to benchmark different AI agent SDKs and tools.\n\nIn summary, this repository is a valuable resource for anyone working on or interested in AI agents, providing a curated list of relevant SDKs and tools.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "golf-mcp/golf",
        "url": "https://github.com/golf-mcp/golf",
        "file_size_mb": 0.48,
        "repomix_file": "repomixes/golf-mcp_golf.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:21",
        "worker_id": 21600,
        "analysis_length": 4544
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided code repository information. Here's a comprehensive summary:\n\n**1. Main Purpose and Functionality:**\n\n*   **Golf is a framework designed to simplify the creation of MCP (Machine Communication Protocol) servers.**  It aims to reduce boilerplate code and accelerate development by providing a structured way to define server capabilities (tools, prompts, resources) using Python files.\n*   The framework automates the discovery, parsing, and compilation of these components into a runnable FastMCP server.\n*   It provides features like OAuth and API Key authentication, health checks, and OpenTelemetry support.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Python:** The primary language for the framework and server components.\n*   **FastMCP:**  The underlying server technology that Golf builds upon.\n*   **Pydantic:** Used for data validation and settings management (e.g., defining input/output schemas for tools).\n*   **Starlette:**  Likely used as the foundation for the HTTP server within FastMCP (though not explicitly stated, it's implied by the use of `starlette.responses.RedirectResponse`).\n*   **Httpx:** Used for making HTTP requests (likely for OAuth flows).\n*   **JWT (JSON Web Tokens):** Used for handling authentication tokens.\n*   **OpenTelemetry:** Used for distributed tracing.\n\n**3. Architecture Overview:**\n\n*   **Component-Based:** The core architecture revolves around defining server capabilities as components (tools, resources, prompts) organized in a specific directory structure.\n*   **Configuration-Driven:** The `golf.json` file acts as the central configuration file, controlling server settings, transport protocols, authentication, and telemetry.\n*   **CLI Tooling:** The `golf` command-line interface provides commands for initializing projects (`golf init`), building the server (`golf build`), and running the server (`golf run`).\n*   **Middleware:**  Authentication (API Key and OAuth) is likely implemented as middleware to intercept requests and enforce security policies.\n*   **Modular:** The code is organized into modules for authentication (`golf/auth`), CLI (`golf/cli`), core functionality (`golf/core`), examples (`golf/examples`), metrics (`golf/metrics`), and telemetry (`golf/telemetry`).\n*   **Examples:** The `golf/examples` directory provides example projects to demonstrate how to use the framework.\n\n**4. Notable Features or Patterns:**\n\n*   **Convention over Configuration:**  Golf relies heavily on conventions for file naming and directory structure to automatically discover and configure components.\n*   **Automatic Component ID Generation:** Component IDs are derived from file paths, simplifying component registration.\n*   **Pre-Build Hooks:** The `pre_build.py` script allows developers to execute custom code before the server is built (e.g., for setting up authentication).\n*   **API Key Authentication:** Provides a simple API key pass-through mechanism for tools to access API keys from request headers.\n*   **OAuth Authentication:** Supports OAuth 2.0 authentication with token management and client registration.\n*   **Health Check Endpoint:** Includes built-in health check endpoint support for production deployments.\n*   **OpenTelemetry Integration:** Provides built-in OpenTelemetry instrumentation for distributed tracing.\n*   **Telemetry Collection:**  Collects anonymous usage data on the CLI to improve the framework.\n*   **Token Storage:** Uses an in-memory token storage for OAuth tokens, authorization codes, and client information.\n\n**5. Potential Use Cases:**\n\n*   **Building custom MCP servers for various applications:**  This is the primary use case.  MCP servers can be used for a wide range of tasks, including:\n    *   Automating tasks\n    *   Integrating with external APIs\n    *   Creating intelligent agents\n    *   Building conversational interfaces\n*   **Creating internal tools and services:**  Golf can be used to build internal tools and services that require authentication and authorization.\n*   **Prototyping and experimenting with MCP concepts:** The framework's ease of use makes it suitable for rapid prototyping and experimentation.\n*   **Educational purposes:**  The well-structured code and examples make it a good learning resource for understanding MCP server development.\n\nIn summary, Golf is a promising framework that simplifies the development of MCP servers by providing a structured, convention-based approach and integrating key features like authentication, health checks, and telemetry.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "tinyfish-io/agentql",
        "url": "https://github.com/tinyfish-io/agentql",
        "file_size_mb": 0.16,
        "repomix_file": "repomixes/tinyfish-io_agentql.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:22",
        "worker_id": 26000,
        "analysis_length": 4678
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided code repository information for `tinyfish-io/agentql`. Here's a comprehensive summary:\n\n**1. Main Purpose and Functionality:**\n\n*   `agentql` is a tool or library designed to simplify web scraping and automation tasks. It allows users to extract data and interact with web pages using a declarative query language (AgentQL).\n*   The core idea is to abstract away the complexities of locating elements and extracting data from web pages by using a query language that can identify elements based on their semantic meaning and relationships, rather than relying solely on CSS selectors or XPath expressions.\n*   It integrates with Playwright, a browser automation library, to control and interact with web browsers.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Playwright:**  Used for browser automation (launching browsers, navigating pages, interacting with elements).\n*   **JavaScript/TypeScript:** The primary language for the library and examples. Python examples are also provided.\n*   **AgentQL (Query Language):**  A custom query language (presumably) that allows users to define what data they want to extract or what elements they want to interact with.  The examples show the query language being used to identify elements by their function (e.g., \"search_input\", \"submit_btn\") rather than specific CSS classes or IDs.\n*   **Node.js:** Used for running the JavaScript examples.\n\n**3. Architecture Overview:**\n\n*   The architecture appears to be based on a \"wrapper\" around Playwright's `Page` object.  The `wrap` function (from the `agentql` library) takes a Playwright `Page` and extends it with AgentQL's querying capabilities.\n*   The core components seem to be:\n    *   **AgentQL Query Engine:**  This is the heart of the library, responsible for interpreting the AgentQL queries and translating them into instructions for locating elements on the page.  (The code for this engine is not directly visible in the provided snippet, but it's implied.)\n    *   **Playwright Integration:**  The library uses Playwright to execute the instructions generated by the query engine (e.g., clicking buttons, filling forms, extracting text).\n    *   **API Key:** The library requires an API key for usage, suggesting it might rely on a remote service for query processing or element identification.\n\n**4. Notable Features or Patterns:**\n\n*   **Declarative Querying:**  The use of AgentQL allows users to express *what* they want to extract or interact with, rather than *how* to do it. This simplifies the code and makes it more maintainable.\n*   **Semantic Element Identification:** AgentQL seems to focus on identifying elements based on their semantic role (e.g., \"search input\", \"product price\") rather than relying on specific CSS selectors or XPath, which can be brittle and change frequently.\n*   **`queryElements()` and `queryData()` Methods:**  These are the main methods provided by the AgentQL wrapper. `queryElements()` is used to locate and interact with specific elements on the page, while `queryData()` is used to extract structured data.\n*   **`getByPrompt()` Method:** Allows finding elements using natural language prompts, further simplifying element selection.\n*   **Example-Driven Documentation:** The repository includes a large number of examples that demonstrate how to use AgentQL for various web scraping and automation tasks.\n*   **Asynchronous Support:** The Python templates include both synchronous and asynchronous examples, indicating that the library supports both programming models.\n*   **Anti-Bot Detection Techniques:** One example demonstrates how to simulate human-like mouse movements to avoid bot detection.\n\n**5. Potential Use Cases:**\n\n*   **Web Scraping:** Extracting data from e-commerce sites, news sites, social media platforms, and other websites.\n*   **Data Aggregation:** Collecting data from multiple sources and combining it into a single dataset.\n*   **Web Automation:** Automating tasks such as filling forms, logging into websites, and performing searches.\n*   **Price Monitoring:** Tracking product prices on e-commerce sites.\n*   **Content Moderation:** Automating the process of identifying and removing inappropriate content from websites.\n*   **Testing:** Automating web application testing.\n\nIn summary, `agentql` aims to make web scraping and automation easier by providing a higher-level, more semantic way to interact with web pages. It leverages Playwright for browser automation and introduces a custom query language (AgentQL) to simplify element identification and data extraction. The numerous examples highlight its versatility and potential for various use cases.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "roger1337/JDBG",
        "url": "https://github.com/roger1337/JDBG",
        "file_size_mb": 0.0,
        "repomix_file": "repomixes/roger1337_JDBG.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:26",
        "worker_id": 21600,
        "analysis_length": 3639
      },
      "analysis": {
        "content": "Based on the provided information, here's a comprehensive summary of the `roger1337/JDBG` repository:\n\n**1. Main Purpose and Functionality:**\n\nThe primary purpose of this repository is to provide a codebase that can be easily analyzed by AI systems.  It's a \"packed representation\" of a subset of the repository's contents, specifically designed for automated processes like code review. The goal is to provide the most important context for AI analysis in a single, consumable file. It seems like the repository itself is likely a debugging tool or library, given the name \"JDBG,\" but without the actual code, this is just speculation.\n\n**2. Key Technologies and Frameworks Used:**\n\nThe provided information doesn't reveal the specific technologies or frameworks used in the actual `JDBG` codebase. However, the file inclusion patterns suggest the repository likely contains code written in:\n\n*   Python (`.py`)\n*   JavaScript (`.js`)\n*   TypeScript (`.ts`)\n*   Go (`.go`)\n*   Rust (`.rs`)\n*   Markdown (`.md`)\n\nThe absence of other common extensions (e.g., `.java`, `.c`, `.cpp`, `.html`, `.css`) suggests these languages are the primary focus.\n\n**3. Architecture Overview:**\n\nWithout the actual code, it's impossible to provide a detailed architecture overview.  However, the file structure suggests a modular design, with files organized into directories (although the directory structure itself is empty in the provided data).  The inclusion of multiple languages hints at a potentially complex system with different components implemented in different languages.\n\n**4. Notable Features or Patterns:**\n\n*   **AI-Optimized Representation:** The repository is explicitly structured for AI consumption, prioritizing ease of analysis and code review.\n*   **Selective Inclusion:**  Only a subset of the repository's files is included in the packed representation, focusing on the most important context.  This is likely determined by factors like file change frequency.\n*   **Read-Only Packed File:** The generated file is intended to be read-only, with modifications made to the original repository files.\n*   **File Sorting by Git Change Count:** Files with more changes are placed at the bottom of the packed file, potentially highlighting areas of active development or complexity.\n*   **Language Diversity:** The inclusion of multiple programming languages suggests a potentially complex or multifaceted project.\n\n**5. Potential Use Cases:**\n\n*   **Automated Code Review:**  AI systems can use the packed representation to automatically identify potential bugs, security vulnerabilities, or style violations.\n*   **Code Understanding and Documentation:**  AI can analyze the code to generate documentation, understand the system's architecture, and identify dependencies.\n*   **Bug Detection and Prevention:**  AI can analyze code patterns to predict potential bugs and suggest preventative measures.\n*   **Security Analysis:**  AI can identify potential security vulnerabilities by analyzing code patterns and dependencies.\n*   **Code Completion and Suggestion:** AI can use the code as context to provide code completion suggestions and improve developer productivity.\n*   **Migration and Refactoring:** AI can assist in migrating the codebase to new technologies or refactoring existing code.\n\n**In summary, the `roger1337/JDBG` repository appears to be a project, likely related to debugging, that is designed to be easily analyzed by AI systems. The repository likely contains code in Python, JavaScript, TypeScript, Go, and Rust, and is structured to provide the most important context for automated code review and analysis.**",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "eugeneyan/news-agents",
        "url": "https://github.com/eugeneyan/news-agents",
        "file_size_mb": 0.1,
        "repomix_file": "repomixes/eugeneyan_news-agents.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:26",
        "worker_id": 17344,
        "analysis_length": 4719
      },
      "analysis": {
        "content": "Here's a comprehensive summary of the provided code repository:\n\n**1. Main Purpose and Functionality:**\n\nThe repository implements a news aggregation system that leverages multiple agents working in parallel within a terminal environment. It uses Amazon Q CLI as the agent harness, Model Context Protocol (MCP) to parse RSS feeds, and tmux for terminal splitting and monitoring. The system fetches news from sources like Hacker News, TechCrunch, Wired, WSJ, and AI News, summarizes the articles, and presents the digests in a structured format within the terminal.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Amazon Q CLI:** Used as the agent harness, providing the environment for running and managing the agents.\n*   **Model Context Protocol (MCP):** Used for parsing RSS feeds and defining tools for the agents.  The `fastmcp` server is used.\n*   **tmux:** Used for terminal multiplexing, allowing multiple agents to run in separate panes within a single terminal window. This facilitates parallel processing and monitoring.\n*   **httpx:** Used for making asynchronous HTTP requests to fetch RSS feeds.\n*   **xml.etree.ElementTree:** Used for parsing XML RSS feeds.\n*   **Python:** The primary programming language.\n*   **uv:** Used for dependency management.\n\n**3. Architecture Overview:**\n\nThe system employs a main agent and multiple sub-agents.\n\n*   **Main Agent:**\n    *   Reads a list of feed URLs from a configuration file (presumably `feeds.txt`, though not included in the provided files).\n    *   Divides the feeds into chunks.\n    *   Spawns sub-agents in separate tmux panes.\n    *   Assigns a chunk of feeds to each sub-agent.\n    *   Monitors the progress of each sub-agent.\n    *   Collects summaries from the sub-agents.\n    *   Aggregates the summaries into a main summary.\n*   **Sub-Agents:**\n    *   Receive a list of feed URLs from the main agent.\n    *   For each feed:\n        *   Fetch the RSS feed.\n        *   Parse the feed to extract articles.\n        *   Summarize the articles.\n        *   Save the summary to a file.\n    *   Report completion to the main agent.\n\nThe agents communicate using `tmux send-keys` to send commands and capture pane output to receive responses. The `context` directory contains instructions for the agents, defining their roles and responsibilities.\n\n**4. Notable Features or Patterns:**\n\n*   **Multi-Agent System:** The core concept is a distributed task execution using multiple agents.\n*   **Parallel Processing:**  Leverages tmux to run sub-agents in parallel, speeding up the news aggregation process.\n*   **Asynchronous Operations:** Uses `httpx` for asynchronous HTTP requests, improving performance.\n*   **Configuration-Driven:** The system reads feed URLs from a configuration file, making it easy to add or remove news sources.\n*   **Modular Design:** The code is organized into separate modules for each news source, making it easier to maintain and extend.\n*   **Error Handling:** Includes basic error handling for network requests and XML parsing.\n*   **Context-Aware Agents:** The agents are given specific instructions and roles via context files (e.g., `context/main-agent.md`, `context/sub-agent.md`).\n*   **Tmux-Based Communication:** Agents communicate by sending commands to each other's tmux panes and monitoring the output.\n*   **Polling for Responses:** The main agent polls the sub-agents' tmux panes to check for completion and retrieve results.\n*   **Specific File Naming Conventions:** Sub-agents are instructed to save their summaries to files with specific names.\n*   **Immediate Saving of Summaries:** Sub-agents are instructed to save each feed's summary immediately after processing it, rather than batching them.\n*   **Sequential Feed Processing:** Sub-agents are instructed to process each feed completely before moving to the next one.\n\n**5. Potential Use Cases:**\n\n*   **Personalized News Aggregation:** Users can customize the system to fetch news from their preferred sources.\n*   **Real-time Monitoring:** The system can be used to monitor news sources for specific keywords or topics.\n*   **Automated Content Creation:** The summaries generated by the system can be used as input for other content creation tools.\n*   **Educational Tool:** Demonstrates the use of agents, parallel processing, and terminal-based applications.\n*   **Experimentation with LLMs:** Provides a framework for experimenting with LLMs in a multi-agent environment.\n\nIn summary, this repository provides a functional news aggregation system that showcases the power of combining agents, parallel processing, and terminal-based tools. It's a good example of how to build a distributed application using a relatively simple architecture.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "arextest/arex-agent-java",
        "url": "https://github.com/arextest/arex-agent-java",
        "file_size_mb": 0.01,
        "repomix_file": "repomixes/arextest_arex-agent-java.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:30",
        "worker_id": 21600,
        "analysis_length": 3333
      },
      "analysis": {
        "content": "Here's a comprehensive summary of the `arextest/arex-agent-java` repository based on the provided information:\n\n**1. Main Purpose and Functionality:**\n\nThe `arextest/arex-agent-java` repository contains the code for the AREX Java agent. AREX is an open-source testing framework that leverages real-world data for regression testing.  The agent dynamically instruments Java applications (Java 8+) to record live traffic data (database records, service payloads, cache items, etc.) and then uses this recorded data for mocking, testing, and debugging.  It aims to provide a non-intrusive way to capture realistic data without requiring code changes in the target application.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Java Instrumentation API:** AREX uses the Java Instrumentation API to dynamically weave bytecode into existing code for recording data.\n*   **Various Libraries and Frameworks:** The agent supports a wide range of popular Java libraries and frameworks, including:\n    *   Spring (Boot, WebClient, RestTemplate, OpenFeign, Security)\n    *   HTTP Clients (Apache HttpClient, OkHttp, Feign)\n    *   Redis Clients (RedisTemplate, Jedis, Redisson, Lettuce)\n    *   Persistence Frameworks (MyBatis, Hibernate)\n    *   NoSQL (MongoDB)\n    *   RPC (Dubbo)\n    *   Cache Libraries (Caffeine, Guava, Spring Cache)\n    *   Auth (Shiro, JCasbin, Auth0, JWTK)\n    *   Netty\n    *   Apollo Config\n    *   Java Executors\n    *   System time\n    *   Dynamic Type\n\n**3. Architecture Overview:**\n\nThe AREX agent operates in the background of the target application.  It's not a proxy like some other similar frameworks.  It uses the Java Instrumentation API to intercept and record data from various supported libraries and frameworks.  The recorded data is then sent to the AREX storage service. The architecture is designed to be non-intrusive, minimizing the impact on the target application's performance and requiring no code changes for integration. The AREX agent works along with the AREX storage service.\n\n**4. Notable Features or Patterns:**\n\n*   **Non-Intrusive Recording:**  The agent records data without requiring modifications to the application's source code.\n*   **Dynamic Instrumentation:**  Uses the Java Instrumentation API to dynamically weave bytecode at runtime.\n*   **Wide Framework Support:**  Supports a broad range of popular Java libraries and frameworks.\n*   **Real-World Data Driven:** Employs real-world data for regression testing, providing more realistic test scenarios.\n*   **Configuration:**  The agent can be configured using command-line arguments or a configuration file (`arex.agent.conf`).\n\n**5. Potential Use Cases:**\n\n*   **Regression Testing:**  Automated regression testing using recorded data to ensure that new code changes don't introduce regressions.\n*   **Mocking:**  Using recorded data to mock external dependencies during testing, eliminating the need for real external services.\n*   **Debugging:**  Analyzing recorded data to understand the behavior of the application and identify the root cause of issues.\n*   **Performance Testing:**  Replaying recorded traffic to simulate real-world load and assess the application's performance.\n*   **Service Virtualization:**  Creating virtualized versions of services based on recorded data for testing and development purposes.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "snagasuri/deebo-prototype",
        "url": "https://github.com/snagasuri/deebo-prototype",
        "file_size_mb": 0.27,
        "repomix_file": "repomixes/snagasuri_deebo-prototype.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:31",
        "worker_id": 18072,
        "analysis_length": 7408
      },
      "analysis": {
        "content": "Okay, let's break down the `snagasuri/deebo-prototype` repository based on the provided information.\n\n**1. Main Purpose and Functionality:**\n\n*   **Deebo:** The repository appears to be a prototype for a debugging tool called \"Deebo.\"  Deebo seems to automate the debugging process by using multiple agents (Mother and Scenario) and a memory bank to store progress.\n*   **Automated Debugging:** Deebo likely analyzes code, identifies potential issues (e.g., race conditions), and uses AI (via OpenAI/OpenRouter) to suggest solutions or provide insights.\n*   **MCP (Model Context Protocol):** Deebo leverages the Model Context Protocol to communicate between different components, likely including the CI client and the Deebo server.\n*   **System Health Check:** The `deebo-doctor` package provides a command-line tool to check the system's health and configuration for Deebo.\n*   **Guide Server:** The `deebo-setup` package includes a guide server that provides documentation and instructions for setting up Deebo.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **TypeScript:** The primary language seems to be TypeScript, indicated by `.ts` file extensions and the presence of build configurations.\n*   **JavaScript:** Some JavaScript files exist, likely the result of compiled TypeScript or legacy code.\n*   **Node.js:**  Node.js is the runtime environment.\n*   **Model Context Protocol (MCP):** Used for communication between Deebo components.  The `@modelcontextprotocol/sdk` package is a key dependency.\n*   **OpenAI/OpenRouter:**  Used for AI-powered analysis and potentially code generation/suggestion. The `openai` package is used.  OpenRouter acts as a proxy for accessing different LLMs.\n*   **dotenv:** Used for managing environment variables.\n*   **simple-git:** Used for interacting with Git repositories.\n*   **chalk:** Used for adding colors and styles to console output.\n*   **uvx:** A tool used for managing MCP servers.\n*   **desktop-commander:** A tool used for interacting with desktop applications.\n\n**3. Architecture Overview:**\n\nBased on the file structure and code snippets, here's a likely architectural breakdown:\n\n*   **CI Client (ci/mcp-client):**\n    *   A Node.js script (`index.ts`) that acts as a client to the Deebo server.\n    *   Uses the `@modelcontextprotocol/sdk` to connect to the server via `StdioClientTransport`.\n    *   Initiates debugging sessions by calling the `start` tool on the server.\n    *   Monitors the session status using the `check` tool in a loop with exponential backoff.\n    *   Optionally uses OpenAI/OpenRouter to analyze the output of the debugging session.\n*   **Deebo Server (implied):**\n    *   The `ci/mcp-client/index.ts` file references a `deeboServerScriptPath`, suggesting a separate server process.  The server is not directly included in the provided file subset.\n    *   The server likely contains the core logic for running the \"Mother Agent\" and \"Scenario Agents.\"\n    *   The server exposes tools like `start` and `check` via the MCP.\n*   **Agents (src/):**\n    *   **Mother Agent (src/mother-agent.ts):**  Likely the main orchestrator of the debugging process. It probably sets up the initial debugging context and spawns Scenario Agents.\n    *   **Scenario Agent (src/scenario-agent.ts):**  These agents probably explore different debugging hypotheses and report their findings.\n*   **Utilities (src/util/):**\n    *   `agent-utils.ts`: Helper functions for working with agents.\n    *   `branch-manager.ts`: Manages Git branches, likely for isolating debugging experiments.\n    *   `logger.ts`:  Logging functionality.\n    *   `mcp.ts`:  MCP-related utilities.\n    *   `membank.ts`:  Manages the memory bank, which stores debugging progress and findings.\n    *   `observations.ts`:  Handles observations made during the debugging process.\n    *   `reports.ts`:  Generates reports on the debugging session.\n    *   `sanitize.ts`:  Sanitizes input data.\n*   **Deebo Doctor (packages/deebo-doctor):**\n    *   A command-line tool for checking system health and configuration.\n    *   Performs checks for Node.js version, Git installation, MCP tools, tool paths, configuration files, and API keys.\n*   **Deebo Setup (packages/deebo-setup):**\n    *   Includes a guide server that provides documentation and instructions for setting up Deebo.\n    *   The guide is written in Markdown (`deebo_guide.md`).\n\n**4. Notable Features or Patterns:**\n\n*   **MCP-Based Communication:** The use of MCP for inter-process communication is a core design element.\n*   **AI-Assisted Debugging:**  Integration with OpenAI/OpenRouter for analysis and potentially code suggestion.\n*   **Agent-Based Architecture:**  The use of Mother and Scenario Agents to explore different debugging paths.\n*   **Memory Bank:**  The memory bank is crucial for persisting debugging progress and allowing Deebo to resume sessions.\n*   **Exponential Backoff:** The `forceCheckSession` function uses exponential backoff with jitter for retrying checks, which is a common pattern for handling potentially flaky or slow operations.\n*   **Configuration Checks:** The `deebo-doctor` package provides a comprehensive set of checks to ensure the system is properly configured.\n*   **Guide Server:** The `deebo-setup` package includes a guide server that provides documentation and instructions for setting up Deebo.\n\n**5. Potential Use Cases:**\n\n*   **Automated Bug Finding:**  Deebo could be used to automatically identify bugs in codebases.\n*   **Root Cause Analysis:**  Deebo could help developers quickly identify the root cause of errors.\n*   **CI/CD Integration:**  Deebo could be integrated into CI/CD pipelines to automatically debug code changes.\n*   **Code Quality Improvement:**  Deebo could be used to identify potential code smells and suggest improvements.\n*   **Learning Tool:**  Deebo could be used as a learning tool to help developers understand how to debug code.\n\n**Summary of Key Files:**\n\n*   **`ci/mcp-client/index.ts`:**  The main CI client script.  It connects to the Deebo server, starts debugging sessions, monitors their progress, and optionally analyzes the results with AI.\n*   **`packages/deebo-doctor/src/checks.ts`:** Defines the system health checks performed by the `deebo-doctor` tool.\n*   **`packages/deebo-setup/src/deebo_guide.md`:** The Markdown file containing the Deebo setup guide.\n*   **`src/mother-agent.ts` and `src/scenario-agent.ts`:**  The core logic for the Mother and Scenario Agents, respectively.\n\n**Important Considerations:**\n\n*   **Incomplete Codebase:** The provided file is only a subset of the repository.  The core Deebo server logic is missing.\n*   **Prototype Status:** The repository is labeled as a \"prototype,\" so the code may be incomplete or subject to change.\n*   **Environment Variables:**  The code relies heavily on environment variables, which need to be properly configured.\n*   **MCP Tooling:**  The code requires specific MCP tools (git-mcp, desktop-commander) to be installed and configured.\n\nIn conclusion, the `snagasuri/deebo-prototype` repository is a promising prototype for an automated debugging tool that leverages AI and a multi-agent architecture.  It uses MCP for communication and provides tools for system health checks and setup guidance.  However, the provided code is only a subset of the complete system, and further investigation would be needed to fully understand its capabilities.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "google/adk-web",
        "url": "https://github.com/google/adk-web",
        "file_size_mb": 0.22,
        "repomix_file": "repomixes/google_adk-web.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:32",
        "worker_id": 26000,
        "analysis_length": 7032
      },
      "analysis": {
        "content": "Okay, let's break down this repository based on the provided information.\n\n**1. Main Purpose and Functionality:**\n\nThe repository `google/adk-web` contains the web UI (ADK Web) for the Google Agent Development Kit (ADK).  ADK Web is a developer UI that integrates with the ADK to facilitate agent development and debugging. It provides a visual interface for interacting with and inspecting the behavior of AI agents built using the ADK.  It allows developers to:\n\n*   Send messages to the agent.\n*   View the agent's responses.\n*   Inspect the agent's internal state (sessions, events, traces, artifacts).\n*   Evaluate agent performance.\n*   Debug agent behavior.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Angular:** The UI is built using Angular, a TypeScript-based front-end framework.  This is evident from the file structure (e.g., `src/app/app.module.ts`, component directories, use of `@Component` decorators) and the presence of Angular CLI configuration files.\n*   **TypeScript:** Angular uses TypeScript, which adds static typing to JavaScript.\n*   **Material Design:**  The presence of `MatButtonModule`, `MatFormFieldModule`, `MatInputModule`, `MatDialogModule` suggests the use of Angular Material for UI components and styling.\n*   **RxJS:** The use of `BehaviorSubject`, `Observable`, `switchMap`, `combineLatest` etc. indicates heavy reliance on RxJS for reactive programming, handling asynchronous operations, and managing data streams.\n*   **Node.js/npm:**  The `README.md` and `set-backend.js` files indicate the use of Node.js and npm for development, dependency management, and running the application.\n*   **JavaScript:** `clean-backend.js` and `set-backend.js` are JavaScript files used for configuring the backend URL.\n*   **string-to-color:** Used for generating colors based on strings, likely for visual differentiation in the UI.\n*   **viz-js:** Used for rendering graphs, likely for visualizing agent traces or event flows.\n\n**3. Architecture Overview:**\n\nThe repository follows a typical Angular architecture:\n\n*   **Components:** The `src/app/components` directory contains reusable UI components, such as:\n    *   `ChatComponent`: The main chat interface.\n    *   `ArtifactTabComponent`: Displays artifacts generated by the agent (images, audio, text, etc.).\n    *   `AudioPlayerComponent`:  Plays audio artifacts.\n    *   `EventTabComponent`:  Displays events that occur during agent execution.\n    *   `SessionTabComponent`:  Manages agent sessions.\n    *   `EvalTabComponent`: For evaluating agent performance.\n    *   `TraceTabComponent`: Visualizes agent traces.\n*   **Services:** The `src/app/core/services` directory contains services that handle data access, business logic, and communication with the backend API:\n    *   `AgentService`:  Handles agent-related operations (listing apps, setting the app).\n    *   `SessionService`:  Manages agent sessions (creation, retrieval, deletion).\n    *   `ArtifactService`:  Retrieves and manages artifacts.\n    *   `EventService`:  Retrieves and manages events.\n    *   `EvalService`: Handles agent evaluation.\n    *   `TraceService`: Handles agent traces.\n    *   `WebSocketService`:  Handles WebSocket communication for real-time updates.\n    *   `AudioService`, `VideoService`, `DownloadService`: Provide utility functions for handling audio, video, and downloads.\n*   **Models:** The `src/app/core/models` directory defines data models used throughout the application (e.g., `AgentRunRequest`, `Session`, `Trace`).\n*   **Modules:** `src/app/app.module.ts` and `src/app/components/component.module.ts` define Angular modules that organize the application's components, services, and dependencies.\n*   **Directives:** `src/app/directives` contains custom Angular directives, such as `ResizableDrawerDirective` and `ResizableBottomDirective`, which likely provide resizable UI elements.\n*   **Environment:** `src/env/environment.ts` and `src/env/env.ts` likely contain environment-specific configuration settings (e.g., API endpoints).\n*   **Routing:** `src/app/app-routing.module.ts` configures the application's routes.\n\n**4. Notable Features or Patterns:**\n\n*   **Tab-based Interface:** The presence of components like `ArtifactTabComponent`, `EventTabComponent`, `SessionTabComponent`, `EvalTabComponent`, and `TraceTabComponent` suggests a tabbed UI for organizing different aspects of agent development and debugging.\n*   **Real-time Updates:** The use of `WebSocketService` indicates that the UI provides real-time updates of agent state, events, and other data.\n*   **Artifact Handling:** The UI supports various artifact types (images, audio, text) and provides mechanisms for viewing, downloading, and playing them.\n*   **Agent Evaluation:** The `EvalTabComponent` and related components suggest features for evaluating agent performance using evaluation sets and metrics.\n*   **Trace Visualization:** The `TraceTabComponent` and `TraceTreeComponent` indicate that the UI can visualize agent execution traces, which is crucial for debugging.\n*   **Configuration via Environment Variables:** The `set-backend.js` script shows that the backend URL is configured using an environment variable (`npm_config_backend`), allowing for flexible deployment.\n*   **Feature Flags:** The use of `FeatureFlagService` indicates that certain features can be enabled or disabled dynamically, likely for A/B testing or controlled rollouts.\n*   **SSE (Server-Sent Events):** The `useSse` variable and `runSse` method in `ChatComponent` suggest the use of Server-Sent Events for streaming data from the backend to the UI.\n*   **Bi-directional Streaming:** The code mentions bi-directional streaming, although it also notes that restarting it is not currently supported.\n*   **OAuth Support:** The code includes a hack for OAuth authentication, suggesting that the UI can integrate with services that require OAuth.\n\n**5. Potential Use Cases:**\n\n*   **Developing and Debugging AI Agents:** The primary use case is to provide a comprehensive UI for developing, debugging, and evaluating AI agents built using the ADK.\n*   **Monitoring Agent Performance:** The UI can be used to monitor the performance of agents in real-time, identify bottlenecks, and optimize their behavior.\n*   **Evaluating Agent Performance:** The evaluation features allow developers to assess the accuracy, reliability, and other qualities of their agents.\n*   **Collaborative Agent Development:** The UI can facilitate collaborative agent development by providing a shared view of agent state and behavior.\n*   **Educational Tool:** The UI can be used as an educational tool to teach developers about AI agent development and debugging.\n\nIn summary, the `google/adk-web` repository provides a sophisticated web UI for the Google Agent Development Kit, enabling developers to build, debug, evaluate, and monitor AI agents effectively. It leverages modern web technologies like Angular, TypeScript, RxJS, and Angular Material to provide a rich and interactive user experience.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "aws/amazon-ecs-logs-collector",
        "url": "https://github.com/aws/amazon-ecs-logs-collector",
        "file_size_mb": 0.01,
        "repomix_file": "repomixes/aws_amazon-ecs-logs-collector.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:32",
        "worker_id": 17344,
        "analysis_length": 3753
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided repository information and file contents. Here's a comprehensive summary:\n\n**1. Main Purpose and Functionality:**\n\nThe `aws/amazon-ecs-logs-collector` repository provides a script (`ecs-logs-collector.sh`) designed to collect logs and system information from Amazon ECS (Elastic Container Service) instances.  Its primary purpose is to aid in troubleshooting ECS-related issues by gathering relevant data for AWS support cases.  It collects OS logs, Docker logs, ECS agent logs, and system configurations, then packages them into a compressed archive for easy retrieval.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Bash Scripting:** The core logic is implemented in a Bash script (`ecs-logs-collector.sh`).\n*   **Standard Linux Utilities:** The script relies heavily on standard Linux command-line tools like `curl`, `ls`, `tar`, `grep`, `ps`, `systemctl`, `docker`, etc., to gather the required information.\n*   **Amazon ECS:** The tool is specifically designed to collect logs and information related to the Amazon ECS service.\n\n**3. Architecture Overview:**\n\nThe repository itself is relatively simple. It consists of:\n\n*   **`ecs-logs-collector.sh` (Not included in this subset):** The main script that orchestrates the log collection process.  It's likely a shell script that executes various commands to gather logs and system information.\n*   **`.github/` directory:** Contains issue and pull request templates for contributing to the project.\n*   **`CONTRIBUTING.md`:**  Provides guidelines for contributing to the project.\n*   **`README.md`:**  Provides an overview of the project, usage instructions, and licensing information.\n\nThe script's architecture is procedural.  It executes a series of commands to collect different types of logs and system information. It then creates a tarball of the collected data.\n\n**4. Notable Features or Patterns:**\n\n*   **Two Modes of Operation:** The script supports two modes: `brief` (default) and `enable-debug`. The `enable-debug` mode attempts to enable debug logging for Docker and the ECS agent (only on Systemd systems and Amazon Linux) before collecting logs.\n*   **Automated Log Collection:**  The script automates the process of collecting various logs and system information, saving time and effort compared to manual collection.\n*   **Tarball Creation:**  The collected data is packaged into a tarball for easy transfer and analysis.\n*   **Emphasis on Security:** The `README.md` explicitly recommends removing sensitive data from the collected logs before sharing them, highlighting a concern for security.\n*   **Contribution Guidelines:** The repository includes a `CONTRIBUTING.md` file, encouraging community contributions.\n\n**5. Potential Use Cases:**\n\n*   **Troubleshooting ECS Issues:** The primary use case is to collect logs and system information to diagnose and resolve issues with Amazon ECS deployments.\n*   **Debugging ECS Agent and Docker:** The `enable-debug` mode can be used to enable more verbose logging for the ECS agent and Docker, aiding in debugging complex problems.\n*   **Gathering System Information:** The script can be used to collect a comprehensive snapshot of system configuration and logs for auditing or security analysis purposes.\n*   **Support Cases:** The collected logs are intended to be provided to AWS support to help them resolve customer issues.\n\nIn summary, the `aws/amazon-ecs-logs-collector` repository provides a valuable tool for collecting logs and system information from ECS instances, simplifying the troubleshooting process and enabling faster resolution of issues. The provided files show the project is open to contributions and emphasizes the importance of security when handling collected logs.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "gabrielchasukjin/cloi",
        "url": "https://github.com/gabrielchasukjin/cloi",
        "file_size_mb": 0.37,
        "repomix_file": "repomixes/gabrielchasukjin_cloi.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:37",
        "worker_id": 21600,
        "analysis_length": 4893
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided code and directory structure for the `gabrielchasukjin/cloi` repository. Here's a comprehensive summary:\n\n**1. Main Purpose and Functionality:**\n\nCloi (Secure Agentic Debugger) is a tool designed to help developers debug code more effectively. It appears to do this by:\n\n*   **Capturing Command Output:**  Cloi reruns shell commands and captures their output (both standard output and standard error).\n*   **Analyzing Errors with LLMs:** When errors occur, Cloi feeds the command output to a local Large Language Model (LLM) for analysis. This LLM is likely used to provide insights into the cause of the error and potential solutions.\n*   **Code Understanding and Embedding:** Uses CodeBERT to generate embeddings of code snippets, likely for semantic search and Retrieval-Augmented Generation (RAG).\n*   **Providing a CLI Interface:** Offers a command-line interface for interacting with the tool.\n*   **Automated Setup:** Includes scripts to automatically download and configure dependencies like Ollama and CodeBERT.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **JavaScript (Node.js):** The primary language for the CLI and core logic.\n*   **Python:** Used for CodeBERT embedding service, model setup, and ONNX conversion.\n*   **Transformers (Hugging Face):** Used for CodeBERT model loading, tokenization, and embedding generation.\n*   **ONNX:** Used for optimizing and running CodeBERT models.\n*   **Ollama:**  A framework for running LLMs locally. Cloi uses Ollama to run the LLM that analyzes error output.\n*   **Retrieval-Augmented Generation (RAG):**  Used for code understanding and semantic search.\n*   **Git:** Integrates with Git to understand project structure and history.\n\n**3. Architecture Overview:**\n\nThe architecture seems to be structured as follows:\n\n*   **CLI (`src/cli/index.js`):**  The entry point for user interaction.  It likely parses commands, executes them, and displays results.\n*   **Core (`src/core/index.js`):** Contains the main logic for running commands, capturing output, and interacting with the LLM.\n*   **Executor (`src/core/executor/*`):**  Handles the execution of commands and interacts with different LLM providers (Claude, Ollama).\n*   **Prompt Templates (`src/core/promptTemplates/*`):** Defines the prompts used to interact with the LLM.  These prompts likely guide the LLM in analyzing error output and generating helpful suggestions.\n*   **RAG (`src/rag/*`):** Implements retrieval-augmented generation for code understanding.\n*   **UI (`src/ui/*`):** Handles the user interface, likely using terminal output.\n*   **Utilities (`src/utils/*`):** Provides helper functions for API key management, CLI tools, Git integration, history tracking, model configuration, patch application, project utilities, provider configuration, Python environment checks, and terminal logging.\n*   **CodeBERT Service (`bin/codebert_service.py`):** A Python HTTP server that provides CodeBERT embeddings.\n*   **Setup Scripts (`bin/*_setup.py`):** Python scripts to automate the installation and configuration of dependencies like CodeBERT and Ollama.\n\n**4. Notable Features or Patterns:**\n\n*   **Local LLM Usage:** Cloi prioritizes using a local LLM (via Ollama) for privacy and potentially faster response times.\n*   **CodeBERT Integration:**  Uses CodeBERT to understand code semantics and generate embeddings.\n*   **Automated Setup:**  Includes scripts to automate the installation and configuration of dependencies.\n*   **Error Analysis:**  The core functionality revolves around analyzing command output to identify and explain errors.\n*   **Modular Design:** The code is organized into modules, making it easier to maintain and extend.\n*   **Fallback Mechanisms:** The code includes fallback mechanisms for when certain dependencies are not available or when errors occur (e.g., using RoBERTa tokenizer if CodeBERT tokenizer files are missing).\n*   **ONNX Conversion:** Converts the CodeBERT model to ONNX format for potential performance improvements.\n\n**5. Potential Use Cases:**\n\n*   **Debugging:**  The primary use case is to help developers debug code more efficiently by providing insights into errors.\n*   **Code Understanding:**  Cloi can be used to understand the semantics of code snippets.\n*   **Code Search:**  The CodeBERT embeddings can be used to search for code snippets based on their meaning.\n*   **Automated Code Review:**  Cloi could potentially be extended to perform automated code reviews.\n*   **Educational Tool:**  Cloi can be used as an educational tool to help developers learn how to debug code.\n\n**In summary, Cloi is a powerful tool that combines command execution, error analysis, and LLM integration to provide a more efficient debugging experience for developers. It leverages local LLMs, CodeBERT, and automated setup scripts to provide a comprehensive and user-friendly solution.**",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "qunarcorp/bistoury",
        "url": "https://github.com/qunarcorp/bistoury",
        "file_size_mb": 2.55,
        "repomix_file": "repomixes/qunarcorp_bistoury.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:38",
        "worker_id": 17344,
        "analysis_length": 3709
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided code repository information and file summaries. Here's a comprehensive summary:\n\n**1. Main Purpose and Functionality:**\n\n*   **Bistoury is a Java diagnostic and monitoring tool.** Based on the file names and directory structure, it appears to be designed for:\n    *   **Decompilation:** Decompiling Java bytecode (using Fernflower).\n    *   **Proxying:**  Likely acting as a proxy to intercept and analyze Java application traffic/behavior.\n    *   **UI:** Providing a user interface for interacting with the tool, visualizing data, and configuring settings.\n    *   **Debugging:** Providing debugging capabilities.\n    *   **Profiling:** Providing profiling capabilities.\n    *   **Monitoring:** Providing monitoring capabilities.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Java:** (Implied, as it's a Java diagnostic tool).\n*   **JavaScript:** The UI heavily relies on JavaScript.\n*   **jQuery:**  A significant portion of the UI uses jQuery for DOM manipulation and event handling.\n*   **Bootstrap:** The UI uses Bootstrap for styling and layout.\n*   **ECharts:** Used for creating charts and visualizations in the UI.\n*   **Handlebars:** Used for templating in the UI.\n*   **Fernflower:** Used for decompiling Java bytecode.\n*   **Potentially Websockets:** The presence of `websocket.js` suggests real-time communication capabilities.\n\n**3. Architecture Overview:**\n\nBased on the directory structure, a high-level architectural view would be:\n\n*   **`bistoury-decompiler-fernflower`:** A component responsible for decompiling Java `.class` files. It uses a modified version of the Fernflower decompiler.\n*   **`bistoury-proxy`:** A component that acts as a proxy, likely intercepting requests/responses to a Java application. It likely collects data for analysis.\n*   **`bistoury-ui`:** The user interface, built with HTML, CSS, and JavaScript. It allows users to:\n    *   View decompiled code.\n    *   Configure the proxy.\n    *   Analyze collected data (using charts, tables, etc.).\n    *   Perform debugging and profiling tasks.\n*   **`docs`:** Contains documentation in Markdown format.\n\n**4. Notable Features or Patterns:**\n\n*   **Modified Fernflower:** The tool uses a modified version of Fernflower, indicating a need for custom decompilation behavior.\n*   **Web-Based UI:** The UI is web-based, making it accessible from different platforms.\n*   **Rich UI Components:** The UI utilizes various JavaScript libraries (Bootstrap, ECharts, Handlebars) to provide a rich user experience.\n*   **Proxy-Based Monitoring:** The proxy component suggests a non-invasive way to monitor and analyze Java applications.\n*   **Comprehensive Documentation:** The presence of a `docs` directory indicates a focus on providing documentation for users.\n\n**5. Potential Use Cases:**\n\n*   **Debugging Production Issues:** Analyzing the state of a running Java application in production to diagnose problems.\n*   **Performance Profiling:** Identifying performance bottlenecks in Java code.\n*   **Security Auditing:** Examining the behavior of Java applications to identify security vulnerabilities.\n*   **Reverse Engineering:** Decompiling and analyzing Java code to understand its functionality (with appropriate ethical considerations).\n*   **Monitoring Application Health:** Tracking key metrics of a Java application to ensure its health and stability.\n*   **Troubleshooting:** Assisting developers in troubleshooting issues by providing insights into application behavior.\n\nIn summary, Bistoury appears to be a powerful tool for Java developers and operations teams, providing a comprehensive suite of features for diagnosing, monitoring, and analyzing Java applications.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "allenai/alexafsm",
        "url": "https://github.com/allenai/alexafsm",
        "file_size_mb": 0.08,
        "repomix_file": "repomixes/allenai_alexafsm.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:38",
        "worker_id": 26000,
        "analysis_length": 4832
      },
      "analysis": {
        "content": "Okay, here's a comprehensive summary of the `allenai/alexafsm` repository based on the provided code and file structure information.\n\n**1. Main Purpose and Functionality:**\n\nThe `alexafsm` library provides a framework for building complex Alexa skills using a finite state machine (FSM) approach.  It aims to simplify the development, debugging, and maintenance of Alexa skills that require sophisticated dialog state tracking.  Instead of manually managing conversation state, developers define states, transitions between states, and actions to be performed during those transitions.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Python:** The primary language.\n*   **transitions:**  A lightweight Python library for creating finite state machines.  This library handles the core FSM logic (state management, transitions, etc.).\n*   **voicelabs:** (Optional) Integrates with VoiceLabs for analytics.\n*   **JSON:** Used for handling Alexa request and response formats and for recording/playback of skill interactions.\n*   **namedtuple:** Used to define the structure of slots.\n\n**3. Architecture Overview:**\n\nThe library's architecture revolves around three core classes:\n\n*   **`SessionAttributes`:**  Represents the session data associated with an Alexa skill session.  It stores the current state of the conversation, intent, slot values, and any other relevant data.  It's responsible for serializing and deserializing session data to/from the Alexa request.\n*   **`States`:**  Defines the states of the FSM and the responses associated with each state.  Each state is represented by a method within the `States` class.  Transitions between states are defined using the `@with_transitions` decorator.\n*   **`Policy`:**  The central class that orchestrates the FSM.  It initializes the FSM based on the `States` definition, handles Alexa requests, triggers state transitions, and generates responses.  It acts as the interface between the Alexa platform and the skill's FSM logic.\n\nThe flow of execution is typically:\n\n1.  Alexa sends a request to the skill.\n2.  The `Policy` class receives the request.\n3.  The `Policy` extracts the intent and slot values from the request and updates the `SessionAttributes`.\n4.  The `Policy` uses the `transitions` library to trigger a state transition based on the intent and current state.\n5.  The `Policy` calls the method in the `States` class corresponding to the new state.\n6.  The `States` method generates a `Response` object, which is then sent back to Alexa.\n\n**4. Notable Features or Patterns:**\n\n*   **FSM-Driven Development:** The core principle is to model the skill's dialog flow as a finite state machine.\n*   **Declarative Transitions:** The `@with_transitions` decorator provides a declarative way to define state transitions, making the code more readable and maintainable.\n*   **Separation of Concerns:** The `SessionAttributes`, `States`, and `Policy` classes promote a clear separation of concerns, making the code easier to understand and modify.\n*   **Validation:** The `validate` function allows developers to check for inconsistencies in the FSM definition, such as unhandled intents or unreachable states.\n*   **Graph Visualization:** The library provides tools to visualize the FSM as a graph, which can be helpful for understanding and debugging complex dialog flows.\n*   **Record/Playback for Testing:** The `recordable` decorator and related functions enable developers to record and replay skill interactions, making it easier to test and verify changes to the skill's logic.\n*   **JSON Serialization:** The `to_json` methods ensure proper serialization of data for communication with the Alexa platform.\n*   **Monkey Patching:** The `make_json_serializable.py` file uses monkey patching to extend the default JSON encoder to handle objects with a `to_json` method.\n\n**5. Potential Use Cases:**\n\n*   **Complex Dialog Skills:** Any Alexa skill that requires managing a complex conversation flow with multiple states and transitions.\n*   **Skills with Multi-Turn Interactions:** Skills that require users to provide information over multiple turns.\n*   **Skills with Dynamic Behavior:** Skills that need to adapt their behavior based on user input and the current state of the conversation.\n*   **Skills Requiring State Persistence:** Skills that need to maintain state across multiple sessions.\n*   **Skills that need to integrate with external APIs or databases:** The `prepare` methods in the transitions can be used to perform actions such as querying a database or calling an API.\n\nIn summary, `alexafsm` is a well-structured library that provides a powerful and flexible framework for building complex Alexa skills using the finite state machine paradigm. It offers a range of features and tools to simplify development, debugging, and maintenance.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "googleapis/cloud-debug-nodejs",
        "url": "https://github.com/googleapis/cloud-debug-nodejs",
        "file_size_mb": 0.53,
        "repomix_file": "repomixes/googleapis_cloud-debug-nodejs.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:39",
        "worker_id": 18072,
        "analysis_length": 5708
      },
      "analysis": {
        "content": "Here's a comprehensive summary of the `googleapis/cloud-debug-nodejs` repository based on the provided information:\n\n**1. Main Purpose and Functionality:**\n\nThe `cloud-debug-nodejs` repository provides a Node.js agent for Google Cloud Debugger.  Its primary function is to enable developers to debug Node.js applications running in production on Google Cloud Platform (GCP) without stopping or significantly impacting the application's performance.  It allows setting breakpoints and inspecting variables in real-time.  The agent interacts with the Google Cloud Debugger service to facilitate this debugging process.  It also supports logpoints, which are breakpoints that log information without halting execution.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Node.js:** The agent itself is written in Node.js.\n*   **TypeScript:**  A significant portion of the code, especially in the `src/agent` directory, is written in TypeScript.\n*   **V8 Debugging Protocol:** The agent uses the V8 JavaScript engine's debugging protocol to interact with the running Node.js application.  It leverages the `v8-inspector` API.\n*   **Express:** Used in samples and potentially for internal communication or testing.\n*   **@google-cloud/common:**  A common library used across Google Cloud Node.js client libraries for authentication and other shared functionalities.\n*   **gcp-metadata:**  Used to retrieve metadata about the GCP environment where the application is running.\n*   **Firebase Realtime Database:** Used as an option to store breakpoint data.\n*   **Source Maps:** The agent supports source maps to enable debugging of transpiled code (e.g., TypeScript, CoffeeScript) by mapping back to the original source code.\n*   **Mocha:** Used as the testing framework.\n*   **Nock:** Used for mocking HTTP requests in tests.\n\n**3. Architecture Overview:**\n\nThe repository's structure suggests a modular architecture:\n\n*   **`src/agent`:** This is the core of the debugging agent.  It contains modules for:\n    *   **`config.ts`:**  Handles configuration settings for the agent.\n    *   **`controller.ts`:**  Manages the communication with the Google Cloud Debugger service.\n    *   **`debuglet.ts`:**  Represents a debuglet, which is a unit of debugging functionality.\n    *   **`firebase-controller.ts`:** Manages the interaction with Firebase Realtime Database.\n    *   **`io/scanner.ts`:**  Scans the file system to locate source files and source maps.\n    *   **`io/sourcemapper.ts`:**  Handles source map processing and mapping.\n    *   **`state/inspector-state.ts`:** Manages the state of the V8 inspector.\n    *   **`v8/debugapi.ts` and `v8/inspector-debugapi.ts`:**  Wrappers around the V8 debugging API.\n*   **`src/client`:**  Contains client-side code, likely related to interacting with the Debugger service.\n*   **`src/debuggee.ts`:**  Represents the debuggee, which is the application being debugged.\n*   **`src/index.ts`:**  The main entry point for the agent.\n*   **`samples`:**  Provides example code demonstrating how to use the agent.\n*   **`system-test`:** Contains end-to-end tests that verify the agent's functionality in a real environment.\n*   **`test`:**  Contains unit tests for the various modules.\n\nThe agent likely works by:\n\n1.  Registering the application as a debuggee with the Google Cloud Debugger service.\n2.  Polling the Debugger service for breakpoints.\n3.  When a breakpoint is received, using the V8 debugging API to set a breakpoint in the running application.\n4.  When the breakpoint is hit, capturing the application's state (variables, stack trace).\n5.  Sending the captured state back to the Debugger service.\n\n**4. Notable Features or Patterns:**\n\n*   **Source Map Support:**  Robust support for source maps is crucial for debugging modern JavaScript applications that are often transpiled from other languages.\n*   **V8 Inspector API:**  The agent leverages the V8 Inspector API for low-level debugging operations.\n*   **Configuration Options:**  The agent is configurable, allowing developers to customize its behavior.  The `config.ts` file likely defines these options.\n*   **Modular Design:**  The code is organized into well-defined modules, making it easier to maintain and extend.\n*   **Extensive Testing:**  The repository includes a comprehensive suite of unit and system tests.\n*   **Firebase Integration:** The agent supports storing breakpoint data in Firebase Realtime Database, providing an alternative to the default storage mechanism.\n*   **Cloud Run Support:** The agent supports debugging applications running on Cloud Run.\n*   **Side-Effect-Free Evaluation:** The agent supports evaluating expressions without causing side effects in the application.\n*   **Path Resolver:** The agent provides a `pathResolver` configuration option to support debugging files that have ambiguous paths.\n\n**5. Potential Use Cases:**\n\n*   **Debugging Production Applications:**  The primary use case is debugging Node.js applications running in production on GCP.\n*   **Troubleshooting Performance Issues:**  The agent can be used to identify performance bottlenecks by setting breakpoints in critical code sections and inspecting variables.\n*   **Diagnosing Errors:**  The agent can be used to diagnose errors by setting breakpoints at the point where the error occurs and examining the application's state.\n*   **Understanding Application Behavior:**  The agent can be used to gain a deeper understanding of how an application works by setting breakpoints and tracing the execution flow.\n*   **Debugging Microservices:**  The agent can be used to debug microservices running on GCP.\n*   **Debugging Cloud Functions:** The agent can be used to debug Cloud Functions.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "CNTRUN/Termux-command",
        "url": "https://github.com/CNTRUN/Termux-command",
        "file_size_mb": 0.0,
        "repomix_file": "repomixes/CNTRUN_Termux-command.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:41",
        "worker_id": 21600,
        "analysis_length": 3230
      },
      "analysis": {
        "content": "Based on the provided information, here's a comprehensive summary of the CNTRUN/Termux-command repository:\n\n**1. Main Purpose and Functionality:**\n\nThe primary purpose of this repository, based on the provided \"file_summary,\" is to provide a curated and condensed representation of a subset of the repository's codebase. This subset is deemed the most important context for automated analysis, code review, and other AI-driven processes.  The repository aims to be easily consumable by AI systems.  The name \"Termux-command\" suggests it likely contains commands or scripts intended for use within the Termux environment (a terminal emulator for Android). However, without the actual file contents, this is speculative.\n\n**2. Key Technologies and Frameworks Used:**\n\nWithout the file contents, it's impossible to definitively determine the technologies and frameworks used. However, the `<notes>` section provides clues:\n\n*   The repository likely contains files written in Python (`.py`), JavaScript (`.js`), TypeScript (`.ts`), Go (`.go`), Rust (`.rs`), and Markdown (`.md`). This suggests a diverse range of potential technologies.\n*   The repository is likely managed using Git, as the files are sorted by Git change count.\n*   The use of Repomix indicates a focus on AI-driven analysis and code review.\n\n**3. Architecture Overview:**\n\nGiven the lack of file content and the empty `<directory_structure>`, it's impossible to provide a meaningful architecture overview.  We can only infer that the architecture is likely organized around the commands or scripts intended for Termux.\n\n**4. Notable Features or Patterns:**\n\n*   **Repomix Integration:** The repository leverages Repomix to create a packed representation of the codebase for AI analysis. This is a key feature.\n*   **Selective Inclusion:**  The repository only includes a subset of files, based on file extensions and exclusion rules (e.g., `.gitignore`).\n*   **AI-Focused Design:** The repository is explicitly designed for consumption by AI systems.\n*   **Git-Based Organization:** Files are sorted by Git change count, potentially highlighting the most actively developed or modified files.\n\n**5. Potential Use Cases:**\n\n*   **Automated Code Review:**  AI systems can use the packed representation to automatically review the code for potential bugs, security vulnerabilities, or style violations.\n*   **Code Understanding and Documentation:** AI can analyze the code to generate documentation or provide insights into the codebase's functionality.\n*   **Bug Detection and Prevention:** AI can identify patterns in the code that are associated with bugs or security vulnerabilities.\n*   **Code Generation and Completion:** AI can use the code as a basis for generating new code or completing existing code.\n*   **Learning Termux Commands:** If the files contain scripts, users could learn how to automate tasks within the Termux environment.\n\n**Limitations:**\n\nThis analysis is heavily limited by the absence of actual file contents. The conclusions are based primarily on the metadata provided in the \"file_summary\" and the file extensions mentioned in the `<notes>` section. A more accurate and detailed analysis would require access to the actual code files.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "pandening/Java-debug-tool",
        "url": "https://github.com/pandening/Java-debug-tool",
        "file_size_mb": 0.17,
        "repomix_file": "repomixes/pandening_Java-debug-tool.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:44",
        "worker_id": 17344,
        "analysis_length": 4353
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided code and will provide a comprehensive summary based on the available information.\n\n**1. Main Purpose and Functionality:**\n\nThe repository `pandening/Java-debug-tool` appears to be focused on providing a low-overhead sampling profiler for Java applications.  Specifically, it leverages the `async-profiler` tool.  The main goal is to identify performance bottlenecks in Java applications without introducing significant overhead or bias. It supports profiling CPU usage, memory allocation, lock contention, and hardware performance counters. It aims to provide accurate stack traces, including Java, native, JVM, and kernel code.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **async-profiler:** This is the core technology. It's a sampling profiler specifically designed for Java.\n*   **HotSpot JVM:** The profiler relies on HotSpot-specific APIs for collecting stack traces and tracking memory allocations.  It's compatible with OpenJDK and Oracle JDK.\n*   **perf_events (Linux):**  On Linux, `perf_events` is used to capture call stacks, including kernel functions.\n*   **TLAB (Thread-Local Allocation Buffers):** The allocation profiling feature uses TLAB-driven sampling to minimize overhead.\n*   **JVMTI (Java Virtual Machine Tool Interface):** The profiler is loaded as a JVMTI agent to interact with the JVM.\n*   **Flame Graphs:** The profiler supports generating Flame Graphs for visualizing profiling results.\n\n**3. Architecture Overview:**\n\nThe architecture is based on a Java agent (`libasyncProfiler.so`) that is loaded into the target JVM process.  The agent uses HotSpot-specific APIs and, on Linux, `perf_events` to collect profiling data.  A helper script (`profiler.sh`) is provided to simplify the process of attaching the agent, starting and stopping profiling, and generating reports.\n\nThe profiler works by:\n\n*   **Sampling:**  It periodically samples the call stacks of running threads.\n*   **Event Handling:** It can be configured to profile different events, such as CPU cycles, memory allocations, or lock contention.\n*   **Data Collection:**  It collects stack traces and associated metrics.\n*   **Reporting:**  It generates reports in various formats, including text summaries, call traces, flat profiles, Java Flight Recorder (JFR) format, collapsed stacks (for Flame Graphs), and SVG Flame Graphs.\n\n**4. Notable Features or Patterns:**\n\n*   **Low Overhead:**  The profiler is designed to minimize performance impact through sampling and TLAB-driven allocation profiling.\n*   **Safepoint Bias Avoidance:** It avoids the safepoint bias problem that can affect other Java profilers.\n*   **Comprehensive Stack Traces:** It captures stack traces that include Java, native, JVM, and kernel code.\n*   **Flame Graph Support:**  It provides built-in support for generating Flame Graphs for easy visualization of profiling results.\n*   **Flexible Configuration:**  It offers a variety of command-line options to control profiling behavior, such as the profiling event, interval, stack depth, and output format.\n*   **Container Support:**  It can profile Java processes running in Docker or LXC containers.\n*   **Agent-based:** It operates as a Java agent, allowing it to be attached to running JVM processes.\n*   **jattach:** Uses `jattach` to dynamically attach the agent to a running JVM.\n\n**5. Potential Use Cases:**\n\n*   **Performance Bottleneck Identification:**  Identifying CPU-intensive methods, excessive memory allocations, lock contention issues, and other performance bottlenecks in Java applications.\n*   **Production Profiling:**  Profiling Java applications in production environments without introducing significant overhead.\n*   **Application Startup Optimization:**  Profiling application startup time to identify areas for improvement.\n*   **Memory Leak Detection:**  Identifying memory leaks by tracking object allocations.\n*   **Lock Contention Analysis:**  Analyzing lock contention to improve concurrency performance.\n*   **Understanding Application Behavior:**  Gaining insights into the runtime behavior of Java applications.\n*   **Troubleshooting Performance Issues:** Diagnosing and resolving performance problems in Java applications.\n*   **Profiling in Containerized Environments:** Analyzing the performance of Java applications running in Docker or LXC containers.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "raga-ai-hub/RagaAI-Catalyst",
        "url": "https://github.com/raga-ai-hub/RagaAI-Catalyst",
        "file_size_mb": 1.04,
        "repomix_file": "repomixes/raga-ai-hub_RagaAI-Catalyst.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:46:01",
        "worker_id": 26000,
        "analysis_length": 14250
      },
      "analysis": {
        "content": "```python\npt,\n            generation_config=genai.GenerationConfig(\n                temperature=temperature,\n                max_output_tokens=max_tokens\n            )\n        )\n        return response.text\n    except Exception as e:\n        print(f\"Error with Google GenerativeAI: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_async_google_generativeai_response\")\nasync def _get_async_google_generativeai_response(\n    prompt,\n    model, \n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get async response from Google GenerativeAI\n    \"\"\"\n    try:\n        model = genai.GenerativeModel(model)\n        response = await model.generate_content_async(\n            prompt,\n            generation_config=genai.GenerationConfig(\n                temperature=temperature,\n                max_output_tokens=max_tokens\n            )\n        )\n        return response.text\n    except Exception as e:\n        print(f\"Error with async Google GenerativeAI: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_chat_google_generativeai_response\")\ndef _get_chat_google_generativeai_response(\n    prompt,\n    model, \n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get response from ChatGoogleGenerativeAI\n    \"\"\"\n    try:\n        chat = ChatGoogleGenerativeAI(model=model, temperature=temperature, max_output_tokens=max_tokens)\n        messages = [\n            SystemMessage(content=\"You are a helpful assistant.\"),\n            HumanMessage(content=prompt),\n        ]\n        response = chat.invoke(messages).content\n        return response\n    except Exception as e:\n        print(f\"Error with ChatGoogleGenerativeAI: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_async_chat_google_generativeai_response\")\nasync def _get_async_chat_google_generativeai_response(\n    prompt,\n    model, \n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get async response from ChatGoogleGenerativeAI\n    \"\"\"\n    try:\n        chat = ChatGoogleGenerativeAI(model=model, temperature=temperature, max_output_tokens=max_tokens)\n        messages = [\n            SystemMessage(content=\"You are a helpful assistant.\"),\n            HumanMessage(content=prompt),\n        ]\n        response = await chat.ainvoke(messages)\n        return response.content\n    except Exception as e:\n        print(f\"Error with async ChatGoogleGenerativeAI: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_chat_vertexai_response\")\ndef _get_chat_vertexai_response(\n    prompt,\n    model, \n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get response from ChatVertexAI\n    \"\"\"\n    try:\n        chat = ChatVertexAI(model_name=model, temperature=temperature, max_output_tokens=max_tokens)\n        messages = [\n            SystemMessage(content=\"You are a helpful assistant.\"),\n            HumanMessage(content=prompt),\n        ]\n        response = chat.invoke(messages).content\n        return response\n    except Exception as e:\n        print(f\"Error with ChatVertexAI: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_async_chat_vertexai_response\")\nasync def _get_async_chat_vertexai_response(\n    prompt,\n    model, \n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get async response from ChatVertexAI\n    \"\"\"\n    try:\n        chat = ChatVertexAI(model_name=model, temperature=temperature, max_output_tokens=max_tokens)\n        messages = [\n            SystemMessage(content=\"You are a helpful assistant.\"),\n            HumanMessage(content=prompt),\n        ]\n        response = await chat.ainvoke(messages)\n        return response.content\n    except Exception as e:\n        print(f\"Error with async ChatVertexAI: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_anthropic_response\")\ndef _get_anthropic_response(\n    anthropic_client,\n    prompt,\n    model,\n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get response from Anthropic\n    \"\"\"\n    try:\n        response = anthropic_client.messages.create(\n            model=model,\n            max_tokens=max_tokens,\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            temperature=temperature\n        )\n        return response.content[0].text\n    except Exception as e:\n        print(f\"Error with Anthropic: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_async_anthropic_response\")\nasync def _get_async_anthropic_response(\n    async_anthropic_client,\n    prompt,\n    model,\n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get async response from Anthropic\n    \"\"\"\n    try:\n        response = await async_anthropic_client.messages.create(\n            model=model,\n            max_tokens=max_tokens,\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            temperature=temperature\n        )\n        return response.content[0].text\n    except Exception as e:\n        print(f\"Error with async Anthropic: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_groq_response\")\ndef _get_groq_response(\n    groq_client,\n    prompt,\n    model,\n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get response from Groq\n    \"\"\"\n    try:\n        response = groq_client.chat.completions.create(\n            model=model,\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            max_tokens=max_tokens,\n            temperature=temperature\n        )\n        return response.choices[0].message.content\n    except Exception as e:\n        print(f\"Error with Groq: {str(e)}\")\n        return None\n\n@trace_llm(name=\"_get_async_groq_response\")\nasync def _get_async_groq_response(\n    async_groq_client,\n    prompt,\n    model,\n    temperature,\n    max_tokens\n    ):\n    \"\"\"\n    Get async response from Groq\n    \"\"\"\n    try:\n        response = await async_groq_client.chat.completions.create(\n            model=model,\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            max_tokens=max_tokens,\n            temperature=temperature\n        )\n        return response.choices[0].message.content\n    except Exception as e:\n        print(f\"Error with async Groq: {str(e)}\")\n        return None\n</file>\n\n<file path=\"examples/all_llm_provider/config.py\">\nimport os\nfrom dotenv import load_dotenv\n\nload_dotenv()\n\n# RagaAI Catalyst setup\nCATALYST_ACCESS_KEY = os.getenv(\"CATALYST_ACCESS_KEY\")\nCATALYST_SECRET_KEY = os.getenv(\"CATALYST_SECRET_KEY\")\nCATALYST_BASE_URL = os.getenv(\"CATALYST_BASE_URL\")\nPROJECT_NAME = os.getenv(\"PROJECT_NAME\")\nDATASET_NAME = os.getenv(\"DATASET_NAME\")\n\n# LLM setup\nPROMPT = \"What is the capital of France?\"\nMODEL = \"gpt-3.5-turbo\"\nPROVIDER = \"openai\"\nTEMPERATURE = 0.7\nMAX_TOKENS = 256\n</file>\n\n<file path=\"examples/all_llm_provider/run_all_llm_provider.py\">\nimport asyncio\nimport os\nimport sys\nsys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))\n\nfrom ragaai_catalyst import RagaAICatalyst, init_tracing\nfrom ragaai_catalyst.tracers import Tracer\nfrom examples.all_llm_provider.all_llm_provider import get_llm_response\nfrom examples.all_llm_provider.config import (\n    CATALYST_ACCESS_KEY,\n    CATALYST_SECRET_KEY,\n    CATALYST_BASE_URL,\n    PROJECT_NAME,\n    DATASET_NAME,\n    PROMPT,\n    MODEL,\n    PROVIDER,\n    TEMPERATURE,\n    MAX_TOKENS\n)\n\nasync def main():\n    \"\"\"\n    Main function to run the LLM response retrieval and tracing.\n    \"\"\"\n    # Initialize RagaAI Catalyst\n    catalyst = RagaAICatalyst(\n        access_key=CATALYST_ACCESS_KEY,\n        secret_key=CATALYST_SECRET_KEY,\n        base_url=CATALYST_BASE_URL\n    )\n\n    # Initialize tracing\n    tracer = Tracer(\n        project_name=PROJECT_NAME,\n        dataset_name=DATASET_NAME,\n        tracer_type=\"default\"\n    )\n    init_tracing(catalyst=catalyst, tracer=tracer)\n\n    # Get LLM response\n    response = await get_llm_response(\n        prompt=PROMPT,\n        model=MODEL,\n        provider=PROVIDER,\n        temperature=TEMPERATURE,\n        max_tokens=MAX_TOKENS,\n        async_llm=True\n    )\n\n    # Print the response\n    print(f\"LLM Response: {response}\")\n\nif __name__ == \"__main__\":\n    asyncio.run(main())\n</file>\n\n<file path=\"examples/crewai/scifi_writer/scifi_writer.py\">\nimport os\nfrom crewai import Agent, Task, Crew\nfrom dotenv import load_dotenv\n\nload_dotenv()\n\nos.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\")\n\n# Define your agents with enhanced role-playing descriptions\nresearcher = Agent(\n    role='Researcher',\n    goal='Gather information about a specific science fiction topic',\n    backstory=\"\"\"You are an expert researcher, skilled in gathering information from various sources.\n    You are meticulous and thorough, ensuring no stone is left unturned.\"\"\",\n    verbose=True,\n    allow_delegation=False\n)\n\nwriter = Agent(\n    role='Writer',\n    goal='Craft a compelling science fiction story based on research',\n    backstory=\"\"\"You are a creative writer with a passion for science fiction.\n    You excel at weaving intricate plots and developing believable characters in futuristic settings.\"\"\",\n    verbose=True,\n    allow_delegation=True\n)\n\n# Create tasks for your agents\nresearch_task = Task(\n    description='Research the concept of time travel, focusing on its paradoxes and potential technologies.',\n    agent=researcher\n)\n\nwrite_task = Task(\n    description='Write a short science fiction story about a character who gets lost in time due to a time travel experiment gone wrong.',\n    agent=writer\n)\n\n# Instantiate your crew with a sequential process\ncrew = Crew(\n    agents=[researcher, writer],\n    tasks=[research_task, write_task],\n    verbose=2 # You can set it to 1 or 2 to different logging levels\n)\n\n# Get your crew to work!\nresult = crew.kickoff()\n\nprint(\"######################\")\nprint(result)\n</file>\n\n<file path=\"examples/custom_agents/travel_agent/agents.py\">\nfrom crewai import Agent\nfrom tools import WeatherTool, FlightBooker, HotelBooker\n\nclass TravelAgents():\n\n  @staticmethod\n  def create_travel_agent():\n    return Agent(\n        role='Travel Agent',\n        goal='Plan and book the perfect trip for customers',\n        backstory=\"\"\"You are a seasoned travel agent with years of experience.\n        You are excellent at understanding customer needs and preferences,\n        and then planning and booking travel arrangements that exceed\n        expectations. You have access to a wide range of tools and resources\n        to help you plan the perfect trip, including weather information,\n        flight booking, and hotel booking.\"\"\",\n        verbose=True,\n        tools=[\n            WeatherTool.get_weather,\n            FlightBooker.book_flight,\n            HotelBooker.book_hotel\n        ]\n    )\n\n  @staticmethod\n  def create_customer_service_agent():\n    return Agent(\n        role='Customer Service Agent',\n        goal='Assist customers with any issues or questions they may have',\n        backstory=\"\"\"You are a friendly and helpful customer service agent.\n        You are excellent at resolving customer issues and answering questions.\n        You have access to a wide range of tools and resources to help you\n        assist customers, including weather information, flight booking, and\n        hotel booking.\"\"\",\n        verbose=True,\n        tools=[\n            WeatherTool.get_weather,\n            FlightBooker.book_flight,\n            HotelBooker.book_hotel\n        ]\n    )\n</file>\n\n<file path=\"examples/custom_agents/travel_agent/config.py\">\nimport os\nfrom dotenv import load_dotenv\n\nload_dotenv()\n\n# RagaAI Catalyst setup\nCATALYST_ACCESS_KEY = os.getenv(\"CATALYST_ACCESS_KEY\")\nCATALYST_SECRET_KEY = os.getenv(\"CATALYST_SECRET_KEY\")\nCATALYST_BASE_URL = os.getenv(\"CATALYST_BASE_URL\")\nPROJECT_NAME = os.getenv(\"PROJECT_NAME\")\nDATASET_NAME = os.getenv(\"DATASET_NAME\")\n</file>\n\n<file path=\"examples/custom_agents/travel_agent/main.py\">\nimport os\nimport sys\nsys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))\n\nfrom crewai import Crew, Task\nfrom agents import TravelAgents\nfrom config import (\n    CATALYST_ACCESS_KEY,\n    CATALYST_SECRET_KEY,\n    CATALYST_BASE_URL,\n    PROJECT_NAME,\n    DATASET_NAME\n)\nfrom ragaai_catalyst import RagaAICatalyst, init_tracing\nfrom ragaai_catalyst.tracers import Tracer\nfrom dotenv import load_dotenv\n\nload_dotenv()\n\ndef main():\n  # Initialize RagaAI Catalyst\n  catalyst = RagaAICatalyst(\n      access_key=CATALYST_ACCESS_KEY,\n      secret_key=CATALYST_SECRET_KEY,\n      base_url=CATALYST_BASE_URL\n  )\n\n  # Initialize tracing\n  tracer = Tracer(\n      project_name=PROJECT_NAME,\n      dataset_name=DATASET_NAME,\n      tracer_type=\"default\"\n  )\n  init_tracing(catalyst=catalyst, tracer=tracer)\n\n  # Create agents\n  travel_agent = TravelAgents.create_travel_agent()\n  customer_service_agent = TravelAgents.create_customer_service_agent()\n\n  # Create tasks\n  task1 = Task(\n      description=\"\"\"Plan a trip to Hawaii for a week. The customer wants\n      to visit the volcanoes and relax on the beach. Book flights and\n      hotels accordingly.\"\"\",\n      agent=travel_agent\n  )\n\n  task2 = Task(\n      description=\"\"\"The customer has a question about their booking.\n      Answer their question and resolve any issues they may have.\"\"\",\n      agent=customer_service_agent\n  )\n\n  # Create crew\n  crew = Crew(\n      agents=[travel_agent, customer_service_agent],\n      tasks=[task1, task2],\n      verbose=2\n  )\n\n  # Run crew\n  result = crew.kickoff()\n  print(result)\n\nif __name__ == \"__main__\":\n  main()\n</file>\n\n<file path=\"examples/custom_agents/travel_agent/tools.py\">\nfrom crewai import Tool\n\nclass WeatherTool():\n  @staticmethod\n  def get_weather(location: str) -> str:\n    \"\"\"Useful to get weather information about a specific location\"\"\"\n    return f\"The weather in {location} is sunny with a temperature of 25 degrees Celsius.\"\n\nclass FlightBooker():\n  @staticmethod\n  def book_flight(destination: str, date: str) -> str:\n    \"\"\"Useful to book a flight to a specific destination on a specific date\"\"\"\n    return f\"Flight to {destination} booked for {date}.\"\n\nclass HotelBooker():\n  @staticmethod\n  def book_hotel(location: str, date: str) -> str:\n    \"\"\"Useful to book a hotel in a specific location on a specific date\"\"\"\n    return f\"Hotel in {location} booked for {date}.\"\n</file>\n\n<file path=\"examples/haystack/news_fetching/news_fetching.py\">\nimport os\nimport sys\nsys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))\n\nfrom haystack.document_stores import InMemoryDocumentStore",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "comet-ml/opik",
        "url": "https://github.com/comet-ml/opik",
        "file_size_mb": 3.08,
        "repomix_file": "repomixes/comet-ml_opik.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:46:03",
        "worker_id": 21600,
        "analysis_length": 5403
      },
      "analysis": {
        "content": "Okay, here's a comprehensive analysis of the provided code snippet, which appears to be documentation or example code for the `opik` library.\n\n**1. Main Purpose and Functionality**\n\nThe primary purpose of the `opik` library, as evidenced by the code, is to provide a platform for **tracking, tracing, and evaluating Large Language Model (LLM) calls and applications.** It aims to help developers:\n\n*   **Log LLM calls:** Capture details about interactions with LLMs, including inputs, outputs, metadata, and usage information (tokens, duration).\n*   **Trace application flow:**  Understand the sequence of operations within an LLM-powered application, beyond just the LLM API calls themselves.\n*   **Evaluate LLM performance:**  Assess the quality and appropriateness of LLM responses using metrics like moderation.\n*   **Manage datasets:** Create, store, and manage datasets of test cases for evaluating LLMs.\n*   **Version control prompts:** Store and version prompts used with LLMs.\n\n**2. Key Technologies and Frameworks Used**\n\nBased on the code, the `opik` library interacts with and integrates with the following technologies:\n\n*   **Python:** The primary language used.\n*   **Ollama:** A library for running LLMs locally.  The code provides examples of tracing Ollama calls.\n*   **OpenAI API:** The code shows how to use the `opik` integration to trace calls made to the OpenAI API.\n*   **LangChain:** A framework for building LLM-powered applications.  `opik` provides an integration for tracing LangChain chains.\n*   **LlamaIndex:** A framework for indexing and querying data using LLMs. `opik` provides an integration for tracing LlamaIndex applications.\n*   **Pandas:** Used for data manipulation and inserting data into datasets.\n*   **JSONL:** A format for storing structured data, used for importing data into datasets.\n*   **LiteLLM:** Mentioned in the context of customizing models for moderation, suggesting `opik` can work with models supported by LiteLLM.\n\n**3. Architecture Overview**\n\nWhile the code snippet doesn't reveal the internal architecture of `opik`, we can infer some aspects:\n\n*   **Client-Server Model:** The `opik configure` command and the mention of a hosted platform suggest a client-server architecture. The `opik` library acts as a client, sending data to a central `opik` server (which can be self-hosted or hosted by Comet).\n*   **Integrations:** The library provides integrations for popular LLM frameworks (LangChain, LlamaIndex, OpenAI). These integrations likely use callbacks or wrappers to intercept and log LLM calls.\n*   **Tracing Mechanism:** The `@track` decorator and the `OpikTracer` class suggest a tracing mechanism that involves wrapping function calls and recording their inputs, outputs, and execution time.\n*   **Data Storage:** The library stores traces, datasets, and prompts. It likely uses a database or other persistent storage mechanism on the server side.\n*   **Evaluation Metrics:** The `Moderation` metric indicates that the library has a module for evaluating LLM responses. This likely involves using LLMs themselves as judges.\n\n**4. Notable Features or Patterns**\n\n*   **Tracing Integrations:** The library provides integrations for popular LLM frameworks, making it easy to track LLM calls without modifying the core application code.\n*   **`@track` Decorator:** This decorator provides a simple way to track arbitrary function calls, allowing developers to trace the entire application flow.\n*   **Dataset Management:** The library provides a way to create, store, and manage datasets of test cases.\n*   **Prompt Versioning:** The library allows developers to store and version prompts, making it easier to track changes to prompts over time.\n*   **Moderation Metric:** The library provides a metric for evaluating the appropriateness of LLM responses.\n*   **Asynchronous Scoring:** The `ascore` method suggests support for asynchronous evaluation of LLM responses.\n*   **Deduplication:**  The library automatically deduplicates items inserted into datasets.\n*   **\"Fire and Forget\" Dataset Management:** The combination of deduplication and `get_or_create_dataset` allows for a simplified dataset management workflow.\n*   **Configuration:** The `opik configure` command suggests a configuration system for setting API keys, server URLs, and other settings.\n\n**5. Potential Use Cases**\n\nThe `opik` library can be used in a variety of use cases, including:\n\n*   **Debugging LLM Applications:** Identifying performance bottlenecks and errors in LLM applications.\n*   **Evaluating LLM Performance:** Assessing the quality and appropriateness of LLM responses.\n*   **Improving LLM Applications:** Using tracing data to identify areas where LLM applications can be improved.\n*   **Monitoring LLM Applications:** Tracking the performance of LLM applications over time.\n*   **Building LLM-Powered Products:** Providing a platform for building and managing LLM-powered products.\n*   **A/B Testing:** Comparing the performance of different LLM models or prompts.\n*   **Compliance:** Ensuring that LLM applications are compliant with content policies.\n*   **Reproducibility:** Ensuring that LLM experiments are reproducible.\n\nIn summary, `opik` is a comprehensive platform for tracking, tracing, and evaluating LLM applications. It provides a variety of features and integrations that make it easy to build and manage LLM-powered products.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "comet-ml/opik",
        "url": "https://github.com/comet-ml/opik",
        "file_size_mb": 2.82,
        "repomix_file": "repomixes/comet-ml_opik.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:46:03",
        "worker_id": 17344,
        "analysis_length": 5362
      },
      "analysis": {
        "content": "Okay, let's break down the `comet-ml/opik` repository based on the provided code snippets.\n\n**1. Main Purpose and Functionality**\n\nBased on the code, `opik` appears to be a Python SDK for interacting with a Comet-ML platform (or a similar service) focused on evaluating and optimizing Large Language Models (LLMs) and conversational AI applications.  It provides tools for:\n\n*   **Dataset Management:** Creating, populating, and managing datasets of input/output examples for LLM evaluation.\n*   **Experiment Tracking:**  Creating and tracking experiments to evaluate LLM performance.  This includes logging experiment configurations, prompts, and results.\n*   **Evaluation:**  Evaluating LLM outputs against expected outputs using various metrics.\n*   **Feedback and Scoring:**  Logging feedback scores (metrics) for individual traces, spans, and experiment items.\n*   **Guardrails:** Implementing and enforcing guardrails to ensure LLM outputs adhere to specific safety and quality standards (e.g., preventing PII exposure, restricting topics).\n*   **Thread Evaluation:** Evaluating conversational threads for coherence, user frustration, and session completeness.\n*   **Optimization:** Creating and managing optimization processes for LLMs.\n\n**2. Key Technologies and Frameworks Used**\n\n*   **Python:** The SDK is written in Python.\n*   **Comet-ML (or similar):**  The SDK interacts with a Comet-ML-like backend for experiment tracking, data storage, and evaluation.  The `opik_client` object is a central component for interacting with this backend.\n*   **pytest:** Used for writing and running end-to-end (E2E) tests.\n*   **UUID:** Used for generating unique identifiers (e.g., for threads).\n*   **mock:** Used for mocking objects in tests.\n\n**3. Architecture Overview**\n\nThe architecture seems to follow a client-server model:\n\n*   **Client (opik SDK):**  The Python SDK provides a client-side interface for users to define datasets, experiments, evaluation tasks, and guardrails.  It handles the logic for interacting with the backend.\n*   **Server (Comet-ML Backend):**  A backend service (likely Comet-ML) stores the data, performs evaluations, and provides APIs for querying and managing experiments, datasets, traces, and spans.\n\nThe SDK uses a REST API (`opik_client._rest_client`) to communicate with the backend.\n\nKey components:\n\n*   **`opik.Opik`:**  The main client class for interacting with the Comet-ML backend.\n*   **`Dataset`:**  Represents a dataset of input/output examples.\n*   **`Experiment`:** Represents an experiment for evaluating LLM performance.\n*   **`Trace` and `Span`:**  Represent execution traces and spans within those traces, used for tracking the flow of execution and logging data.\n*   **`Guardrail`:**  Represents a set of rules and checks to ensure LLM outputs are safe and compliant.\n*   **`metrics`:**  Module containing various evaluation metrics (e.g., `Equals`, `ConversationalCoherenceMetric`).\n*   **`Prompt`:** Represents a prompt used for LLMs.\n\n**4. Notable Features or Patterns**\n\n*   **E2E Testing:** The code includes extensive end-to-end tests to verify the functionality of the SDK and its interaction with the backend.\n*   **Context Management:**  The `opik_context` module provides a way to access and update the current trace and span data within a tracked function.  This is useful for automatically logging information and feedback scores.\n*   **Decorators:** The `@opik.track` decorator simplifies the process of tracking function execution and creating traces and spans.\n*   **Fluent Interface:**  The `Trace` and `Span` objects provide a fluent interface for logging feedback scores and other data.\n*   **Guardrail Integration:** The SDK integrates with a guardrail system to validate LLM outputs and prevent violations of defined rules.\n*   **Thread Evaluation:** The SDK provides functionality to evaluate conversational threads, which is important for conversational AI applications.\n*   **Asynchronous Operations:** The `opik.flush_tracker()` function suggests that the SDK may use asynchronous operations to send data to the backend.\n*   **Idempotency:** The `test_deduplication` test suggests that the SDK handles duplicate dataset items gracefully.\n\n**5. Potential Use Cases**\n\n*   **LLM Evaluation and Benchmarking:**  Evaluating the performance of different LLMs on various tasks and datasets.\n*   **Conversational AI Development:**  Building and evaluating conversational AI applications, such as chatbots and virtual assistants.\n*   **Prompt Engineering:**  Optimizing prompts to improve LLM performance.\n*   **AI Safety and Compliance:**  Ensuring that LLM outputs are safe, compliant, and aligned with ethical guidelines.\n*   **Automated Feedback and Scoring:**  Automatically scoring LLM outputs based on predefined metrics.\n*   **Monitoring and Debugging:**  Monitoring the performance of LLMs in production and debugging issues.\n*   **A/B Testing:**  Comparing the performance of different LLM configurations or prompts.\n\nIn summary, `opik` is a comprehensive Python SDK designed to streamline the process of evaluating, optimizing, and monitoring LLMs and conversational AI applications within the Comet-ML ecosystem (or a similar platform). It provides a rich set of features for dataset management, experiment tracking, evaluation, feedback, guardrails, and thread evaluation.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "comet-ml/opik",
        "url": "https://github.com/comet-ml/opik",
        "file_size_mb": 3.06,
        "repomix_file": "repomixes/comet-ml_opik.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:46:04",
        "worker_id": 18072,
        "analysis_length": 6436
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided repository information. Here's a comprehensive summary of the `comet-ml/opik` repository based on the file structure and metadata:\n\n**1. Main Purpose and Functionality:**\n\n*   **LLM Observability and Evaluation:** Opik appears to be a comprehensive platform for observing, evaluating, and optimizing Large Language Model (LLM) based applications. It provides tools for tracking LLM interactions (traces, spans), evaluating the quality of LLM outputs, and optimizing prompts and models.\n*   **End-to-End LLM Workflow Support:** The repository covers various aspects of the LLM development lifecycle, from experimentation and prompt engineering to deployment and monitoring.\n*   **Integration with Popular LLM Frameworks:** Opik provides integrations with popular LLM frameworks like LangChain, LlamaIndex, DSPy, Haystack, and others. This allows developers to easily track and evaluate LLM applications built with these frameworks.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **Frontend:**\n    *   **TypeScript:** The primary language for the frontend.\n    *   **React:** Likely the UI framework used for building the web interface.\n    *   **Vite:**  A build tool for modern web development.\n    *   **Playwright:** Used for end-to-end (E2E) testing.\n*   **Backend:**\n    *   **Python:**  The primary language for the backend services and SDKs.\n    *   **FastAPI (Likely):** The `opik-guardrails-backend` and `opik-python-backend` directories suggest the use of FastAPI for building REST APIs.\n*   **SDKs:**\n    *   **Python SDK:**  Provides tools for instrumenting LLM applications and sending data to the Opik platform.\n    *   **Optimizer SDK:**  Provides tools for optimizing prompts and models.\n*   **Other:**\n    *   **LLM Frameworks:** Integrations with LangChain, LlamaIndex, DSPy, Haystack, OpenAI, Anthropic, Bedrock, Gemini, Groq, LiteLLM, Ragas, and others.\n    *   **Docker:** Used for containerizing the backend services.\n    *   **Helm:** Used for deploying Opik on Kubernetes.\n    *   **Guardrails AI:** Used for implementing guardrails and validating LLM outputs.\n\n**3. Architecture Overview:**\n\nBased on the directory structure, the architecture seems to be structured as follows:\n\n*   **Frontend (apps/opik-frontend):**  A web application that provides a user interface for interacting with the Opik platform.  It allows users to visualize traces, evaluate LLM outputs, manage datasets, and configure the system.\n*   **Guardrails Backend (apps/opik-guardrails-backend):** A Python-based backend service responsible for validating LLM outputs and enforcing guardrails.  It likely uses libraries like `spacy` to perform topic matching and PII detection.\n*   **Python Backend (apps/opik-python-backend):**  A Python-based backend service that likely handles core functionalities such as data processing, evaluation, and API endpoints.\n*   **Python SDK (sdks/python):**  A Python library that developers can use to instrument their LLM applications and send data to the Opik platform.  It provides decorators and other tools for automatically tracking LLM interactions.\n*   **Optimizer SDK (sdks/opik\\_optimizer):** A Python library that provides tools for optimizing prompts and models. It includes various optimization algorithms, such as evolutionary optimization, few-shot Bayesian optimization, and meta-prompt optimization.\n\n**4. Notable Features or Patterns:**\n\n*   **Traces and Spans:**  Opik uses the concept of traces and spans to track the execution of LLM applications.  Traces represent end-to-end requests, while spans represent individual operations within a trace.\n*   **Evaluation Metrics:**  Opik provides a wide range of evaluation metrics for assessing the quality of LLM outputs.  These metrics include heuristics (e.g., BLEU, ROUGE), LLM-based judges (e.g., answer relevance, factuality), and conversation-specific metrics (e.g., conversational coherence, user frustration).\n*   **Guardrails:**  Opik allows users to define guardrails to ensure that LLM outputs are safe, ethical, and aligned with business requirements.  Guardrails can be used to prevent the generation of PII, toxic content, or outputs that are off-topic.\n*   **Prompt Optimization:**  Opik provides tools for optimizing prompts to improve the performance of LLM applications.  This includes techniques such as evolutionary optimization and meta-prompt optimization.\n*   **Integrations:**  Opik integrates with a wide range of LLM frameworks, cloud providers, and other tools.  This makes it easy to integrate Opik into existing LLM workflows.\n*   **API-Driven:** The extensive use of `use*Mutation` and `use*List` hooks in the frontend suggests a strong reliance on a REST API for data fetching and manipulation.\n*   **Configuration via Code:** The presence of numerous `config.ts` and `tailwind.config.ts` files indicates that the frontend is highly configurable through code.\n*   **Testing:**  The presence of E2E tests in the frontend and unit tests in the backend suggests a strong emphasis on testing.\n\n**5. Potential Use Cases:**\n\n*   **LLM Application Development:**  Opik can be used to track the performance of LLM applications during development and identify areas for improvement.\n*   **Prompt Engineering:**  Opik can be used to optimize prompts to improve the accuracy, fluency, and relevance of LLM outputs.\n*   **Model Evaluation:**  Opik can be used to evaluate the performance of different LLM models and select the best model for a given task.\n*   **LLM Monitoring:**  Opik can be used to monitor the performance of LLM applications in production and detect anomalies.\n*   **AI Safety and Ethics:**  Opik can be used to enforce guardrails and ensure that LLM outputs are safe, ethical, and aligned with business requirements.\n*   **Building LLM-powered conversational AI applications:** The presence of conversation-related metrics and thread management features suggests that Opik can be used to build and evaluate conversational AI applications.\n*   **Experiment Tracking:** The presence of experiment tracking features suggests that Opik can be used to track the results of different experiments and identify the best configurations for LLM applications.\n\nIn summary, `comet-ml/opik` is a comprehensive platform for LLM observability, evaluation, and optimization. It provides a wide range of features and integrations to support the entire LLM development lifecycle.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "Nice-PLQ/devtools-remote-debugger",
        "url": "https://github.com/Nice-PLQ/devtools-remote-debugger",
        "file_size_mb": 3.07,
        "repomix_file": "repomixes/Nice-PLQ_devtools-remote-debugger.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:47:40",
        "worker_id": 17344,
        "analysis_length": 5939
      },
      "analysis": {
        "content": "Okay, I've analyzed the code from the \"Nice-PLQ/devtools-remote-debugger\" repository you provided. Here's a comprehensive summary:\n\n**1. Main Purpose and Functionality**\n\nThe code snippets appear to be related to the Accessibility panel within the Chrome DevTools frontend.  Specifically, they focus on:\n\n*   **Displaying an accessibility tree:**  The `AXBreadcrumbsPane` and related classes (`AXBreadcrumb`) are responsible for rendering a hierarchical representation of the accessibility tree for a selected DOM node. This allows developers to understand how assistive technologies like screen readers perceive the structure of the page.\n*   **ARIA attribute handling:** The `ARIAAttributePrompt` and `ARIAMetadata` classes deal with providing autocompletion and metadata for ARIA attributes, aiding developers in correctly implementing accessible web content.\n*   **Source Order Viewer:** The `SourceOrderPane` allows developers to visualize the source order of elements in the DOM, which is crucial for accessibility as it impacts how screen readers navigate the page.\n*   **AI Assistance:** The `AgentProject` and `AiAgent` classes seem to be related to integrating AI assistance into DevTools, potentially for tasks like code modification or analysis related to accessibility.\n\n**2. Key Technologies and Frameworks Used**\n\n*   **JavaScript/TypeScript:** The code is primarily written in TypeScript, a superset of JavaScript that adds static typing.\n*   **Chrome DevTools Frontend Architecture:** The code integrates deeply with the DevTools frontend, using its UI components, data structures (e.g., `SDK.DOMNode`, `SDK.Accessibility.AXNode`), and event handling mechanisms.\n*   **Legacy UI Framework:** The code uses `UI.legacy.legacy.js`, indicating that it relies on the older UI framework within DevTools.  Newer DevTools code often uses a component-based approach.\n*   **CSS Modules:** The `axBreadcrumbs.css.js` file suggests the use of CSS Modules for styling, which helps to encapsulate styles and avoid naming conflicts.\n*   **ARIA:** The code heavily interacts with ARIA (Accessible Rich Internet Applications) attributes and roles, which are essential for making web content accessible.\n*   **Diff:** The `AgentProject` class uses the `diff` library to calculate the number of lines changed when writing to a file.\n*   **Visual Logging:** The code uses `VisualLogging` to track user interactions with the accessibility tree and source order viewer.\n\n**3. Architecture Overview**\n\n*   **Accessibility Tree Representation:** The `AXBreadcrumbsPane` acts as the main container.  It fetches the accessibility tree from the backend (likely through the `SDK.Accessibility.AXNode` API) and creates a hierarchy of `AXBreadcrumb` objects to represent the tree visually.  The breadcrumbs are interactive, allowing users to navigate the tree and inspect individual nodes.\n*   **ARIA Metadata:** The `ARIAMetadata` class provides a central repository of information about ARIA attributes and roles.  It's likely used to provide autocompletion suggestions and validation within the DevTools UI.\n*   **Source Order Overlay:** The `SourceOrderPane` interacts with the `SDK.OverlayModel` to highlight the source order of elements directly in the browser viewport.\n*   **AI Agent Integration:** The `AgentProject` class provides an abstraction over the DevTools workspace, allowing AI agents to read, write, and search files. The `AiAgent` class provides a base class for implementing interactions with AIDA (AI DevTools Assistant).\n\n**4. Notable Features or Patterns**\n\n*   **Breadcrumb Navigation:** The use of breadcrumbs is a common UI pattern for navigating hierarchical data structures.  The `AXBreadcrumb` class handles the rendering and interaction logic for each breadcrumb.\n*   **Lazy Loading:** The accessibility tree is likely loaded lazily to improve performance, especially for complex pages.  The `children-unloaded` class and related logic in `AXBreadcrumb` suggest that child nodes are only fetched when a breadcrumb is expanded.\n*   **Overlay Highlighting:** The `SourceOrderPane` and `AXBreadcrumb` classes use the `SDK.OverlayModel` to highlight DOM nodes directly in the browser viewport, providing a visual representation of the accessibility tree and source order.\n*   **Experiment Flags:** The `AXBreadcrumbsPane` uses `Root.Runtime.experiments` to enable or disable the full accessibility tree, indicating that this feature is still under development.\n*   **Function Calling:** The `AiAgent` class supports function calling, allowing the AI model to invoke specific functions in DevTools to perform actions like modifying code or retrieving data.\n*   **Error Handling:** The `AiAgent` class includes error handling for AIDA API calls, including abort and block errors.\n\n**5. Potential Use Cases**\n\n*   **Accessibility Auditing:** Developers can use the Accessibility panel to identify and fix accessibility issues in their web pages.\n*   **ARIA Implementation:** The ARIA attribute autocompletion and metadata can help developers correctly implement ARIA attributes and roles.\n*   **Source Order Optimization:** Developers can use the Source Order Viewer to ensure that the source order of elements is logical and accessible to screen readers.\n*   **AI-Assisted Accessibility Improvements:** The AI assistance integration could be used to automatically identify and fix accessibility issues, generate ARIA attributes, or refactor code to improve accessibility.\n*   **Automated Code Modification:** The AI agent could be used to automatically modify code based on accessibility recommendations.\n\nIn summary, this code is a crucial part of the Chrome DevTools Accessibility panel, providing developers with tools to understand, debug, and improve the accessibility of their web pages. The integration of AI assistance has the potential to further automate and simplify the process of creating accessible web content.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "Nice-PLQ/devtools-remote-debugger",
        "url": "https://github.com/Nice-PLQ/devtools-remote-debugger",
        "file_size_mb": 3.06,
        "repomix_file": "repomixes/Nice-PLQ_devtools-remote-debugger.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:47:41",
        "worker_id": 26000,
        "analysis_length": 6127
      },
      "analysis": {
        "content": "Okay, I've analyzed the provided file summary and directory structure of the `Nice-PLQ/devtools-remote-debugger` repository. Here's a comprehensive summary:\n\n**1. Main Purpose and Functionality:**\n\nThe repository appears to be a remote debugger, heavily based on the Chrome DevTools frontend.  Its primary purpose is likely to provide a debugging interface for web applications running in remote environments (e.g., mobile devices, embedded systems, or other browsers). The inclusion of \"devtools-frontend\" in many file paths strongly suggests it's leveraging the existing DevTools UI and functionality. The \"remote\" aspect implies a mechanism for connecting to and controlling a debugging target that isn't running locally. The addition of AI assistance suggests that the tool is being augmented with AI-powered features to aid in debugging and development.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **JavaScript/TypeScript:** The vast majority of the code appears to be written in JavaScript and TypeScript, indicated by the `.js` and `.ts` file extensions.\n*   **Chrome DevTools Frontend:**  The directory structure `devtools-frontend` is a strong indicator that the project is built upon the open-source Chrome DevTools frontend.  This implies the use of related technologies and patterns common in that codebase.\n*   **Repomix:** Used to create the single file representation for AI analysis.\n*   **Likely a Protocol for Remote Debugging:**  The name \"remote-debugger\" and the presence of files like `InspectorBackend.js` and `protocol.js` suggest the use of a debugging protocol (likely based on the Chrome DevTools Protocol or a similar mechanism) for communication between the debugger UI and the remote target.\n*   **AI/ML:** The `panels/ai_assistance` directory indicates the use of AI to assist in debugging.\n\n**3. Architecture Overview:**\n\nBased on the directory structure, here's a likely architectural overview:\n\n*   **Frontend (devtools-frontend):**  This is the main UI, responsible for displaying debugging information, providing controls for interacting with the remote target, and handling user input.  It's likely structured as a modular application with components for different DevTools panels (e.g., Elements, Console, Network, Performance).\n*   **Core (devtools-frontend/core):** Contains fundamental utilities, data structures, and base classes used throughout the frontend.  This includes things like:\n    *   `common`:  Basic utilities, event handling, settings management.\n    *   `host`:  Abstraction layer for interacting with the host environment (e.g., the browser or a remote debugging server).\n    *   `i18n`: Internationalization support.\n    *   `platform`:  Platform-specific utilities and abstractions.\n    *   `protocol_client`:  Handles communication with the remote debugging target using the DevTools Protocol.\n    *   `sdk`:  Provides models and APIs for interacting with the debugging target (e.g., accessing DOM, CSS, JavaScript runtime).\n*   **Models (devtools-frontend/models):**  Represents the data and logic for various aspects of the debugging target (e.g., DOM, CSS, JavaScript, network requests, performance profiles).\n*   **Panels (devtools-frontend/panels):**  Implements the individual DevTools panels, providing specific debugging tools and visualizations (e.g., Elements panel, Console panel, Network panel).\n*   **Entrypoints:**  Defines the entry points for different parts of the application (e.g., the main DevTools application, worker threads).\n*   **AI Assistance (panels/ai_assistance):** This section contains the AI-powered features, including agents for various debugging tasks (e.g., file analysis, network analysis, performance analysis), a chat interface, and data formatters for presenting information to the user.\n\n**4. Notable Features or Patterns:**\n\n*   **Remote Debugging:** The core functionality is focused on debugging applications running in remote environments.\n*   **Chrome DevTools Integration:**  The project heavily leverages the existing Chrome DevTools frontend, providing a familiar debugging experience.\n*   **Modular Design:** The `devtools-frontend` directory is structured into modules (core, models, panels, etc.), suggesting a modular and maintainable codebase.\n*   **Asynchronous Communication:**  The use of a debugging protocol implies asynchronous communication between the debugger UI and the remote target.\n*   **Source Maps:**  The presence of `SourceMapManager` and related files indicates support for source maps, allowing debugging of minified or transpiled code.\n*   **Performance Analysis:**  Files related to CPU profiling, heap snapshots, and tracing suggest capabilities for performance analysis and optimization.\n*   **Issues Management:** The `models/issues_manager` directory indicates a system for detecting and reporting potential issues in the web application.\n*   **AI Assistance:** The `panels/ai_assistance` directory indicates the use of AI to assist in debugging.\n\n**5. Potential Use Cases:**\n\n*   **Debugging Mobile Web Applications:**  Debugging web applications running on mobile devices (Android, iOS) using a remote debugging connection.\n*   **Debugging Embedded Systems:**  Debugging web-based UIs running on embedded systems or IoT devices.\n*   **Cross-Browser Debugging:**  Debugging web applications running in different browsers (e.g., Firefox, Safari) by implementing a remote debugging adapter for each browser.\n*   **Debugging WebViews:** Debugging web content within native applications using WebViews.\n*   **Extending DevTools:**  Adding custom debugging features or panels to the existing Chrome DevTools frontend.\n*   **Automated Testing:**  Using the remote debugging interface to automate testing of web applications.\n*   **AI-Assisted Debugging:** Using AI to automatically identify and fix bugs in web applications.\n\nIn summary, this repository appears to be a sophisticated remote debugger built upon the Chrome DevTools frontend, with added AI assistance. It provides a powerful and familiar debugging experience for web applications running in various remote environments.",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "bitsauce/Carla-ppo",
        "url": "https://github.com/bitsauce/Carla-ppo",
        "file_size_mb": 0.23,
        "repomix_file": "repomixes/bitsauce_Carla-ppo.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:48:34",
        "worker_id": 18072,
        "analysis_length": 5177
      },
      "analysis": {
        "content": "Okay, let's break down the `bitsauce/Carla-ppo` repository based on the provided information.\n\n**1. Main Purpose and Functionality:**\n\nThe repository appears to be focused on implementing Proximal Policy Optimization (PPO) for autonomous navigation in the CARLA simulator.  The code aims to train an agent to drive a vehicle in a simulated environment, likely to complete laps or follow routes. The inclusion of a Variational Autoencoder (VAE) suggests that the system might be using a compressed or latent representation of the environment as input to the PPO agent.\n\n**2. Key Technologies and Frameworks Used:**\n\n*   **CARLA Simulator:** This is the core environment.  CARLA provides a realistic urban driving simulation platform.\n*   **Python:** The primary programming language.\n*   **PPO (Proximal Policy Optimization):**  A reinforcement learning algorithm used for training the autonomous driving agent. The `ppo.py` file is likely the core implementation of this algorithm.\n*   **Variational Autoencoder (VAE):**  Used for dimensionality reduction or feature extraction from the CARLA environment's visual input.  The `vae/` directory contains related code.\n*   **NumPy:** Used for numerical computations.\n*   **NetworkX:** Used for graph-based path planning.\n\n**3. Architecture Overview:**\n\nThe architecture seems to be structured as follows:\n\n*   **CARLA Environment (`CarlaEnv/`):**  This directory contains the code that interfaces with the CARLA simulator. It includes:\n    *   `carla_lap_env.py` and `carla_route_env.py`:  Likely define specific CARLA environments for lap completion and route following, respectively.\n    *   `wrappers.py`: Probably contains environment wrappers to modify the CARLA environment (e.g., for observation space or reward shaping).\n    *   `agents/navigation/`: Contains implementations of basic agents for navigation, including path planning and low-level control.\n    *   `planner.py`: Implements a high-level route planner.\n    *   `hud.py`: Implements a Heads-Up Display for visualizing information in the simulation.\n*   **PPO Implementation (`ppo.py`):**  This file contains the core PPO algorithm, which interacts with the CARLA environment to train the agent.\n*   **VAE (`vae/`):**  This directory contains the VAE implementation:\n    *   `models.py`: Defines the VAE's neural network architecture.\n    *   `train_vae.py`: Contains the training script for the VAE.\n    *   `inspect_vae.py`: Used for debugging and visualizing the VAE.\n*   **Reward Functions (`reward_functions.py`):** Defines the reward function used to train the PPO agent.\n*   **Training and Evaluation (`train.py`, `run_eval.py`):**  `train.py` likely contains the main training loop for the PPO agent. `run_eval.py` is used to evaluate the trained agent's performance.\n*   **Utilities (`utils.py`):** Contains helper functions.\n\nThe general flow is likely:\n\n1.  The VAE (if used) is trained to create a compressed representation of the CARLA environment.\n2.  The PPO agent interacts with the CARLA environment (potentially using the VAE's output as input).\n3.  The agent receives rewards based on its actions.\n4.  The PPO algorithm updates the agent's policy to maximize future rewards.\n5.  The trained agent is evaluated on its ability to complete laps or follow routes.\n\n**4. Notable Features or Patterns:**\n\n*   **Modular Design:** The code is organized into logical directories and files, making it easier to understand and maintain.\n*   **Use of Wrappers:** Environment wrappers are used to customize the CARLA environment for reinforcement learning.\n*   **PID Controllers:** PID controllers are used for low-level vehicle control (steering, throttle, braking).\n*   **Global Route Planner:** A global route planner is used to generate high-level routes for the agent to follow.\n*   **VAE for Representation Learning:** The use of a VAE suggests an attempt to learn a more efficient representation of the environment, which can improve the agent's learning speed and performance.\n*   **Traffic Light and Vehicle Avoidance:** The agent is designed to respond to traffic lights and avoid other vehicles.\n\n**5. Potential Use Cases:**\n\n*   **Autonomous Driving Research:**  The repository can be used as a starting point for research on autonomous driving algorithms.\n*   **Reinforcement Learning in Simulated Environments:**  The code provides an example of how to train reinforcement learning agents in a complex simulated environment.\n*   **Development of Autonomous Driving Systems:**  The repository can be used to develop and test autonomous driving systems before deploying them in the real world.\n*   **Training Data Generation:** The simulator can be used to generate training data for other machine learning models.\n*   **Benchmarking:** The repository can be used to benchmark different reinforcement learning algorithms for autonomous driving.\n\nIn summary, this repository provides a framework for training autonomous driving agents using PPO in the CARLA simulator, potentially leveraging a VAE for environment representation. It's a valuable resource for researchers and developers working on autonomous driving and reinforcement learning.",
        "format": "structured_text"
      }
    }
  ]
}
</file>

<file path="successful_analysis_example.json">
{
  "metadata": {
    "generated": "2025-07-25 04:45:01",
    "output_directory": "20250725_044501_511",
    "total_repositories": 20,
    "processing_configuration": {
      "repomix_workers": 15,
      "llm_workers": 4,
      "rate_limit_mb_per_min": 12
    }
  },
  "analyses": [
    {
      "repository": {
        "name": "zszszszsz/.config",
        "url": "https://github.com/zszszszsz/.config",
        "file_size_mb": 0.0,
        "repomix_file": "repomixes/zszszszsz_.config.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:18",
        "worker_id": 18072,
        "analysis_length": 3509
      },
      "analysis": {
        "content": "**1. Main Purpose and Functionality:**\n\nThe repository, `zszszszsz/.config`, is a template repository designed to build OpenWrt firmware using GitHub Actions. It leverages GitHub's CI/CD capabilities to automate the process of compiling OpenWrt based on a user-provided `.config` file.\n\n**2. Key Technologies and Frameworks Used:**\n\n* **GitHub Actions:** The primary technology for CI/CD automation\n* **OpenWrt:** The embedded operating system being built\n* **Lean's OpenWrt (coolsnowwolf/lede):** Used as the source code base\n* **tmate, mxschmitt/action-tmate:** Used for debugging purposes\n* **Cowtransfer, WeTransfer:** Used for transferring built firmware binaries\n\n**3. Architecture Overview:**\n\n1. User creates repository from template and pushes `.config` file\n2. GitHub Actions trigger builds the workflow\n3. Build process uses `.config` file to build OpenWrt\n4. Built firmware binaries are packaged as artifacts\n5. User downloads artifacts from GitHub Actions page\n\n**4. Notable Features:**\n\n* Template Repository design for easy reuse\n* Configuration-Driven Build process\n* Automated Build Process with GitHub Actions\n* Artifact Delivery system\n* Debugging Tools integration\n\n**5. Potential Use Cases:**\n\n* Custom OpenWrt Firmware creation\n* Automated Firmware Builds\n* OpenWrt Development\n* Creating OpenWrt Distributions\n* Reproducible Builds",
        "format": "structured_text"
      }
    },
    {
      "repository": {
        "name": "e2b-dev/awesome-ai-sdks",
        "url": "https://github.com/e2b-dev/awesome-ai-sdks",
        "file_size_mb": 0.01,
        "repomix_file": "repomixes/e2b-dev_awesome-ai-sdks.md"
      },
      "processing": {
        "processed_at": "2025-07-25 04:45:19",
        "worker_id": 17344,
        "analysis_length": 3331
      },
      "analysis": {
        "content": "**1. Main Purpose and Functionality:**\n\nThe repository \"e2b-dev/awesome-ai-sdks\" is a curated list of SDKs, frameworks, libraries, and tools specifically designed for creating, monitoring, debugging, and deploying autonomous AI agents.\n\n**2. Key Technologies and Frameworks Listed:**\n\n* **LangChain:** Framework for building applications using large language models\n* **Vercel AI SDK:** Library for building AI-powered user interfaces\n* **Chidori:** Reactive runtime for building AI agents\n* **Steamship:** Platform for building, scaling, and monitoring AI agents\n* **Helicone:** Observability platform for GPT-3\n* **AgentOps:** Tools for agent monitoring and analytics\n* **Langfuse:** Open-source analytics for LLM apps\n* **LangSmith:** Platform for debugging, testing, evaluating LLM applications\n* **E2B:** Operating system for AI agents\n\n**3. Architecture Overview:**\n\nSimple repository architecture consisting of a single `README.md` file that acts as a structured directory with links and descriptions of various AI agent SDKs and tools.\n\n**4. Notable Features:**\n\n* Curated List of AI agent tools\n* Categorization by tool type\n* Links and Descriptions for each tool\n* Community Focus with contribution encouragement\n* E2B Promotion of their own offerings\n\n**5. Potential Use Cases:**\n\n* Discovery of AI Agent Tools\n* Tool Selection and comparison\n* Staying Up-to-Date with AI agent tooling\n* Community Engagement and knowledge sharing\n* Benchmarking different AI agent SDKs",
        "format": "structured_text"
      }
    }
  ]
}
</file>

<file path="src/__init__.py">

</file>

<file path="src/data_fetcher.py">
#!/usr/bin/env python3
"""
Data fetcher for Repository Research Tool.
Auto-fetches analysis files from cloud storage to local output directory.
"""

import os
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class DataFetcher:
    """
    Fetches analysis files from cloud storage to local directory.
    Provides easy access to analysis results for review and processing.
    """
    
    def __init__(self, config=None, storage_manager=None, supabase_client=None):
        """
        Initialize data fetcher.
        
        Args:
            config: ScraperConfig instance
            storage_manager: StorageManager instance
            supabase_client: Supabase client instance
        """
        self.config = config
        self.storage_manager = storage_manager
        self.supabase_client = supabase_client
        
        # Local output directory
        self.local_output_dir = Path("output")
        self.local_output_dir.mkdir(exist_ok=True)
        
        # Downloaded files tracking
        self.downloads_dir = self.local_output_dir / "downloads"
        self.downloads_dir.mkdir(exist_ok=True)
        
        logger.info(f"Data fetcher initialized, output directory: {self.local_output_dir}")
    
    def fetch_job_analysis(self, job_id: str, force_refresh: bool = False) -> Optional[Path]:
        """
        Fetch analysis file for a specific job.
        
        Args:
            job_id: Job ID to fetch analysis for
            force_refresh: Force re-download even if file exists locally
            
        Returns:
            Path to local analysis file, or None if not found
        """
        try:
            # Create job-specific directory
            job_dir = self.downloads_dir / job_id
            job_dir.mkdir(exist_ok=True)
            
            # Check if already downloaded
            local_analysis_file = job_dir / "analysis.json"
            if local_analysis_file.exists() and not force_refresh:
                logger.info(f"Analysis file already exists locally: {local_analysis_file}")
                return local_analysis_file
            
            # Get job information from database
            if not self.supabase_client:
                logger.error("Supabase client not available for fetching job data")
                return None
            
            job_record = self.supabase_client.get_job(job_id)
            if not job_record:
                logger.error(f"Job {job_id} not found in database")
                return None
            
            # Check if job has analysis file URL
            analysis_file_url = getattr(job_record, 'analysis_file_url', None)
            if not analysis_file_url:
                logger.warning(f"Job {job_id} has no analysis file URL")
                return None
            
            # Download analysis file from storage
            if not self.storage_manager:
                logger.error("Storage manager not available for downloading files")
                return None
            
            logger.info(f"Downloading analysis file for job {job_id}...")
            analysis_content = self.storage_manager.download_analysis_file(analysis_file_url)
            
            if not analysis_content:
                logger.error(f"Failed to download analysis file for job {job_id}")
                return None
            
            # Save to local file
            with open(local_analysis_file, 'w', encoding='utf-8') as f:
                if isinstance(analysis_content, str):
                    f.write(analysis_content)
                else:
                    json.dump(analysis_content, f, indent=2)
            
            # Create metadata file
            metadata = {
                "job_id": job_id,
                "downloaded_at": datetime.now().isoformat(),
                "source_url": analysis_file_url,
                "file_size": local_analysis_file.stat().st_size,
                "job_status": getattr(job_record, 'status', 'unknown'),
                "created_at": getattr(job_record, 'created_at', None)
            }
            
            metadata_file = job_dir / "metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"✅ Analysis file downloaded: {local_analysis_file}")
            return local_analysis_file
            
        except Exception as e:
            logger.error(f"Error fetching analysis for job {job_id}: {e}")
            return None
    
    def fetch_recent_analyses(self, limit: int = 10, days: int = 7) -> List[Path]:
        """
        Fetch recent analysis files.
        
        Args:
            limit: Maximum number of analyses to fetch
            days: Number of days to look back
            
        Returns:
            List of paths to downloaded analysis files
        """
        try:
            if not self.supabase_client:
                logger.error("Supabase client not available")
                return []
            
            # Get recent completed jobs
            recent_jobs = self.supabase_client.get_recent_jobs(limit=limit, days=days)
            
            downloaded_files = []
            for job in recent_jobs:
                job_id = getattr(job, 'id', None)
                if job_id:
                    analysis_file = self.fetch_job_analysis(job_id)
                    if analysis_file:
                        downloaded_files.append(analysis_file)
            
            logger.info(f"✅ Downloaded {len(downloaded_files)} recent analysis files")
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Error fetching recent analyses: {e}")
            return []
    
    def fetch_all_analyses(self) -> List[Path]:
        """
        Fetch all available analysis files.
        
        Returns:
            List of paths to downloaded analysis files
        """
        try:
            if not self.supabase_client:
                logger.error("Supabase client not available")
                return []
            
            # Get all completed jobs
            all_jobs = self.supabase_client.get_all_completed_jobs()
            
            downloaded_files = []
            for job in all_jobs:
                job_id = getattr(job, 'id', None)
                if job_id:
                    analysis_file = self.fetch_job_analysis(job_id)
                    if analysis_file:
                        downloaded_files.append(analysis_file)
            
            logger.info(f"✅ Downloaded {len(downloaded_files)} analysis files")
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Error fetching all analyses: {e}")
            return []
    
    def list_local_analyses(self) -> List[Dict]:
        """
        List all locally downloaded analysis files.
        
        Returns:
            List of dictionaries with analysis file information
        """
        analyses = []
        
        try:
            for job_dir in self.downloads_dir.iterdir():
                if not job_dir.is_dir():
                    continue
                
                analysis_file = job_dir / "analysis.json"
                metadata_file = job_dir / "metadata.json"
                
                if analysis_file.exists():
                    # Load metadata if available
                    metadata = {}
                    if metadata_file.exists():
                        try:
                            with open(metadata_file) as f:
                                metadata = json.load(f)
                        except:
                            pass
                    
                    # Get file stats
                    stat = analysis_file.stat()
                    
                    analyses.append({
                        "job_id": job_dir.name,
                        "analysis_file": str(analysis_file),
                        "file_size": stat.st_size,
                        "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "metadata": metadata
                    })
            
            # Sort by modification time (newest first)
            analyses.sort(key=lambda x: x["modified_at"], reverse=True)
            
            return analyses
            
        except Exception as e:
            logger.error(f"Error listing local analyses: {e}")
            return []
    
    def cleanup_old_downloads(self, days: int = 30):
        """
        Clean up old downloaded files.
        
        Args:
            days: Remove files older than this many days
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
            removed_count = 0
            
            for job_dir in self.downloads_dir.iterdir():
                if not job_dir.is_dir():
                    continue
                
                # Check if directory is old
                if job_dir.stat().st_mtime < cutoff_time:
                    shutil.rmtree(job_dir)
                    removed_count += 1
                    logger.debug(f"Removed old download directory: {job_dir}")
            
            if removed_count > 0:
                logger.info(f"✅ Cleaned up {removed_count} old download directories")
            
        except Exception as e:
            logger.error(f"Error cleaning up old downloads: {e}")
    
    def create_analysis_index(self) -> Path:
        """
        Create an index file of all local analyses.
        
        Returns:
            Path to the index file
        """
        try:
            analyses = self.list_local_analyses()
            
            index = {
                "generated_at": datetime.now().isoformat(),
                "total_analyses": len(analyses),
                "analyses": analyses
            }
            
            index_file = self.downloads_dir / "index.json"
            with open(index_file, 'w') as f:
                json.dump(index, f, indent=2)
            
            logger.info(f"✅ Created analysis index: {index_file}")
            return index_file
            
        except Exception as e:
            logger.error(f"Error creating analysis index: {e}")
            return None
</file>

<file path="src/monitoring.py">
"""
Comprehensive monitoring and logging system for repository research tool.
Integrates with Sentry and creates detailed logs for analysis.
"""
import os
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration


class RepositoryMonitor:
    """Comprehensive monitoring for repository processing with Sentry integration."""
    
    def __init__(self, output_dir: Path):
        self.output_dir = Path(output_dir)
        self.logs_dir = self.output_dir / "logs"
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize log files
        self.run_log_file = self.logs_dir / "run_analysis.json"
        self.errors_log_file = self.logs_dir / "errors.json"
        self.metrics_log_file = self.logs_dir / "metrics.json"
        self.sentry_log_file = self.logs_dir / "sentry_events.json"
        
        # Initialize data structures
        self.run_data = {
            "start_time": datetime.now().isoformat(),
            "configuration": {},
            "search_results": {},
            "processing_stats": {
                "repositories_found": 0,
                "repositories_attempted": 0,
                "repositories_successful": 0,
                "repositories_failed": 0,
                "repomix_failures": [],
                "llm_failures": [],
                "rate_limit_events": []
            },
            "performance_metrics": {},
            "end_time": None,
            "status": "running"
        }
        
        self.errors = []
        self.metrics = []
        self.sentry_events = []
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup comprehensive logging with Sentry integration."""
        # Configure Python logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.logs_dir / "detailed.log"),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("repository_monitor")
        
        # Add custom Sentry breadcrumb handler
        def custom_breadcrumb_processor(crumb, hint):
            """Process and log all Sentry breadcrumbs."""
            self.sentry_events.append({
                "timestamp": datetime.now().isoformat(),
                "type": "breadcrumb",
                "data": crumb,
                "hint": str(hint) if hint else None
            })
            return crumb
            
        # Configure Sentry with enhanced logging
        sentry_dsn = os.getenv('SENTRY_DSN')
        if sentry_dsn and sentry_dsn != 'your_sentry_dsn_here':
            sentry_sdk.init(
                dsn=os.getenv('SENTRY_DSN'),
                traces_sample_rate=1.0,
                profiles_sample_rate=1.0,
                integrations=[
                    LoggingIntegration(
                        level=logging.INFO,
                        event_level=logging.ERROR,
                        sentry_logs_level=logging.INFO
                    )
                ],
                before_breadcrumb=custom_breadcrumb_processor,
                _experiments={"enable_logs": True},
                environment=os.getenv('ENVIRONMENT', 'development'),
                release=os.getenv('RELEASE', 'repository-research-tool@1.0.0'),
            )
            
    def log_configuration(self, config: Dict[str, Any]):
        """Log the run configuration."""
        self.run_data["configuration"] = config
        self.logger.info(f"Configuration logged: {config}")
        sentry_sdk.add_breadcrumb(
            category="configuration",
            message="Run configuration set",
            level="info",
            data=config
        )
        
    def log_search_results(self, keyword: str, found_count: int, requested_count: int):
        """Log GitHub search results."""
        self.run_data["search_results"][keyword] = {
            "found": found_count,
            "requested": requested_count,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"Search results - {keyword}: {found_count}/{requested_count}")
        sentry_sdk.add_breadcrumb(
            category="search",
            message=f"GitHub search completed for '{keyword}'",
            level="info",
            data={"keyword": keyword, "found": found_count, "requested": requested_count}
        )

    def log_duplicate_detection(self, duplicate_info: Dict[str, Any]):
        """Log duplicate detection results."""
        self.run_data["duplicate_detection"] = duplicate_info

        self.logger.info(f"Duplicate detection: {duplicate_info['duplicates_removed']} duplicates removed "
                        f"({duplicate_info['duplicate_rate']:.1f}% duplicate rate)")

        sentry_sdk.add_breadcrumb(
            category="deduplication",
            message=f"Removed {duplicate_info['duplicates_removed']} duplicates "
                   f"({duplicate_info['duplicate_rate']:.1f}% rate)",
            level="info",
            data=duplicate_info
        )

        # Log multi-keyword repositories if any
        if duplicate_info.get('multi_keyword_repos'):
            multi_keyword_count = len(duplicate_info['multi_keyword_repos'])
            self.logger.info(f"Found {multi_keyword_count} repositories matching multiple keywords")

            sentry_sdk.add_breadcrumb(
                category="deduplication",
                message=f"{multi_keyword_count} repositories found by multiple keywords",
                level="info",
                data={"multi_keyword_repos": duplicate_info['multi_keyword_repos']}
            )

    def log_repository_start(self, repo_name: str, worker_id: int, worker_type: str):
        """Log when repository processing starts."""
        self.run_data["processing_stats"]["repositories_attempted"] += 1
        
        self.logger.info(f"{worker_type} Worker {worker_id}: Starting {repo_name}")
        sentry_sdk.add_breadcrumb(
            category="processing",
            message=f"Repository processing started: {repo_name}",
            level="info",
            data={"repo_name": repo_name, "worker_id": worker_id, "worker_type": worker_type}
        )
        
    def log_repository_success(self, repo_name: str, worker_id: int, worker_type: str, 
                             duration: float, file_size_mb: float = None):
        """Log successful repository processing."""
        self.run_data["processing_stats"]["repositories_successful"] += 1
        
        log_data = {
            "repo_name": repo_name,
            "worker_id": worker_id,
            "worker_type": worker_type,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        if file_size_mb:
            log_data["file_size_mb"] = file_size_mb
            
        self.metrics.append(log_data)
        
        self.logger.info(f"{worker_type} Worker {worker_id}: ✅ Completed {repo_name} - Duration: {duration:.1f}s")
        sentry_sdk.add_breadcrumb(
            category="success",
            message=f"Repository processed successfully: {repo_name}",
            level="info",
            data=log_data
        )
        
    def log_repository_failure(self, repo_name: str, worker_id: int, worker_type: str,
                             error_message: str, duration: float):
        """Log repository processing failure."""
        self.run_data["processing_stats"]["repositories_failed"] += 1
        
        error_data = {
            "repo_name": repo_name,
            "worker_id": worker_id,
            "worker_type": worker_type,
            "error_message": error_message,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        if worker_type == "repomix":
            self.run_data["processing_stats"]["repomix_failures"].append(error_data)
        else:
            self.run_data["processing_stats"]["llm_failures"].append(error_data)
            
        self.errors.append(error_data)
        
        self.logger.error(f"{worker_type} Worker {worker_id}: ❌ Failed {repo_name} - {error_message}")
        sentry_sdk.add_breadcrumb(
            category="error",
            message=f"Repository processing failed: {repo_name}",
            level="error",
            data=error_data
        )
        
        # Send to Sentry as an event for tracking
        sentry_sdk.capture_message(
            f"Repository processing failed: {repo_name}",
            level="error",
            extras=error_data
        )
        
    def log_rate_limit_event(self, worker_id: int, repo_name: str, retry_delay: int, attempt: int):
        """Log rate limiting events."""
        rate_limit_data = {
            "worker_id": worker_id,
            "repo_name": repo_name,
            "retry_delay": retry_delay,
            "attempt": attempt,
            "timestamp": datetime.now().isoformat()
        }
        
        self.run_data["processing_stats"]["rate_limit_events"].append(rate_limit_data)
        
        self.logger.warning(f"Rate limited - Worker {worker_id}, Repo: {repo_name}, Delay: {retry_delay}s, Attempt: {attempt}")
        sentry_sdk.add_breadcrumb(
            category="rate_limit",
            message=f"Rate limit encountered",
            level="warning",
            data=rate_limit_data
        )
        
    def log_performance_metrics(self, phase: str, duration: float, count: int, success_count: int = None):
        """Log performance metrics for different phases."""
        metrics_data = {
            "phase": phase,
            "duration": duration,
            "total_count": count,
            "success_count": success_count or count,
            "success_rate": (success_count or count) / count if count > 0 else 0,
            "timestamp": datetime.now().isoformat()
        }
        
        self.run_data["performance_metrics"][phase] = metrics_data
        
        self.logger.info(f"Performance - {phase}: {duration:.2f}s, Count: {count}, Success: {success_count or count}")
        sentry_sdk.add_breadcrumb(
            category="performance",
            message=f"Phase completed: {phase}",
            level="info",
            data=metrics_data
        )
        
    def finalize_run(self, status: str = "completed"):
        """Finalize the run and save all logs."""
        self.run_data["end_time"] = datetime.now().isoformat()
        self.run_data["status"] = status

        # Calculate final statistics
        total_found = sum(result["found"] for result in self.run_data["search_results"].values())
        self.run_data["processing_stats"]["repositories_found"] = total_found

        # Save all log files
        self._save_logs()

        # Send final summary to Sentry
        summary = {
            "total_repositories_found": total_found,
            "repositories_successful": self.run_data["processing_stats"]["repositories_successful"],
            "repositories_failed": self.run_data["processing_stats"]["repositories_failed"],
            "success_rate": self.run_data["processing_stats"]["repositories_successful"] / max(1, self.run_data["processing_stats"]["repositories_attempted"]),
            "total_duration": self._calculate_total_duration()
        }

        self.logger.info(f"Run completed: {summary}")
        sentry_sdk.capture_message(
            f"Repository research run completed",
            level="info",
            extras=summary
        )

        # Auto-fetch and analyze Sentry data
        self._auto_analyze_sentry_data()
        
    def _calculate_total_duration(self) -> float:
        """Calculate total run duration."""
        if self.run_data["end_time"]:
            start = datetime.fromisoformat(self.run_data["start_time"])
            end = datetime.fromisoformat(self.run_data["end_time"])
            return (end - start).total_seconds()
        return 0
        
    def _save_logs(self):
        """Save all logs to files."""
        # Save run analysis
        with open(self.run_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.run_data, f, indent=2, ensure_ascii=False, default=str)

        # Save errors
        with open(self.errors_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.errors, f, indent=2, ensure_ascii=False, default=str)

        # Save metrics
        with open(self.metrics_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False, default=str)

        # Save Sentry events
        with open(self.sentry_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.sentry_events, f, indent=2, ensure_ascii=False, default=str)
            
        print(f"\n📊 LOGS SAVED TO: {self.logs_dir}")
        print(f"  - Run analysis: {self.run_log_file}")
        print(f"  - Errors: {self.errors_log_file}")
        print(f"  - Metrics: {self.metrics_log_file}")
        print(f"  - Sentry events: {self.sentry_log_file}")

    def _auto_analyze_sentry_data(self):
        """Auto-fetch Sentry data and perform comprehensive analysis."""
        try:
            print(f"\n🔍 AUTO-ANALYZING SENTRY DATA...")

            # Fetch recent Sentry issues
            sentry_issues = self._fetch_recent_sentry_issues()

            # Comprehensive analysis
            analysis_data = {
                "timestamp": datetime.now().isoformat(),
                "run_summary": self.run_data,
                "sentry_analysis": sentry_issues,
                "error_analysis": self._analyze_local_errors(),
                "performance_analysis": self._analyze_performance(),
                "critical_issues": self._identify_critical_issues(sentry_issues),
                "recommendations": self._generate_enhanced_recommendations(sentry_issues)
            }

            # Save comprehensive analysis
            sentry_analysis_file = self.logs_dir / "auto_analysis.json"
            with open(sentry_analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)

            # Generate human-readable analysis report
            analysis_report_file = self.logs_dir / "auto_analysis_report.md"
            with open(analysis_report_file, 'w', encoding='utf-8') as f:
                f.write(self._generate_enhanced_analysis_report(analysis_data))

            # Generate Sentry-specific insights
            sentry_insights_file = self.logs_dir / "sentry_insights.md"
            with open(sentry_insights_file, 'w', encoding='utf-8') as f:
                f.write(self._generate_sentry_insights_report(sentry_issues))

            print(f"✅ Enhanced auto-analysis completed:")
            print(f"  - Analysis data: {sentry_analysis_file}")
            print(f"  - Analysis report: {analysis_report_file}")
            print(f"  - Sentry insights: {sentry_insights_file}")

        except Exception as e:
            print(f"⚠️ Auto-analysis failed: {e}")
            # Save the error for debugging
            error_file = self.logs_dir / "auto_analysis_error.txt"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"Auto-analysis error at {datetime.now().isoformat()}:\n")
                f.write(f"{str(e)}\n")
                f.write(f"\nTraceback:\n")
                import traceback
                f.write(traceback.format_exc())

    def _analyze_local_errors(self) -> Dict[str, Any]:
        """Analyze errors from local logs."""
        error_analysis = {
            "total_errors": len(self.errors),
            "repomix_failures": len(self.run_data["processing_stats"]["repomix_failures"]),
            "llm_failures": len(self.run_data["processing_stats"]["llm_failures"]),
            "rate_limit_events": len(self.run_data["processing_stats"]["rate_limit_events"]),
            "error_patterns": {},
            "failure_reasons": {}
        }

        # Analyze repomix failures
        for failure in self.run_data["processing_stats"]["repomix_failures"]:
            error_msg = failure.get("error_message", "unknown")
            if "clone repository" in error_msg:
                error_analysis["failure_reasons"]["git_clone_failures"] = error_analysis["failure_reasons"].get("git_clone_failures", 0) + 1
            elif "Invalid remote repository" in error_msg:
                error_analysis["failure_reasons"]["invalid_repo_urls"] = error_analysis["failure_reasons"].get("invalid_repo_urls", 0) + 1
            else:
                error_analysis["failure_reasons"]["other_repomix"] = error_analysis["failure_reasons"].get("other_repomix", 0) + 1

        return error_analysis

    def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze performance metrics."""
        stats = self.run_data["processing_stats"]
        total_attempted = stats["repositories_attempted"]
        total_successful = stats["repositories_successful"]
        total_found = stats["repositories_found"]

        # INTEGRITY FIX: Handle multiprocessing synchronization issues
        # If worker process stats weren't synchronized, use performance metrics as fallback
        if total_attempted == 0 and "total_processing" in self.run_data.get("performance_metrics", {}):
            perf_metrics = self.run_data["performance_metrics"]["total_processing"]
            total_attempted = perf_metrics.get("total_count", total_found)
            total_successful = perf_metrics.get("success_count", 0)

            # Log the synchronization issue
            self.logger.warning(f"INTEGRITY WARNING: Worker process stats not synchronized. Using fallback metrics.")
            self.logger.warning(f"  - repositories_attempted: {stats['repositories_attempted']} -> {total_attempted}")
            self.logger.warning(f"  - repositories_successful: {stats['repositories_successful']} -> {total_successful}")

        performance = {
            "success_rate": (total_successful / max(1, total_attempted)) * 100,
            "total_repositories": total_found,
            "processing_efficiency": (total_successful / max(1, total_found)) * 100,
            "rate_limit_frequency": len(stats["rate_limit_events"]) / max(1, total_attempted),
            "average_processing_time": self._calculate_average_processing_time(),
            "integrity_warning": total_attempted == 0 and total_found > 0
        }

        return performance

    def _calculate_average_processing_time(self) -> float:
        """Calculate average processing time from metrics."""
        if not self.metrics:
            return 0.0

        total_time = sum(metric.get("duration", 0) for metric in self.metrics)
        return total_time / len(self.metrics)

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on run data."""
        recommendations = []
        stats = self.run_data["processing_stats"]

        # INTEGRITY FIX: Use performance analysis for accurate success rate
        performance = self._analyze_performance()
        success_rate = performance["success_rate"]

        if performance.get("integrity_warning", False):
            recommendations.append("⚠️ MONITORING INTEGRITY WARNING: Worker process synchronization issues detected. Metrics may be inaccurate.")

        if success_rate < 50:
            recommendations.append("🚨 LOW SUCCESS RATE: Less than 50% of repositories processed successfully. Investigate repomix failures and API issues.")
        elif success_rate < 80:
            recommendations.append("⚠️ MODERATE SUCCESS RATE: Consider optimizing error handling and retry logic.")
        else:
            recommendations.append("✅ GOOD SUCCESS RATE: System performing well with high success rate.")

        # Rate limiting analysis
        rate_limit_count = len(stats["rate_limit_events"])
        if rate_limit_count > 100:
            recommendations.append("🚨 EXCESSIVE RATE LIMITING: Consider reducing concurrent workers or increasing delays.")
        elif rate_limit_count > 20:
            recommendations.append("⚠️ MODERATE RATE LIMITING: Monitor API usage patterns.")

        # Repository failures analysis
        repomix_failures = len(stats["repomix_failures"])
        if repomix_failures > 20:
            recommendations.append("🔧 HIGH REPOMIX FAILURES: Many repositories failed to process. Check repository accessibility and network connectivity.")

        return recommendations

    def _generate_analysis_report(self, analysis_data: Dict[str, Any]) -> str:
        """Generate human-readable analysis report."""
        report = []
        report.append("# Repository Research Tool - Auto Analysis Report")
        report.append(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Run Summary
        run_data = analysis_data["run_summary"]
        stats = run_data["processing_stats"]

        report.append("## Run Summary")
        report.append(f"- **Status**: {run_data['status']}")
        report.append(f"- **Duration**: {self._format_duration(self._calculate_total_duration())}")
        report.append(f"- **Repositories Found**: {stats['repositories_found']}")
        report.append(f"- **Repositories Attempted**: {stats['repositories_attempted']}")
        report.append(f"- **Repositories Successful**: {stats['repositories_successful']}")
        report.append(f"- **Success Rate**: {analysis_data['performance_analysis']['success_rate']:.1f}%")
        report.append("")

        # Error Analysis
        error_analysis = analysis_data["error_analysis"]
        report.append("## Error Analysis")
        report.append(f"- **Total Errors**: {error_analysis['total_errors']}")
        report.append(f"- **Repomix Failures**: {error_analysis['repomix_failures']}")
        report.append(f"- **LLM Failures**: {error_analysis['llm_failures']}")
        report.append(f"- **Rate Limit Events**: {error_analysis['rate_limit_events']}")

        if error_analysis["failure_reasons"]:
            report.append("\n### Failure Breakdown")
            for reason, count in error_analysis["failure_reasons"].items():
                report.append(f"- **{reason.replace('_', ' ').title()}**: {count}")
        report.append("")

        # Performance Analysis
        perf = analysis_data["performance_analysis"]
        report.append("## Performance Analysis")
        report.append(f"- **Processing Efficiency**: {perf['processing_efficiency']:.1f}%")
        report.append(f"- **Average Processing Time**: {perf['average_processing_time']:.1f}s")
        report.append(f"- **Rate Limit Frequency**: {perf['rate_limit_frequency']:.2f} per repository")
        report.append("")

        # Recommendations
        recommendations = analysis_data["recommendations"]
        if recommendations:
            report.append("## Recommendations")
            for rec in recommendations:
                report.append(f"- {rec}")
            report.append("")

        # Configuration
        config = run_data.get("configuration", {})
        if config:
            report.append("## Configuration Used")
            for key, value in config.items():
                report.append(f"- **{key}**: {value}")

        return "\n".join(report)

    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"

    def _fetch_recent_sentry_issues(self) -> Dict[str, Any]:
        """Fetch recent Sentry issues for analysis."""
        try:
            # This would use Sentry MCP tools if available
            # For now, return mock data structure
            return {
                "issues_fetched": 0,
                "connection_errors": 0,
                "timeout_errors": 0,
                "rate_limit_errors": 0,
                "api_errors": 0,
                "critical_issues": [],
                "fetch_error": "Sentry MCP tools not available in current context"
            }
        except Exception as e:
            return {
                "issues_fetched": 0,
                "fetch_error": str(e),
                "critical_issues": []
            }

    def _identify_critical_issues(self, sentry_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify critical issues from Sentry data and local logs."""
        critical_issues = []

        # Analyze local error patterns
        error_patterns = {}
        for error in self.errors:
            error_type = error.get("error_type", "unknown")
            error_msg = error.get("error_message", "")

            # Categorize errors
            if "connection" in error_msg.lower() or "reset" in error_msg.lower():
                pattern = "connection_reset"
            elif "timeout" in error_msg.lower():
                pattern = "timeout"
            elif "rate limit" in error_msg.lower() or "429" in error_msg:
                pattern = "rate_limit"
            elif "api" in error_msg.lower() and ("key" in error_msg.lower() or "auth" in error_msg.lower()):
                pattern = "api_auth"
            else:
                pattern = "other"

            if pattern not in error_patterns:
                error_patterns[pattern] = []
            error_patterns[pattern].append(error)

        # Generate critical issue summaries
        for pattern, errors in error_patterns.items():
            if len(errors) > 3:  # More than 3 occurrences = critical
                critical_issues.append({
                    "pattern": pattern,
                    "count": len(errors),
                    "severity": "critical" if len(errors) > 10 else "high",
                    "description": self._get_pattern_description(pattern),
                    "examples": errors[:3],  # First 3 examples
                    "recommendation": self._get_pattern_recommendation(pattern)
                })

        return critical_issues

    def _get_pattern_description(self, pattern: str) -> str:
        """Get description for error pattern."""
        descriptions = {
            "connection_reset": "Network connections being forcibly closed by remote host",
            "timeout": "Operations timing out, likely due to large file processing or network issues",
            "rate_limit": "API rate limiting preventing requests from completing",
            "api_auth": "API authentication or authorization failures",
            "other": "Miscellaneous errors requiring investigation"
        }
        return descriptions.get(pattern, "Unknown error pattern")

    def _get_pattern_recommendation(self, pattern: str) -> str:
        """Get recommendation for error pattern."""
        recommendations = {
            "connection_reset": "Implement connection pooling, retry logic, and reduce request payload sizes",
            "timeout": "Increase timeout values, implement chunking for large files, add progress monitoring",
            "rate_limit": "Implement exponential backoff, reduce concurrent requests, check API tier limits",
            "api_auth": "Verify API key validity, check authentication headers, ensure proper permissions",
            "other": "Review error logs for specific failure patterns and implement targeted fixes"
        }
        return recommendations.get(pattern, "Investigate error details and implement appropriate fixes")

    def _generate_enhanced_recommendations(self, sentry_data: Dict[str, Any]) -> List[str]:
        """Generate enhanced recommendations based on comprehensive analysis."""
        recommendations = self._generate_recommendations()  # Get base recommendations

        # Add Sentry-specific recommendations
        if sentry_data.get("connection_errors", 0) > 5:
            recommendations.append("🌐 HIGH CONNECTION ERRORS: Implement connection pooling and retry logic for network stability")

        if sentry_data.get("timeout_errors", 0) > 3:
            recommendations.append("⏰ TIMEOUT ISSUES: Increase timeout values and implement chunking for large file processing")

        # Add recommendations based on critical issues
        critical_issues = sentry_data.get("critical_issues", [])
        for issue in critical_issues:
            recommendations.append(f"🚨 {issue['pattern'].upper()}: {issue['recommendation']}")

        return recommendations

    def _generate_enhanced_analysis_report(self, analysis_data: Dict[str, Any]) -> str:
        """Generate enhanced human-readable analysis report."""
        base_report = self._generate_analysis_report(analysis_data)

        # Add Sentry analysis section
        sentry_section = []
        sentry_section.append("\n## Sentry Analysis")

        sentry_data = analysis_data.get("sentry_analysis", {})
        if sentry_data.get("fetch_error"):
            sentry_section.append(f"- **Fetch Status**: ⚠️ {sentry_data['fetch_error']}")
        else:
            sentry_section.append(f"- **Issues Fetched**: {sentry_data.get('issues_fetched', 0)}")
            sentry_section.append(f"- **Connection Errors**: {sentry_data.get('connection_errors', 0)}")
            sentry_section.append(f"- **Timeout Errors**: {sentry_data.get('timeout_errors', 0)}")
            sentry_section.append(f"- **Rate Limit Errors**: {sentry_data.get('rate_limit_errors', 0)}")

        # Add critical issues section
        critical_issues = analysis_data.get("critical_issues", [])
        if critical_issues:
            sentry_section.append("\n### Critical Issues Identified")
            for issue in critical_issues:
                sentry_section.append(f"- **{issue['pattern'].replace('_', ' ').title()}** ({issue['severity']}): {issue['count']} occurrences")
                sentry_section.append(f"  - {issue['description']}")
                sentry_section.append(f"  - Recommendation: {issue['recommendation']}")

        return base_report + "\n".join(sentry_section)

    def _generate_sentry_insights_report(self, sentry_data: Dict[str, Any]) -> str:
        """Generate detailed Sentry insights report."""
        report = []
        report.append("# Sentry Insights Report")
        report.append(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Connection status
        if sentry_data.get("fetch_error"):
            report.append("## ⚠️ Sentry Connection Status")
            report.append(f"**Status**: Failed to fetch data")
            report.append(f"**Error**: {sentry_data['fetch_error']}")
            report.append("")
            report.append("### Manual Sentry Check Recommended")
            report.append("1. Visit https://imimi.de.sentry.io/issues/")
            report.append("2. Filter by project: repomixed-scraper")
            report.append("3. Check for recent issues in the last 2 hours")
            report.append("4. Look for patterns in:")
            report.append("   - Connection errors (ConnectionResetError)")
            report.append("   - Timeout errors (TimeoutExpired)")
            report.append("   - Rate limiting (429 errors)")
            report.append("   - API authentication issues")
        else:
            report.append("## ✅ Sentry Data Summary")
            report.append(f"- Issues fetched: {sentry_data.get('issues_fetched', 0)}")
            report.append(f"- Connection errors: {sentry_data.get('connection_errors', 0)}")
            report.append(f"- Timeout errors: {sentry_data.get('timeout_errors', 0)}")
            report.append(f"- Rate limit errors: {sentry_data.get('rate_limit_errors', 0)}")

        report.append("")
        report.append("## 🔍 Key Issues to Monitor")
        report.append("Based on recent Sentry data, watch for:")
        report.append("")
        report.append("### 1. Connection Reset Errors")
        report.append("- **Pattern**: `ConnectionResetError: [WinError 10054]`")
        report.append("- **Cause**: Remote host forcibly closing connections")
        report.append("- **Impact**: LLM API calls failing mid-request")
        report.append("- **Solution**: Implement connection pooling and retry logic")
        report.append("")
        report.append("### 2. Timeout Issues")
        report.append("- **Pattern**: `TimeoutExpired: Command timed out after 300 seconds`")
        report.append("- **Cause**: Large repositories taking too long to process")
        report.append("- **Impact**: Repomix operations failing on large codebases")
        report.append("- **Solution**: Increase timeouts and implement chunking")
        report.append("")
        report.append("### 3. API Rate Limiting")
        report.append("- **Pattern**: HTTP 429 responses")
        report.append("- **Cause**: Exceeding API rate limits")
        report.append("- **Impact**: LLM analysis requests being rejected")
        report.append("- **Solution**: Implement exponential backoff and reduce concurrency")

        return "\n".join(report)
</file>

<file path="src/redis_queue.py">
"""
Redis-based distributed queue implementation.
Replaces in-memory JoinableQueue with Redis-backed queues for cloud deployment.
"""

import json
import time
import redis
import logging
from typing import Any, Dict, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class QueueItem:
    """Base class for queue items with metadata."""
    id: str
    created_at: str
    data: Dict[str, Any]
    retries: int = 0
    max_retries: int = 3

    def to_json(self) -> str:
        """Convert to JSON string for Redis storage."""
        return json.dumps(asdict(self))
    
    @classmethod
    def from_json(cls, json_str: str) -> 'QueueItem':
        """Create from JSON string."""
        data = json.loads(json_str)
        return cls(**data)

class RepositoryQueueItem(QueueItem):
    """Queue item for repository processing tasks."""

    def __init__(self, repository_url: str, repository_name: str, job_id: str,
                 item_id: Optional[str] = None, retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"repo_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "repository_url": repository_url,
                "repository_name": repository_name,
                "job_id": job_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.repository_url = repository_url
        self.repository_name = repository_name
        self.job_id = job_id

class FileQueueItem(QueueItem):
    """Queue item for LLM processing tasks."""

    def __init__(self, file_path: str, repository_name: str, job_id: str,
                 chunk_id: Optional[str] = None, item_id: Optional[str] = None,
                 retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"file_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "file_path": file_path,
                "repository_name": repository_name,
                "job_id": job_id,
                "chunk_id": chunk_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.file_path = file_path
        self.repository_name = repository_name
        self.job_id = job_id
        self.chunk_id = chunk_id

class RedisQueue:
    """Redis-backed distributed queue implementation."""
    
    def __init__(self, redis_url: str, queue_name: str, 
                 processing_timeout: int = 300):
        """
        Initialize Redis queue.
        
        Args:
            redis_url: Redis connection URL
            queue_name: Name of the queue
            processing_timeout: Timeout for processing items (seconds)
        """
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.queue_name = queue_name
        self.processing_queue = f"{queue_name}:processing"
        self.failed_queue = f"{queue_name}:failed"
        self.processing_timeout = processing_timeout
        
        # Test connection
        try:
            self.redis_client.ping()
            logger.info(f"Connected to Redis queue: {queue_name}")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def put(self, item: QueueItem) -> None:
        """Add item to queue."""
        try:
            self.redis_client.lpush(self.queue_name, item.to_json())
            logger.debug(f"Added item {item.id} to queue {self.queue_name}")
        except Exception as e:
            logger.error(f"Failed to add item to queue: {e}")
            raise
    
    def get(self, timeout: Optional[int] = None) -> Optional[QueueItem]:
        """
        Get item from queue (blocking).
        
        Args:
            timeout: Timeout in seconds (None for indefinite)
            
        Returns:
            QueueItem or None if timeout
        """
        try:
            # Use BRPOPLPUSH for atomic move to processing queue
            result = self.redis_client.brpoplpush(
                self.queue_name, 
                self.processing_queue, 
                timeout=timeout or 0
            )
            
            if result:
                item = QueueItem.from_json(result)
                # Add processing timestamp
                self.redis_client.hset(
                    f"{self.processing_queue}:meta:{item.id}",
                    "started_at", 
                    datetime.now().isoformat()
                )
                logger.debug(f"Retrieved item {item.id} from queue {self.queue_name}")
                return item
            return None
            
        except Exception as e:
            logger.error(f"Failed to get item from queue: {e}")
            raise
    
    def task_done(self, item: QueueItem) -> None:
        """Mark task as completed."""
        try:
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            # Remove metadata
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            logger.debug(f"Marked item {item.id} as done")
        except Exception as e:
            logger.error(f"Failed to mark task as done: {e}")
            raise
    
    def task_failed(self, item: QueueItem, error: str) -> None:
        """Mark task as failed and handle retry logic."""
        try:
            item.retries += 1
            
            if item.retries < item.max_retries:
                # Retry: put back in main queue
                self.redis_client.lpush(self.queue_name, item.to_json())
                logger.warning(f"Retrying item {item.id} (attempt {item.retries})")
            else:
                # Max retries reached: move to failed queue
                failed_item = {
                    "item": asdict(item),
                    "error": error,
                    "failed_at": datetime.now().isoformat()
                }
                self.redis_client.lpush(self.failed_queue, json.dumps(failed_item))
                logger.error(f"Item {item.id} failed permanently: {error}")
            
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            
        except Exception as e:
            logger.error(f"Failed to handle task failure: {e}")
            raise
    
    def size(self) -> int:
        """Get queue size."""
        return self.redis_client.llen(self.queue_name)
    
    def processing_size(self) -> int:
        """Get processing queue size."""
        return self.redis_client.llen(self.processing_queue)
    
    def failed_size(self) -> int:
        """Get failed queue size."""
        return self.redis_client.llen(self.failed_queue)
    
    def cleanup_stale_items(self) -> int:
        """Clean up items that have been processing too long."""
        try:
            stale_count = 0
            processing_items = self.redis_client.lrange(self.processing_queue, 0, -1)
            
            for item_json in processing_items:
                item = QueueItem.from_json(item_json)
                meta_key = f"{self.processing_queue}:meta:{item.id}"
                started_at_str = self.redis_client.hget(meta_key, "started_at")
                
                if started_at_str:
                    started_at = datetime.fromisoformat(started_at_str)
                    if datetime.now() - started_at > timedelta(seconds=self.processing_timeout):
                        # Item is stale, move back to main queue
                        self.redis_client.lrem(self.processing_queue, 1, item_json)
                        self.redis_client.delete(meta_key)
                        self.redis_client.lpush(self.queue_name, item_json)
                        stale_count += 1
                        logger.warning(f"Moved stale item {item.id} back to queue")
            
            return stale_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup stale items: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get queue statistics."""
        return {
            "pending": self.size(),
            "processing": self.processing_size(),
            "failed": self.failed_size()
        }

class QueueManager:
    """Manages multiple Redis queues for the application."""
    
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.queues: Dict[str, RedisQueue] = {}
    
    def get_queue(self, queue_name: str) -> RedisQueue:
        """Get or create a queue."""
        if queue_name not in self.queues:
            self.queues[queue_name] = RedisQueue(self.redis_url, queue_name)
        return self.queues[queue_name]
    
    def get_repository_queue(self) -> RedisQueue:
        """Get the repository processing queue."""
        return self.get_queue("repositories")
    
    def get_file_queue(self) -> RedisQueue:
        """Get the file processing queue."""
        return self.get_queue("files")
    
    def cleanup_all_stale_items(self) -> Dict[str, int]:
        """Cleanup stale items in all queues."""
        results = {}
        for name, queue in self.queues.items():
            results[name] = queue.cleanup_stale_items()
        return results
    
    def get_all_stats(self) -> Dict[str, Dict[str, int]]:
        """Get statistics for all queues."""
        stats = {}
        for name, queue in self.queues.items():
            stats[name] = queue.get_stats()
        return stats
</file>

<file path="src/sentry_analyzer.py">
"""
Sentry data analyzer for automatic log analysis and insights.
"""
import os
import requests
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional


class SentryAnalyzer:
    """Analyzer for Sentry data with automatic insights generation."""
    
    def __init__(self):
        self.base_url = "https://de.sentry.io/api/0"
        self.headers = {
            "Authorization": f"Bearer {os.getenv('SENTRY_AUTH_TOKEN', '')}",
            "Content-Type": "application/json"
        }
        
    def get_recent_issues(self, organization_slug: str, project_slug: str, hours_back: int = 2) -> List[Dict]:
        """Fetch recent issues from Sentry."""
        try:
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            url = f"{self.base_url}/projects/{organization_slug}/{project_slug}/issues/"
            params = {
                "statsPeriod": f"{hours_back}h",
                "query": f"timestamp:>{start_time.isoformat()}",
                "sort": "date",
                "limit": 50
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to fetch Sentry issues: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Error fetching Sentry issues: {e}")
            return []
    
    def get_recent_events(self, organization_slug: str, project_slug: str, hours_back: int = 2) -> List[Dict]:
        """Fetch recent events from Sentry."""
        try:
            url = f"{self.base_url}/projects/{organization_slug}/{project_slug}/events/"
            params = {
                "statsPeriod": f"{hours_back}h",
                "full": "true",
                "limit": 100
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to fetch Sentry events: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Error fetching Sentry events: {e}")
            return []
    
    def analyze_patterns(self, issues: List[Dict], events: List[Dict]) -> Dict[str, Any]:
        """Analyze patterns in Sentry data."""
        analysis = {
            "total_issues": len(issues),
            "total_events": len(events),
            "error_patterns": {},
            "rate_limit_events": 0,
            "api_errors": 0,
            "network_errors": 0,
            "most_common_errors": [],
            "error_frequency": {},
            "recommendations": []
        }
        
        # Analyze issues
        error_counts = {}
        for issue in issues:
            error_type = issue.get('type', 'unknown')
            title = issue.get('title', 'unknown')
            count = issue.get('count', 1)
            
            if error_type not in error_counts:
                error_counts[error_type] = 0
            error_counts[error_type] += count
            
            # Check for specific error patterns
            if 'rate limit' in title.lower() or '429' in title:
                analysis["rate_limit_events"] += count
            elif 'api' in title.lower() or 'http' in title.lower():
                analysis["api_errors"] += count
            elif 'network' in title.lower() or 'connection' in title.lower():
                analysis["network_errors"] += count
        
        # Sort errors by frequency
        analysis["most_common_errors"] = sorted(
            error_counts.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        analysis["error_frequency"] = error_counts
        
        # Analyze events for additional patterns
        event_patterns = {}
        for event in events:
            event_type = event.get('type', 'unknown')
            if event_type not in event_patterns:
                event_patterns[event_type] = 0
            event_patterns[event_type] += 1
        
        analysis["event_patterns"] = event_patterns
        
        return analysis
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Rate limiting recommendations
        if analysis["rate_limit_events"] > 10:
            recommendations.append(
                f"🚨 HIGH RATE LIMITING: {analysis['rate_limit_events']} rate limit events detected. "
                "Consider reducing concurrent workers or increasing delays between requests."
            )
        elif analysis["rate_limit_events"] > 0:
            recommendations.append(
                f"⚠️ Rate limiting detected: {analysis['rate_limit_events']} events. Monitor API usage patterns."
            )
        
        # API error recommendations
        if analysis["api_errors"] > 5:
            recommendations.append(
                f"🔧 API ERRORS: {analysis['api_errors']} API-related errors. "
                "Check API endpoint URLs, authentication, and request formats."
            )
        
        # Network error recommendations
        if analysis["network_errors"] > 5:
            recommendations.append(
                f"🌐 NETWORK ISSUES: {analysis['network_errors']} network-related errors. "
                "Check internet connectivity and consider implementing better retry logic."
            )
        
        # General error frequency recommendations
        if analysis["total_issues"] > 20:
            recommendations.append(
                f"📊 HIGH ERROR VOLUME: {analysis['total_issues']} total issues. "
                "Review error patterns and implement preventive measures."
            )
        
        # Most common error recommendations
        if analysis["most_common_errors"]:
            top_error = analysis["most_common_errors"][0]
            recommendations.append(
                f"🎯 TOP ERROR: '{top_error[0]}' occurred {top_error[1]} times. "
                "Focus on fixing this error type first for maximum impact."
            )
        
        # Success recommendations
        if analysis["total_issues"] == 0:
            recommendations.append("✅ NO ERRORS: Clean run with no Sentry issues detected!")
        
        if not recommendations:
            recommendations.append("📈 System appears stable with minimal error activity.")
        
        return recommendations
    
    def generate_summary_report(self, analysis: Dict[str, Any]) -> str:
        """Generate a human-readable summary report."""
        report = []
        report.append("# Sentry Analysis Summary")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Overview
        report.append("## Overview")
        report.append(f"- Total Issues: {analysis['total_issues']}")
        report.append(f"- Total Events: {analysis['total_events']}")
        report.append(f"- Rate Limit Events: {analysis['rate_limit_events']}")
        report.append(f"- API Errors: {analysis['api_errors']}")
        report.append(f"- Network Errors: {analysis['network_errors']}")
        report.append("")
        
        # Top errors
        if analysis["most_common_errors"]:
            report.append("## Most Common Errors")
            for error_type, count in analysis["most_common_errors"][:5]:
                report.append(f"- {error_type}: {count} occurrences")
            report.append("")
        
        # Recommendations
        recommendations = self.get_recommendations(analysis)
        if recommendations:
            report.append("## Recommendations")
            for rec in recommendations:
                report.append(f"- {rec}")
            report.append("")
        
        return "\n".join(report)
</file>

<file path="src/sentry_config.py">
"""
Sentry configuration and instrumentation for repository research tool.
"""
import os
import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
import logging


def init_sentry():
    """Initialize Sentry SDK with proper configuration."""
    sentry_dsn = os.getenv('SENTRY_DSN')

    if not sentry_dsn or sentry_dsn == 'your_sentry_dsn_here':
        print("⚠️ SENTRY_DSN not configured. Sentry monitoring disabled.")
        return False
    
    # Configure logging integration
    logging_integration = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )
    
    try:
        sentry_sdk.init(
            dsn=sentry_dsn,
            # Enable tracing for performance monitoring
            traces_sample_rate=1.0,  # 100% sampling for development/testing
            # Enable profiling
            profiles_sample_rate=1.0,
            # Add integrations
            integrations=[logging_integration],
            # Environment and release info
            environment=os.getenv('ENVIRONMENT', 'development'),
            release=os.getenv('RELEASE', 'repository-research-tool@1.0.0'),
            # Additional options
            send_default_pii=False,  # Don't send personally identifiable information
            debug=os.getenv('SENTRY_DEBUG', 'false').lower() == 'true',
            # Custom tags
            before_send=add_custom_tags,
        )
        
        print("✅ Sentry monitoring initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize Sentry: {e}")
        return False


def add_custom_tags(event, hint):
    """Add custom tags to Sentry events."""
    event.setdefault('tags', {}).update({
        'component': 'repository-research-tool',
        'worker_type': os.getenv('WORKER_TYPE', 'unknown'),
        'process_id': str(os.getpid()),
    })
    return event


def trace_function(operation_name):
    """Decorator to trace function execution with Sentry."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(name=operation_name, op="function"):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    sentry_sdk.capture_exception(e)
                    raise
        return wrapper
    return decorator


def trace_api_call(api_name, repo_name=None):
    """Context manager for tracing API calls."""
    class APITracer:
        def __init__(self, api_name, repo_name=None):
            self.api_name = api_name
            self.repo_name = repo_name
            self.transaction = None
            
        def __enter__(self):
            transaction_name = f"{self.api_name}"
            if self.repo_name:
                transaction_name += f" - {self.repo_name}"
                
            self.transaction = sentry_sdk.start_transaction(
                name=transaction_name,
                op="api_call"
            )
            self.transaction.__enter__()
            
            # Add context
            sentry_sdk.set_tag("api_name", self.api_name)
            if self.repo_name:
                sentry_sdk.set_tag("repository", self.repo_name)
                
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            if exc_type:
                sentry_sdk.capture_exception(exc_val)
            self.transaction.__exit__(exc_type, exc_val, exc_tb)
            
        def set_status(self, status_code, error_msg=None):
            """Set the status of the API call."""
            sentry_sdk.set_tag("status_code", str(status_code))
            if error_msg:
                sentry_sdk.set_context("error_details", {"message": error_msg})
                
        def add_breadcrumb(self, message, category="api", level="info"):
            """Add a breadcrumb to track API call progress."""
            sentry_sdk.add_breadcrumb(
                message=message,
                category=category,
                level=level
            )
    
    return APITracer(api_name, repo_name)


def log_worker_start(worker_type, worker_id):
    """Log worker startup with Sentry context."""
    sentry_sdk.set_tag("worker_type", worker_type)
    sentry_sdk.set_tag("worker_id", str(worker_id))
    sentry_sdk.add_breadcrumb(
        message=f"{worker_type} worker {worker_id} started",
        category="worker",
        level="info"
    )


def log_rate_limit_event(worker_id, repo_name, retry_delay, attempt):
    """Log rate limiting events for monitoring."""
    sentry_sdk.add_breadcrumb(
        message=f"Rate limited - Worker {worker_id}, Repo: {repo_name}, Delay: {retry_delay}s, Attempt: {attempt}",
        category="rate_limit",
        level="warning",
        data={
            "worker_id": worker_id,
            "repository": repo_name,
            "retry_delay": retry_delay,
            "attempt": attempt
        }
    )


def log_processing_metrics(phase, duration, count, success_count=None):
    """Log processing metrics for performance monitoring."""
    sentry_sdk.add_breadcrumb(
        message=f"{phase} completed - Duration: {duration:.2f}s, Count: {count}",
        category="metrics",
        level="info",
        data={
            "phase": phase,
            "duration": duration,
            "total_count": count,
            "success_count": success_count or count,
            "success_rate": (success_count or count) / count if count > 0 else 0
        }
    )
</file>

<file path="src/storage_manager.py">
"""
Storage manager for handling file operations with Supabase Storage.
Abstracts file storage operations for repomix files and analysis results.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path
from datetime import datetime
from src.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)

class StorageManager:
    """Manages file storage operations with Supabase Storage."""
    
    def __init__(self, supabase_client: SupabaseClient):
        """
        Initialize storage manager.
        
        Args:
            supabase_client: Configured Supabase client
        """
        self.client = supabase_client
        self.repomix_bucket = "repomixes"
        self.analysis_bucket = "analyses"
        self.logs_bucket = "logs"
    
    def upload_repomix_file(self, job_id: str, repository_name: str, 
                           content: str) -> str:
        """
        Upload repomix file to storage.
        
        Args:
            job_id: Job identifier
            repository_name: Repository name (sanitized)
            content: Repomix file content
            
        Returns:
            Public URL of uploaded file
        """
        try:
            # Create file path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"{job_id}/{repository_name}_{timestamp}.md"
            
            # Upload file
            url = self.client.upload_file(
                bucket=self.repomix_bucket,
                file_path=file_path,
                file_content=content.encode('utf-8'),
                content_type="text/markdown"
            )
            
            logger.info(f"Uploaded repomix file for {repository_name}: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload repomix file for {repository_name}: {e}")
            raise
    
    def upload_analysis_file(self, job_id: str, analysis_data: Dict, 
                           filename: Optional[str] = None) -> str:
        """
        Upload analysis results file to storage.
        
        Args:
            job_id: Job identifier
            analysis_data: Analysis data dictionary
            filename: Optional custom filename
            
        Returns:
            Public URL of uploaded file
        """
        try:
            # Create filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"analysis_{timestamp}.json"
            
            file_path = f"{job_id}/{filename}"
            
            # Convert to JSON
            json_content = json.dumps(analysis_data, indent=2, ensure_ascii=False)
            
            # Upload file
            url = self.client.upload_file(
                bucket=self.analysis_bucket,
                file_path=file_path,
                file_content=json_content.encode('utf-8'),
                content_type="application/json"
            )
            
            logger.info(f"Uploaded analysis file for job {job_id}: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload analysis file for job {job_id}: {e}")
            raise
    
    def upload_log_file(self, job_id: str, log_content: str, 
                       log_type: str = "general") -> str:
        """
        Upload log file to storage.
        
        Args:
            job_id: Job identifier
            log_content: Log file content
            log_type: Type of log (general, error, sentry, etc.)
            
        Returns:
            Public URL of uploaded file
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"{job_id}/{log_type}_{timestamp}.log"
            
            # Upload file
            url = self.client.upload_file(
                bucket=self.logs_bucket,
                file_path=file_path,
                file_content=log_content.encode('utf-8'),
                content_type="text/plain"
            )
            
            logger.debug(f"Uploaded log file for job {job_id}: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload log file for job {job_id}: {e}")
            raise
    
    def download_repomix_file(self, file_url: str) -> str:
        """
        Download repomix file content.
        
        Args:
            file_url: Public URL of the file
            
        Returns:
            File content as string
        """
        try:
            # Extract file path from URL
            file_path = self._extract_file_path_from_url(file_url, self.repomix_bucket)
            
            # Download file
            content_bytes = self.client.download_file(self.repomix_bucket, file_path)
            content = content_bytes.decode('utf-8')
            
            logger.debug(f"Downloaded repomix file: {file_path}")
            return content
            
        except Exception as e:
            logger.error(f"Failed to download repomix file {file_url}: {e}")
            raise
    
    def download_analysis_file(self, file_url: str) -> Dict:
        """
        Download and parse analysis file.
        
        Args:
            file_url: Public URL of the file
            
        Returns:
            Analysis data as dictionary
        """
        try:
            # Extract file path from URL
            file_path = self._extract_file_path_from_url(file_url, self.analysis_bucket)
            
            # Download file
            content_bytes = self.client.download_file(self.analysis_bucket, file_path)
            content = content_bytes.decode('utf-8')
            
            # Parse JSON
            analysis_data = json.loads(content)
            
            logger.debug(f"Downloaded analysis file: {file_path}")
            return analysis_data
            
        except Exception as e:
            logger.error(f"Failed to download analysis file {file_url}: {e}")
            raise
    
    def list_job_files(self, job_id: str, bucket: str) -> List[Dict]:
        """
        List all files for a specific job in a bucket.
        
        Args:
            job_id: Job identifier
            bucket: Bucket name
            
        Returns:
            List of file information dictionaries
        """
        try:
            files = self.client.list_files(bucket, job_id)
            logger.debug(f"Listed {len(files)} files for job {job_id} in bucket {bucket}")
            return files
        except Exception as e:
            logger.error(f"Failed to list files for job {job_id} in bucket {bucket}: {e}")
            raise
    
    def delete_job_files(self, job_id: str, bucket: str) -> int:
        """
        Delete all files for a specific job in a bucket.
        
        Args:
            job_id: Job identifier
            bucket: Bucket name
            
        Returns:
            Number of files deleted
        """
        try:
            files = self.list_job_files(job_id, bucket)
            deleted_count = 0
            
            for file_info in files:
                file_path = f"{job_id}/{file_info['name']}"
                self.client.delete_file(bucket, file_path)
                deleted_count += 1
            
            logger.info(f"Deleted {deleted_count} files for job {job_id} from bucket {bucket}")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to delete files for job {job_id} from bucket {bucket}: {e}")
            raise
    
    def get_storage_stats(self, job_id: Optional[str] = None) -> Dict[str, Dict]:
        """
        Get storage statistics.
        
        Args:
            job_id: Optional job ID to get stats for specific job
            
        Returns:
            Storage statistics by bucket
        """
        try:
            stats = {}
            buckets = [self.repomix_bucket, self.analysis_bucket, self.logs_bucket]
            
            for bucket in buckets:
                try:
                    if job_id:
                        files = self.list_job_files(job_id, bucket)
                    else:
                        files = self.client.list_files(bucket)
                    
                    total_size = sum(file_info.get('metadata', {}).get('size', 0) 
                                   for file_info in files)
                    
                    stats[bucket] = {
                        "file_count": len(files),
                        "total_size_bytes": total_size,
                        "total_size_mb": round(total_size / (1024 * 1024), 2)
                    }
                except Exception as e:
                    logger.warning(f"Failed to get stats for bucket {bucket}: {e}")
                    stats[bucket] = {"error": str(e)}
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            raise
    
    def _extract_file_path_from_url(self, file_url: str, bucket: str) -> str:
        """
        Extract file path from Supabase storage URL.
        
        Args:
            file_url: Full public URL
            bucket: Bucket name
            
        Returns:
            File path within the bucket
        """
        try:
            # Supabase storage URLs have format:
            # https://project.supabase.co/storage/v1/object/public/bucket/path
            url_parts = file_url.split(f"/storage/v1/object/public/{bucket}/")
            if len(url_parts) != 2:
                raise ValueError(f"Invalid storage URL format: {file_url}")
            
            return url_parts[1]
            
        except Exception as e:
            logger.error(f"Failed to extract file path from URL {file_url}: {e}")
            raise
    
    def create_local_backup(self, job_id: str, local_dir: str) -> Dict[str, str]:
        """
        Create local backup of all files for a job.
        
        Args:
            job_id: Job identifier
            local_dir: Local directory to save files
            
        Returns:
            Dictionary mapping bucket names to local file paths
        """
        try:
            local_paths = {}
            backup_dir = Path(local_dir) / f"backup_{job_id}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            buckets = [self.repomix_bucket, self.analysis_bucket, self.logs_bucket]
            
            for bucket in buckets:
                try:
                    files = self.list_job_files(job_id, bucket)
                    bucket_dir = backup_dir / bucket
                    bucket_dir.mkdir(exist_ok=True)
                    
                    for file_info in files:
                        file_path = f"{job_id}/{file_info['name']}"
                        content = self.client.download_file(bucket, file_path)
                        
                        local_file_path = bucket_dir / file_info['name']
                        local_file_path.write_bytes(content)
                        
                        if bucket not in local_paths:
                            local_paths[bucket] = []
                        local_paths[bucket].append(str(local_file_path))
                    
                except Exception as e:
                    logger.warning(f"Failed to backup files from bucket {bucket}: {e}")
            
            logger.info(f"Created local backup for job {job_id} in {backup_dir}")
            return local_paths
            
        except Exception as e:
            logger.error(f"Failed to create local backup for job {job_id}: {e}")
            raise

def create_storage_manager() -> StorageManager:
    """Create storage manager from environment variables."""
    from src.supabase_client import create_supabase_client
    
    supabase_client = create_supabase_client()
    return StorageManager(supabase_client)
</file>

<file path="src/supabase_client.py">
"""
Supabase client integration for database and storage operations.
Handles PostgreSQL database operations and S3-compatible storage.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from supabase import create_client, Client
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class JobRecord:
    """Database record for analysis jobs."""
    id: str
    keywords: str
    min_stars: int
    max_repos: int
    status: str  # pending, running, completed, failed
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    error_message: Optional[str] = None
    analysis_file_url: Optional[str] = None
    total_repositories: Optional[int] = None
    custom_prompt: Optional[str] = None
    debug: bool = False

@dataclass
class RepositoryRecord:
    """Database record for repository information."""
    id: str
    job_id: str
    name: str
    url: str
    stars: int
    file_size_mb: float
    repomix_file_url: Optional[str] = None
    processed_at: Optional[str] = None
    analysis_status: str = "pending"  # pending, processing, completed, failed
    error_message: Optional[str] = None

@dataclass
class AnalysisRecord:
    """Database record for analysis results."""
    id: str
    repository_id: str
    job_id: str
    worker_id: str
    processed_at: str
    analysis_content: str
    analysis_length: int
    chunk_id: Optional[str] = None

class SupabaseClient:
    """Supabase client for database and storage operations."""
    
    def __init__(self, url: str, key: str):
        """
        Initialize Supabase client.
        
        Args:
            url: Supabase project URL
            key: Supabase API key (anon or service role)
        """
        self.client: Client = create_client(url, key)
        self.url = url
        self.key = key
        
        # Test connection
        try:
            # Simple query to test connection
            result = self.client.table('jobs').select('id').limit(1).execute()
            logger.info("Connected to Supabase successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Supabase: {e}")
            raise
    
    # Job Management
    def create_job(self, job: JobRecord) -> JobRecord:
        """Create a new analysis job."""
        try:
            result = self.client.table('jobs').insert(asdict(job)).execute()
            logger.info(f"Created job {job.id}")
            return JobRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def get_job(self, job_id: str) -> Optional[JobRecord]:
        """Get job by ID."""
        try:
            result = self.client.table('jobs').select('*').eq('id', job_id).execute()
            if result.data:
                return JobRecord(**result.data[0])
            return None
        except Exception as e:
            logger.error(f"Failed to get job {job_id}: {e}")
            raise
    
    def update_job_status(self, job_id: str, status: str, 
                         error_message: Optional[str] = None,
                         analysis_file_url: Optional[str] = None,
                         total_repositories: Optional[int] = None) -> None:
        """Update job status and related fields."""
        try:
            update_data = {"status": status}
            
            if status == "running" and not self.get_job(job_id).started_at:
                update_data["started_at"] = datetime.now().isoformat()
            elif status in ["completed", "failed"]:
                update_data["completed_at"] = datetime.now().isoformat()
            
            if error_message:
                update_data["error_message"] = error_message
            if analysis_file_url:
                update_data["analysis_file_url"] = analysis_file_url
            if total_repositories is not None:
                update_data["total_repositories"] = total_repositories
            
            self.client.table('jobs').update(update_data).eq('id', job_id).execute()
            logger.debug(f"Updated job {job_id} status to {status}")
            
        except Exception as e:
            logger.error(f"Failed to update job status: {e}")
            raise
    
    def list_jobs(self, limit: int = 50, offset: int = 0) -> List[JobRecord]:
        """List jobs with pagination."""
        try:
            result = self.client.table('jobs')\
                .select('*')\
                .order('created_at', desc=True)\
                .range(offset, offset + limit - 1)\
                .execute()
            
            return [JobRecord(**job) for job in result.data]
        except Exception as e:
            logger.error(f"Failed to list jobs: {e}")
            raise
    
    # Repository Management
    def create_repository(self, repo: RepositoryRecord) -> RepositoryRecord:
        """Create a repository record."""
        try:
            result = self.client.table('repositories').insert(asdict(repo)).execute()
            logger.debug(f"Created repository record {repo.id}")
            return RepositoryRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create repository: {e}")
            raise
    
    def update_repository_status(self, repo_id: str, status: str,
                               repomix_file_url: Optional[str] = None,
                               error_message: Optional[str] = None) -> None:
        """Update repository processing status."""
        try:
            update_data = {
                "analysis_status": status,
                "processed_at": datetime.now().isoformat()
            }
            
            if repomix_file_url:
                update_data["repomix_file_url"] = repomix_file_url
            if error_message:
                update_data["error_message"] = error_message
            
            self.client.table('repositories').update(update_data).eq('id', repo_id).execute()
            logger.debug(f"Updated repository {repo_id} status to {status}")
            
        except Exception as e:
            logger.error(f"Failed to update repository status: {e}")
            raise
    
    def get_repositories_by_job(self, job_id: str) -> List[RepositoryRecord]:
        """Get all repositories for a job."""
        try:
            result = self.client.table('repositories')\
                .select('*')\
                .eq('job_id', job_id)\
                .order('created_at')\
                .execute()
            
            return [RepositoryRecord(**repo) for repo in result.data]
        except Exception as e:
            logger.error(f"Failed to get repositories for job {job_id}: {e}")
            raise
    
    # Analysis Management
    def create_analysis(self, analysis: AnalysisRecord) -> AnalysisRecord:
        """Create an analysis record."""
        try:
            result = self.client.table('analyses').insert(asdict(analysis)).execute()
            logger.debug(f"Created analysis record {analysis.id}")
            return AnalysisRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create analysis: {e}")
            raise
    
    def get_analyses_by_job(self, job_id: str) -> List[AnalysisRecord]:
        """Get all analyses for a job."""
        try:
            result = self.client.table('analyses')\
                .select('*')\
                .eq('job_id', job_id)\
                .order('processed_at')\
                .execute()
            
            return [AnalysisRecord(**analysis) for analysis in result.data]
        except Exception as e:
            logger.error(f"Failed to get analyses for job {job_id}: {e}")
            raise
    
    # Storage Operations
    def upload_file(self, bucket: str, file_path: str, file_content: bytes,
                   content_type: str = "application/octet-stream") -> str:
        """
        Upload file to Supabase storage.
        
        Returns:
            Public URL of uploaded file
        """
        try:
            # Upload file
            result = self.client.storage.from_(bucket).upload(
                file_path, 
                file_content,
                file_options={"content-type": content_type}
            )
            
            if result.error:
                raise Exception(f"Upload failed: {result.error}")
            
            # Get public URL
            public_url = self.client.storage.from_(bucket).get_public_url(file_path)
            logger.debug(f"Uploaded file to {public_url}")
            return public_url
            
        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            raise
    
    def download_file(self, bucket: str, file_path: str) -> bytes:
        """Download file from Supabase storage."""
        try:
            result = self.client.storage.from_(bucket).download(file_path)
            if result.error:
                raise Exception(f"Download failed: {result.error}")
            return result.data
        except Exception as e:
            logger.error(f"Failed to download file {file_path}: {e}")
            raise
    
    def delete_file(self, bucket: str, file_path: str) -> None:
        """Delete file from Supabase storage."""
        try:
            result = self.client.storage.from_(bucket).remove([file_path])
            if result.error:
                raise Exception(f"Delete failed: {result.error}")
            logger.debug(f"Deleted file {file_path}")
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            raise
    
    def list_files(self, bucket: str, folder: str = "") -> List[Dict[str, Any]]:
        """List files in a bucket/folder."""
        try:
            result = self.client.storage.from_(bucket).list(folder)
            if result.error:
                raise Exception(f"List failed: {result.error}")
            return result.data
        except Exception as e:
            logger.error(f"Failed to list files in {bucket}/{folder}: {e}")
            raise

def create_supabase_client() -> SupabaseClient:
    """Create Supabase client from environment variables."""
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")
    
    return SupabaseClient(url, key)
</file>

<file path="worker.py">
#!/usr/bin/env python3
"""
Worker service for Repository Research Tool.
Runs distributed workers that process repositories and LLM analysis tasks.
"""

import os
import sys
import time
import signal
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from config import ScraperConfig
from cloud_pipeline import CloudRepositoryPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorkerService:
    """
    Worker service that runs distributed processing workers.
    Processes repositories and LLM analysis tasks from Redis queues.
    """
    
    def __init__(self):
        """Initialize the worker service."""
        self.config = ScraperConfig()
        self.pipeline = None
        self.running = False
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Worker service initialized")
        logger.info(f"Configuration: {self.config.REPOMIX_WORKERS} repomix workers, {self.config.LLM_WORKERS} LLM workers")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    def start(self):
        """Start the worker service."""
        logger.info("🚀 Starting Repository Research Tool Worker Service")
        logger.info(f"   Deployment mode: {self.config.DEPLOYMENT_MODE}")
        logger.info(f"   Redis URL: {self.config.REDIS_URL}")
        logger.info(f"   Workers: {self.config.REPOMIX_WORKERS} repomix + {self.config.LLM_WORKERS} LLM")
        
        try:
            # Initialize cloud pipeline for worker operations
            self.pipeline = CloudRepositoryPipeline(config=self.config)
            
            # Start worker processes
            self.pipeline.start_workers()
            
            self.running = True
            logger.info("✅ Worker service started successfully")
            
            # Keep the service running
            self._run_worker_loop()
            
        except Exception as e:
            logger.error(f"❌ Failed to start worker service: {e}")
            raise
        finally:
            self._shutdown()
    
    def _run_worker_loop(self):
        """Main worker loop that keeps the service running."""
        logger.info("Worker service running... Press Ctrl+C to stop")
        
        try:
            while self.running:
                # Monitor worker health and restart if needed
                self._check_worker_health()
                
                # Sleep for a short interval
                time.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
            self.running = False
    
    def _check_worker_health(self):
        """Check worker process health and restart if needed."""
        if not self.pipeline:
            return
        
        try:
            # Check repomix workers
            dead_repomix = []
            for i, process in enumerate(self.pipeline.repomix_processes):
                if not process.is_alive():
                    dead_repomix.append(i)
            
            # Check LLM workers
            dead_llm = []
            for i, process in enumerate(self.pipeline.llm_processes):
                if not process.is_alive():
                    dead_llm.append(i)
            
            # Log any dead workers
            if dead_repomix:
                logger.warning(f"Dead repomix workers detected: {dead_repomix}")
            if dead_llm:
                logger.warning(f"Dead LLM workers detected: {dead_llm}")
            
            # In a production system, you might restart dead workers here
            # For now, we just log the issue
            
        except Exception as e:
            logger.error(f"Error checking worker health: {e}")
    
    def _shutdown(self):
        """Shutdown the worker service gracefully."""
        logger.info("🛑 Shutting down worker service...")
        
        try:
            if self.pipeline:
                self.pipeline.stop_workers()
                logger.info("✅ All workers stopped")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        logger.info("Worker service shutdown complete")

def main():
    """Main entry point for the worker service."""
    try:
        # Check if we're in cloud mode
        config = ScraperConfig()
        if not config.USE_CLOUD_SERVICES:
            logger.error("❌ Worker service requires cloud services to be enabled")
            logger.error("   Set DEPLOYMENT_MODE=cloud or USE_CLOUD_SERVICES=true")
            return 1
        
        # Start the worker service
        worker_service = WorkerService()
        worker_service.start()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Worker service interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"❌ Worker service failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="main.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Main Entry Point

Usage:
    python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
    python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze this code for AI patterns"
"""

import argparse
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_custom_prompt_template():
    """Create a template for custom prompts with placeholders"""
    return """CUSTOM REPOSITORY ANALYSIS

Repository: {repo_name}
File size: {file_size_mb:.2f}MB

[Your custom analysis instructions here]

Code content:
{content}
"""

def main():
    """Main entry point for the repository research tool"""
    parser = argparse.ArgumentParser(
        description="Repository Research Tool - Analyze GitHub repositories with AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
  python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze for AI patterns"
  python main.py --keywords "web frameworks" --min-stars 100 --max-repos 5 --debug

Custom Prompt Template:
  Use {repo_name}, {file_size_mb}, and {content} as placeholders in your custom prompt.
        """
    )
    
    # Required arguments
    parser.add_argument(
        '--keywords',
        required=True,
        help='Comma-separated list of keywords to search for (e.g., "cursor rules,code prompts")'
    )
    
    parser.add_argument(
        '--min-stars',
        type=int,
        default=30,
        help='Minimum number of stars for repositories (default: 30)'
    )
    
    parser.add_argument(
        '--max-repos',
        type=int,
        default=15,
        help='Maximum number of repositories per keyword (default: 15)'
    )
    
    # Optional arguments
    parser.add_argument(
        '--custom-prompt',
        help='Custom LLM analysis prompt. Use {repo_name}, {file_size_mb}, and {content} as placeholders.'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode with verbose logging'
    )
    
    parser.add_argument(
        '--output-dir',
        help='Custom output directory name (default: auto-generated timestamp)'
    )
    
    args = parser.parse_args()
    
    # Parse keywords
    keywords = [k.strip() for k in args.keywords.split(',')]
    
    print("REPOSITORY RESEARCH TOOL")
    print("=" * 60)
    print(f"Configuration:")
    print(f"   Keywords: {keywords}")
    print(f"   Min stars: {args.min_stars}")
    print(f"   Max repos per keyword: {args.max_repos}")
    print(f"   Expected total repos: ~{len(keywords) * args.max_repos}")
    print(f"   Custom prompt: {'Yes' if args.custom_prompt else 'No (using default)'}")
    print(f"   Debug mode: {'Yes' if args.debug else 'No'}")
    
    try:
        # Import and configure
        from config import ScraperConfig
        from pipeline import RepositoryPipeline
        from github_search import GitHubSearcher
        from sentry_config import init_sentry

        # Initialize Sentry monitoring
        init_sentry()

        # Load configuration
        config = ScraperConfig()
        
        # Set custom prompt if provided
        if args.custom_prompt:
            config.CUSTOM_LLM_PROMPT = args.custom_prompt
            print(f"Custom prompt configured")
        
        # Validate API key
        if not config.LLM_API_KEY:
            print("Error: LLM_API_KEY not configured in .env file")
            print("   Please add your Gemini API key to .env file")
            return 1

        print(f"Configuration loaded:")
        print(f"   API key: Configured")
        print(f"   Repomix workers: {config.REPOMIX_WORKERS}")
        print(f"   LLM workers: {config.LLM_WORKERS}")
        
        # Search for repositories
        print(f"\nSearching for repositories (sorted by last updated)...")
        searcher = GitHubSearcher(config)
        
        all_repositories = []
        for keyword in keywords:
            print(f"   Searching for: '{keyword}'")
            repos = searcher.search_repositories(
                keyword, 
                min_stars=args.min_stars, 
                max_repos=args.max_repos
            )
            print(f"   Found {len(repos)} repositories for '{keyword}'")
            
            # Show first few repos
            for i, repo in enumerate(repos[:3]):
                updated_at = repo.get('updated_at', 'unknown')
                print(f"      {i+1}. {repo.get('full_name', 'unknown')} ({repo.get('stargazers_count', 0)} stars, updated: {updated_at[:10]})")
            
            if len(repos) > 3:
                print(f"      ... and {len(repos) - 3} more repositories")
            
            all_repositories.extend(repos)
        
        print(f"\nTotal repositories to process: {len(all_repositories)}")

        if len(all_repositories) == 0:
            print("No repositories found - check keywords and min-stars filter")
            return 1

        # Initialize pipeline
        print(f"\nInitializing Pipeline...")
        first_keyword = keywords[0].replace(' ', '_').replace('-', '_')
        if args.output_dir:
            # Custom output directory logic would go here
            pipeline = RepositoryPipeline(config=config, first_keyword=first_keyword)
        else:
            pipeline = RepositoryPipeline(config=config, first_keyword=first_keyword)
        
        print(f"Pipeline initialized")
        print(f"   Output directory: {pipeline.output_dir}")
        
        # Run the pipeline
        print(f"\nRunning Complete Pipeline...")
        print(f"   Processing {len(all_repositories)} repositories")
        print(f"   Using {config.REPOMIX_WORKERS} repomix workers")
        print(f"   Using {config.LLM_WORKERS} LLM workers")
        print(f"   Expected time: ~{len(all_repositories) * 1.5} minutes")
        print("=" * 60)

        # Run the pipeline
        pipeline.run(all_repositories)

        print("=" * 60)
        print(f"ANALYSIS COMPLETED!")
        
        # Show results
        analysis_file = pipeline.output_dir / "analysis.json"
        if analysis_file.exists():
            file_size = analysis_file.stat().st_size
            print(f"Results saved to: {pipeline.output_dir}")
            print(f"   Main analysis: analysis.json ({file_size:,} bytes)")
            print(f"   Repomix files: repomixes/ directory")
            print(f"   Error tracking: sentry_analysis.json")
        else:
            print(f"Analysis file not found - check for errors")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\nAnalysis interrupted by user")
        return 0

    except Exception as e:
        print(f"\nAnalysis failed: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="src/cloud_pipeline.py">
"""
Cloud-integrated pipeline for Repository Research Tool.
Uses Redis queues, Supabase database, and cloud storage.
"""

import os
import uuid
import time
import json
import logging
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any

from config import ScraperConfig
from redis_queue import QueueManager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord, AnalysisRecord
from storage_manager import create_storage_manager
from monitoring import RepositoryMonitor
from llm_rate_limiter import LLMRateLimiter

# Import worker functions from original pipeline
from pipeline import repomix_worker, llm_worker

logger = logging.getLogger(__name__)

class CloudRepositoryPipeline:
    """
    Cloud-native pipeline for repository processing.
    Uses Redis for distributed queues, Supabase for persistence, and cloud storage.
    """
    
    def __init__(self, config=None, job_id=None):
        """
        Initialize the cloud pipeline.
        
        Args:
            config: Configuration object (defaults to ScraperConfig)
            job_id: Unique job identifier (generated if not provided)
        """
        self.config = config or ScraperConfig()
        self.job_id = job_id or str(uuid.uuid4())
        
        # Initialize cloud services
        self._initialize_cloud_services()
        
        # Initialize local components
        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None
        
        # Create local output directory for temporary files
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
        self.output_dir = Path(self.config.OUTPUT_BASE) / f"cloud_{timestamp_str}"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
        
        logger.info(f"Initialized cloud pipeline for job {self.job_id}")
    
    def _initialize_cloud_services(self):
        """Initialize cloud service connections."""
        try:
            # Initialize queue manager
            self.queue_manager = QueueManager(self.config.REDIS_URL)
            self.repo_queue = self.queue_manager.get_repository_queue()
            self.file_queue = self.queue_manager.get_file_queue()

            # Initialize distributed rate limiter with Redis client
            self.rate_limiter = LLMRateLimiter(
                redis_client=self.queue_manager.redis_client,
                rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN,
                rate_limit_requests_per_min=getattr(self.config, 'LLM_RATE_LIMIT_REQUESTS_PER_MIN', 1000)
            )

            # Initialize Supabase client
            self.supabase_client = create_supabase_client()

            # Initialize storage manager
            self.storage_manager = create_storage_manager()
            
            logger.info("Cloud services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cloud services: {e}")
            raise
    
    def create_job(self, keywords: str, min_stars: int, max_repos: int,
                   custom_prompt: Optional[str] = None, debug: bool = False) -> JobRecord:
        """
        Create a new analysis job in the database.
        
        Args:
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            custom_prompt: Optional custom LLM prompt
            debug: Debug mode flag
            
        Returns:
            Created job record
        """
        try:
            job = JobRecord(
                id=self.job_id,
                keywords=keywords,
                min_stars=min_stars,
                max_repos=max_repos,
                status="pending",
                created_at=datetime.now().isoformat(),
                custom_prompt=custom_prompt,
                debug=debug
            )
            
            created_job = self.supabase_client.create_job(job)
            logger.info(f"Created job {self.job_id} in database")
            return created_job
            
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def add_repositories(self, repositories: List[Dict[str, Any]]) -> int:
        """
        Add repositories to the processing queue and database.
        
        Args:
            repositories: List of repository information dictionaries
            
        Returns:
            Number of repositories added
        """
        try:
            added_count = 0
            
            for repo_info in repositories:
                # Create repository record in database
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=self.job_id,
                    name=repo_info.get('full_name', 'unknown'),
                    url=repo_info.get('html_url', ''),
                    stars=repo_info.get('stargazers_count', 0),
                    file_size_mb=0.0  # Will be updated after repomix processing
                )
                
                self.supabase_client.create_repository(repo_record)
                
                # Add to Redis queue
                queue_item = RepositoryQueueItem(
                    repository_url=repo_info.get('html_url', ''),
                    repository_name=repo_info.get('full_name', 'unknown'),
                    job_id=self.job_id
                )
                
                self.repo_queue.put(queue_item)
                added_count += 1
            
            # Update job with total repositories
            self.supabase_client.update_job_status(
                self.job_id, 
                "running", 
                total_repositories=added_count
            )
            
            logger.info(f"Added {added_count} repositories to queue and database")
            return added_count
            
        except Exception as e:
            logger.error(f"Failed to add repositories: {e}")
            raise
    
    def start_workers(self):
        """Start repomix and LLM worker processes."""
        try:
            # Start repomix workers
            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=self._cloud_repomix_worker,
                    args=(i,)
                )
                p.start()
                self.repomix_processes.append(p)
            
            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=self._cloud_llm_worker,
                    args=(i,)
                )
                p.start()
                self.llm_processes.append(p)
            
            logger.info(f"Started {len(self.repomix_processes)} repomix and {len(self.llm_processes)} LLM workers")
            
        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            raise
    
    def _cloud_repomix_worker(self, worker_id: int):
        """
        Cloud-integrated repomix worker.
        
        Args:
            worker_id: Worker identifier
        """
        logger.info(f"Starting cloud repomix worker {worker_id}")
        
        while True:
            try:
                # Get repository from queue
                repo_item = self.repo_queue.get(timeout=30)
                if repo_item is None:
                    break
                
                logger.info(f"Worker {worker_id} processing {repo_item.repository_name}")
                
                # Process repository with repomix (reuse existing logic)
                # This would call the existing repomix processing logic
                # but upload results to cloud storage instead of local files
                
                # Simulate repomix processing for now
                repomix_content = f"# Repomix output for {repo_item.repository_name}\n\nProcessed by worker {worker_id}"
                
                # Upload to cloud storage
                repomix_url = self.storage_manager.upload_repomix_file(
                    job_id=self.job_id,
                    repository_name=repo_item.repository_name.replace('/', '_'),
                    content=repomix_content
                )
                
                # Update repository record
                # Find repository by name and job_id
                repositories = self.supabase_client.get_repositories_by_job(self.job_id)
                repo_record = next((r for r in repositories if r.name == repo_item.repository_name), None)
                
                if repo_record:
                    self.supabase_client.update_repository_status(
                        repo_record.id,
                        "completed",
                        repomix_file_url=repomix_url
                    )
                
                # Add to LLM queue
                file_item = FileQueueItem(
                    file_path=repomix_url,
                    repository_name=repo_item.repository_name,
                    job_id=self.job_id
                )
                self.file_queue.put(file_item)
                
                # Mark task as done
                self.repo_queue.task_done(repo_item)
                
                logger.info(f"Worker {worker_id} completed {repo_item.repository_name}")
                
            except Exception as e:
                logger.error(f"Repomix worker {worker_id} error: {e}")
                if 'repo_item' in locals():
                    self.repo_queue.task_failed(repo_item, str(e))
                break
    
    def _cloud_llm_worker(self, worker_id: int):
        """
        Cloud-integrated LLM worker.
        
        Args:
            worker_id: Worker identifier
        """
        logger.info(f"Starting cloud LLM worker {worker_id}")
        
        while True:
            try:
                # Get file from queue
                file_item = self.file_queue.get(timeout=30)
                if file_item is None:
                    break
                
                logger.info(f"LLM worker {worker_id} processing {file_item.repository_name}")
                
                # Download file content
                repomix_content = self.storage_manager.download_repomix_file(file_item.file_path)

                # Calculate file size for rate limiting
                file_size_mb = len(repomix_content.encode('utf-8')) / (1024 * 1024)

                # Wait for rate limit slot
                if not self.rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300):
                    logger.warning(f"LLM worker {worker_id} timed out waiting for rate limit slot")
                    continue

                logger.info(f"LLM worker {worker_id} acquired rate limit slot for {file_item.repository_name} ({file_size_mb:.2f}MB)")

                # Process with LLM (reuse existing logic)
                # This would call the existing LLM processing logic

                # Simulate LLM analysis for now
                analysis_content = f"Analysis of {file_item.repository_name} by LLM worker {worker_id}"
                
                # Create analysis record
                analysis_record = AnalysisRecord(
                    id=str(uuid.uuid4()),
                    repository_id="",  # Would need to look up repository ID
                    job_id=self.job_id,
                    worker_id=str(worker_id),
                    processed_at=datetime.now().isoformat(),
                    analysis_content=analysis_content,
                    analysis_length=len(analysis_content)
                )
                
                self.supabase_client.create_analysis(analysis_record)
                
                # Mark task as done
                self.file_queue.task_done(file_item)
                
                logger.info(f"LLM worker {worker_id} completed {file_item.repository_name}")
                
            except Exception as e:
                logger.error(f"LLM worker {worker_id} error: {e}")
                if 'file_item' in locals():
                    self.file_queue.task_failed(file_item, str(e))
                break
    
    def wait_for_completion(self, timeout: Optional[int] = None):
        """
        Wait for all processing to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
        """
        try:
            start_time = time.time()
            
            while True:
                # Check queue statistics
                stats = self.queue_manager.get_all_stats()
                repo_pending = stats.get('repositories', {}).get('pending', 0)
                file_pending = stats.get('files', {}).get('pending', 0)
                
                if repo_pending == 0 and file_pending == 0:
                    logger.info("All processing completed")
                    break
                
                if timeout and (time.time() - start_time) > timeout:
                    logger.warning(f"Timeout reached after {timeout} seconds")
                    break
                
                logger.info(f"Waiting... Repos: {repo_pending}, Files: {file_pending}")
                time.sleep(10)
            
        except Exception as e:
            logger.error(f"Error waiting for completion: {e}")
            raise
    
    def finalize_job(self) -> str:
        """
        Finalize the job by creating the final analysis file.
        
        Returns:
            URL of the final analysis file
        """
        try:
            # Get all analyses for this job
            analyses = self.supabase_client.get_analyses_by_job(self.job_id)
            repositories = self.supabase_client.get_repositories_by_job(self.job_id)
            
            # Create final analysis data
            analysis_data = {
                "metadata": {
                    "job_id": self.job_id,
                    "generated": datetime.now().isoformat(),
                    "total_repositories": len(repositories),
                    "total_analyses": len(analyses),
                    "processing_configuration": {
                        "repomix_workers": self.config.REPOMIX_WORKERS,
                        "llm_workers": self.config.LLM_WORKERS,
                        "deployment_mode": self.config.DEPLOYMENT_MODE
                    }
                },
                "analyses": []
            }
            
            # Add analyses to final data
            for analysis in analyses:
                repo = next((r for r in repositories if r.id == analysis.repository_id), None)
                if repo:
                    analysis_data["analyses"].append({
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url
                        },
                        "processing": {
                            "worker_id": analysis.worker_id,
                            "processed_at": analysis.processed_at,
                            "analysis_length": analysis.analysis_length
                        },
                        "analysis": {
                            "content": analysis.analysis_content
                        }
                    })
            
            # Upload final analysis file
            analysis_url = self.storage_manager.upload_analysis_file(
                job_id=self.job_id,
                analysis_data=analysis_data,
                filename="final_analysis.json"
            )
            
            # Update job status
            self.supabase_client.update_job_status(
                self.job_id,
                "completed",
                analysis_file_url=analysis_url
            )
            
            logger.info(f"Job {self.job_id} finalized with analysis file: {analysis_url}")
            return analysis_url
            
        except Exception as e:
            logger.error(f"Failed to finalize job: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
    
    def stop_workers(self):
        """Stop all worker processes."""
        try:
            # Send poison pills to stop workers
            for _ in range(len(self.repomix_processes)):
                self.repo_queue.put(None)
            
            for _ in range(len(self.llm_processes)):
                self.file_queue.put(None)
            
            # Wait for processes to finish
            for p in self.repomix_processes + self.llm_processes:
                p.join(timeout=10)
                if p.is_alive():
                    p.terminate()
            
            logger.info("All workers stopped")
            
        except Exception as e:
            logger.error(f"Error stopping workers: {e}")
    
    def run(self, repositories: List[Dict[str, Any]], keywords: str, 
            min_stars: int, max_repos: int, **kwargs) -> str:
        """
        Run the complete cloud pipeline.
        
        Args:
            repositories: List of repository information
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            **kwargs: Additional job parameters
            
        Returns:
            URL of the final analysis file
        """
        try:
            # Create job
            self.create_job(keywords, min_stars, max_repos, **kwargs)
            
            # Add repositories
            self.add_repositories(repositories)
            
            # Start workers
            self.start_workers()
            
            # Wait for completion
            self.wait_for_completion(timeout=3600)  # 1 hour timeout
            
            # Finalize job
            analysis_url = self.finalize_job()
            
            return analysis_url
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
        finally:
            # Always stop workers
            self.stop_workers()
</file>

<file path="src/github_search.py">
"""
GitHub repository search functionality
"""

import requests
import time
from typing import List, Dict, Any


class GitHubSearcher:
    """GitHub API searcher for repositories"""
    
    def __init__(self, config):
        """Initialize with configuration"""
        self.config = config
        self.base_url = "https://api.github.com"
        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Repository-Research-Tool/1.0'
        }
        
        # Add authentication if token is available
        if config.GITHUB_TOKEN:
            self.headers['Authorization'] = f'token {config.GITHUB_TOKEN}'
    
    def search_repositories(self, keyword: str, min_stars: int = 30, max_repos: int = 20) -> List[Dict[str, Any]]:
        """
        Search for repositories by keyword

        Args:
            keyword: Search keyword
            min_stars: Minimum number of stars
            max_repos: Maximum number of repositories to return

        Returns:
            List of repository dictionaries
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"🔍 Searching GitHub for '{keyword}' repositories...")

        # Build search query
        query = f"{keyword} stars:>={min_stars}"
        logger.info(f"📝 Search query: '{query}'")

        params = {
            'q': query,
            'sort': 'stars',
            'order': 'desc',
            'per_page': min(max_repos, 100),  # GitHub API limit is 100 per page
            'page': 1
        }

        logger.info(f"🌐 API request parameters: {params}")

        try:
            # Make API request
            logger.info(f"📡 Making GitHub API request...")
            response = requests.get(
                f"{self.base_url}/search/repositories",
                headers=self.headers,
                params=params,
                timeout=30
            )

            logger.info(f"📊 API response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                repositories = data.get('items', [])
                total_count = data.get('total_count', 0)

                logger.info(f"🎯 GitHub reports {total_count} total repositories available for '{keyword}'")
                logger.info(f"📦 Retrieved {len(repositories)} repositories in this request")

                # Limit to requested number
                repositories = repositories[:max_repos]

                logger.info(f"✅ Returning {len(repositories)} repositories for '{keyword}'")
                
                # Extract relevant information
                result = []
                for repo in repositories:
                    repo_info = {
                        'full_name': repo['full_name'],
                        'name': repo['name'],
                        'url': repo['html_url'],  # Add 'url' key for service compatibility
                        'clone_url': repo['clone_url'],
                        'html_url': repo['html_url'],
                        'stars': repo['stargazers_count'],  # Add 'stars' key for service compatibility
                        'stargazers_count': repo['stargazers_count'],
                        'language': repo.get('language', 'Unknown'),
                        'description': repo.get('description', ''),
                        'updated_at': repo['updated_at']
                    }
                    result.append(repo_info)
                
                return result
                
            elif response.status_code == 403:
                print(f"❌ GitHub API rate limit exceeded for '{keyword}'")
                return []
            elif response.status_code == 422:
                print(f"❌ Invalid search query for '{keyword}': {query}")
                return []
            else:
                print(f"❌ GitHub API error for '{keyword}': {response.status_code}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error searching for '{keyword}': {e}")
            return []
        except Exception as e:
            print(f"❌ Unexpected error searching for '{keyword}': {e}")
            return []
    
    def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current GitHub API rate limit status"""
        try:
            response = requests.get(
                f"{self.base_url}/rate_limit",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'error': str(e)}


def search_repositories(keywords: str, min_stars: int = 30, max_repos: int = 20) -> List[Dict[str, Any]]:
    """
    Standalone function to search repositories.

    Args:
        keywords: Comma-separated keywords to search for
        min_stars: Minimum number of stars
        max_repos: Maximum number of repositories to return PER KEYWORD

    Returns:
        List of repository dictionaries (max_repos * number_of_keywords total)

    Example:
        keywords="proxy,vpn", max_repos=60 will return up to 120 repositories
        (60 for "proxy" + 60 for "vpn", minus any duplicates)
    """
    import logging
    from config import ScraperConfig

    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    config = ScraperConfig()
    searcher = GitHubSearcher(config)

    # Split keywords and search for each
    keyword_list = [k.strip() for k in keywords.split(',')]
    logger.info(f"🔍 GITHUB SEARCH FLOW: Starting search for {len(keyword_list)} keywords: {keyword_list}")
    logger.info(f"📊 GITHUB SEARCH FLOW: Search parameters: min_stars={min_stars}, max_repos={max_repos} PER KEYWORD")
    logger.info(f"🎯 GITHUB SEARCH FLOW: Expected maximum repositories: {len(keyword_list)} × {max_repos} = {len(keyword_list) * max_repos}")

    all_repositories = []

    for i, keyword in enumerate(keyword_list, 1):
        logger.info(f"🔎 GITHUB SEARCH FLOW: [{i}/{len(keyword_list)}] Processing keyword: '{keyword}'")
        logger.info(f"🎯 GITHUB SEARCH FLOW: Target for '{keyword}': {max_repos} repositories")

        # Each keyword gets exactly max_repos repositories
        # Use GitHub API limit (100) as the search limit, then trim to max_repos
        search_limit = min(100, max_repos)
        logger.info(f"🌐 GITHUB SEARCH FLOW: API search limit for '{keyword}': {search_limit}")

        repos = searcher.search_repositories(keyword, min_stars, search_limit)
        logger.info(f"📦 GITHUB SEARCH FLOW: Retrieved {len(repos)} repositories from GitHub API for '{keyword}'")

        # Limit to exactly max_repos for this keyword
        keyword_repos = repos[:max_repos]
        logger.info(f"✅ GITHUB SEARCH FLOW: Selected {len(keyword_repos)} repositories for '{keyword}' (max: {max_repos})")

        # Add keyword info to each repo for tracking
        for repo in keyword_repos:
            repo['search_keyword'] = keyword

        all_repositories.extend(keyword_repos)
        logger.info(f"📈 GITHUB SEARCH FLOW: Total repositories accumulated: {len(all_repositories)}")
        logger.info(f"📊 GITHUB SEARCH FLOW: Progress: {i}/{len(keyword_list)} keywords processed")

    logger.info(f"🔄 GITHUB SEARCH FLOW: Starting deduplication process...")
    logger.info(f"📊 GITHUB SEARCH FLOW: Total repositories before deduplication: {len(all_repositories)}")

    # Remove duplicates based on URL
    seen_urls = set()
    unique_repos = []
    duplicate_count = 0

    for repo in all_repositories:
        if repo['url'] not in seen_urls:
            seen_urls.add(repo['url'])
            unique_repos.append(repo)
        else:
            duplicate_count += 1
            logger.debug(f"🔄 GITHUB SEARCH FLOW: Duplicate found: {repo['url']} (keyword: {repo.get('search_keyword', 'unknown')})")

    logger.info(f"🗑️ GITHUB SEARCH FLOW: Removed {duplicate_count} duplicate repositories")
    logger.info(f"✨ GITHUB SEARCH FLOW: Unique repositories found: {len(unique_repos)}")

    # Sort by stars (descending) to get the best repositories
    unique_repos.sort(key=lambda x: x.get('stars', 0), reverse=True)
    logger.info(f"⭐ GITHUB SEARCH FLOW: Repositories sorted by stars (highest first)")

    # NO FINAL LIMITING - we want all unique repos from all keywords
    final_repos = unique_repos

    logger.info(f"🎯 GITHUB SEARCH FLOW: Final result: {len(final_repos)} repositories selected")
    logger.info(f"📊 GITHUB SEARCH FLOW: Expected maximum: {len(keyword_list)} keywords × {max_repos} repos = {len(keyword_list) * max_repos} repos")
    logger.info(f"✅ GITHUB SEARCH FLOW: Per-keyword logic working correctly: {len(final_repos)} ≤ {len(keyword_list) * max_repos}")

    # Log summary by keyword
    keyword_summary = {}
    for repo in final_repos:
        keyword = repo.get('search_keyword', 'unknown')
        keyword_summary[keyword] = keyword_summary.get(keyword, 0) + 1

    logger.info("📋 Final selection by keyword:")
    for keyword, count in keyword_summary.items():
        logger.info(f"   '{keyword}': {count} repositories")

    return final_repos
</file>

<file path="src/llm_rate_limiter.py">
import time
import uuid
import redis
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class LLMRateLimiter:
    """
    A distributed rate limiter for LLM API calls using Redis sliding window log.
    Enforces rate limits globally across all Cloud Run instances.
    Uses Redis Sorted Sets for efficient distributed rate limiting.
    """

    def __init__(self, redis_client: Optional[redis.Redis] = None,
                 rate_limit_mb_per_min: float = 12.0,
                 rate_limit_requests_per_min: int = 1000,
                 window_size_seconds: int = 60):
        """
        Initialize the distributed rate limiter.

        Args:
            redis_client: Redis client instance for distributed state
            rate_limit_mb_per_min: Maximum MB that can be processed per minute
            rate_limit_requests_per_min: Maximum requests per minute
            window_size_seconds: Time window for rate limiting (default: 60 seconds)
        """
        self.redis_client = redis_client
        self.rate_limit_mb_per_min = rate_limit_mb_per_min
        self.rate_limit_requests_per_min = rate_limit_requests_per_min
        self.window_size_seconds = window_size_seconds

        # Redis keys for rate limiting
        self.mb_log_key = "llm_rate_limit_mb_log"
        self.request_log_key = "llm_rate_limit_request_log"

        # Fallback to file-based for local development
        self.use_redis = redis_client is not None
        if not self.use_redis:
            logger.warning("Redis client not provided, falling back to local file-based rate limiting")
            self._init_file_fallback()

    def _init_file_fallback(self):
        """Initialize file-based fallback for local development."""
        from pathlib import Path
        import json

        self.state_file = Path('temp_rate_limiter_state.json')

        # Initialize state file if it doesn't exist
        if not self.state_file.exists():
            self._write_state({
                'total_mb': 0.0,
                'last_reset': time.time()
            })

    def _read_state(self):
        """Read state from file with error handling (fallback mode)"""
        import json
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass

        # Return default state if file doesn't exist or is corrupted
        return {
            'total_mb': 0.0,
            'last_reset': time.time()
        }

    def _write_state(self, state):
        """Write state to file with error handling (fallback mode)"""
        import json
        try:
            # Ensure directory exists
            self.state_file.parent.mkdir(parents=True, exist_ok=True)

            # Write atomically by writing to temp file first
            temp_file = self.state_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(state, f)

            # Atomic rename
            temp_file.replace(self.state_file)
        except IOError:
            # If file operations fail, continue without crashing
            pass

    def acquire_slot(self, file_size_mb: float = 0.0) -> bool:
        """
        Acquire a slot for processing a file of given size.
        Uses Redis sliding window log for distributed rate limiting.

        Args:
            file_size_mb: Size of the file to process in MB

        Returns:
            bool: True if slot acquired, False if rate limited
        """
        if self.use_redis:
            return self._acquire_slot_redis(file_size_mb)
        else:
            return self._acquire_slot_fallback(file_size_mb)

    def _acquire_slot_redis(self, file_size_mb: float) -> bool:
        """Redis-based sliding window rate limiting."""
        try:
            current_time = time.time()
            window_start = current_time - self.window_size_seconds

            # Generate unique request ID
            request_id = f"{current_time}:{uuid.uuid4()}"

            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()

            # Remove old entries from both logs
            pipe.zremrangebyscore(self.mb_log_key, 0, window_start)
            pipe.zremrangebyscore(self.request_log_key, 0, window_start)

            # Get current usage
            pipe.zcard(self.request_log_key)  # Request count

            # Execute pipeline to get current state
            results = pipe.execute()
            current_requests = results[2]

            # Check request rate limit
            if current_requests >= self.rate_limit_requests_per_min:
                logger.debug(f"Request rate limit exceeded: {current_requests}/{self.rate_limit_requests_per_min}")
                return False

            # Check MB rate limit if file size specified
            if file_size_mb > 0:
                # Get current MB usage by summing scores in the sorted set
                mb_entries = self.redis_client.zrangebyscore(
                    self.mb_log_key, window_start, current_time, withscores=True
                )
                current_mb = sum(score for _, score in mb_entries)

                if current_mb + file_size_mb > self.rate_limit_mb_per_min:
                    logger.debug(f"MB rate limit exceeded: {current_mb + file_size_mb}/{self.rate_limit_mb_per_min}")
                    return False

                # Add to MB log with file size as score
                self.redis_client.zadd(self.mb_log_key, {request_id: file_size_mb})

            # Add to request log
            self.redis_client.zadd(self.request_log_key, {request_id: current_time})

            # Set expiration on keys to prevent memory leaks
            self.redis_client.expire(self.mb_log_key, self.window_size_seconds * 2)
            self.redis_client.expire(self.request_log_key, self.window_size_seconds * 2)

            return True

        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Fallback to allowing the request if Redis fails
            return True

    def _acquire_slot_fallback(self, file_size_mb: float) -> bool:
        """File-based fallback rate limiting for local development."""
        current_time = time.time()

        # Read current state
        state = self._read_state()

        # Check if a minute has passed since last reset
        if current_time - state['last_reset'] >= self.window_size_seconds:
            state['total_mb'] = 0.0
            state['last_reset'] = current_time

        # Check if adding this file would exceed the limit
        if state['total_mb'] + file_size_mb <= self.rate_limit_mb_per_min:
            # We can process this file
            state['total_mb'] += file_size_mb
            self._write_state(state)
            return True

        return False

    def wait_for_slot(self, file_size_mb: float = 0.0, max_wait_seconds: int = 300):
        """
        Wait for a slot to become available, with timeout.

        Args:
            file_size_mb: Size of the file to process in MB
            max_wait_seconds: Maximum time to wait for a slot
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_seconds:
            if self.acquire_slot(file_size_mb):
                return True

            # Sleep with exponential backoff, max 5 seconds
            wait_time = min(1.0 + (time.time() - start_time) * 0.1, 5.0)
            time.sleep(wait_time)

        raise TimeoutError(f"Could not acquire rate limit slot within {max_wait_seconds} seconds")

    def get_current_usage(self) -> dict:
        """Get current rate limit usage statistics."""
        if self.use_redis:
            try:
                current_time = time.time()
                window_start = current_time - self.window_size_seconds

                # Clean old entries and get current counts
                self.redis_client.zremrangebyscore(self.mb_log_key, 0, window_start)
                self.redis_client.zremrangebyscore(self.request_log_key, 0, window_start)

                # Get current usage
                current_requests = self.redis_client.zcard(self.request_log_key)

                # Calculate current MB usage
                mb_entries = self.redis_client.zrangebyscore(
                    self.mb_log_key, window_start, current_time, withscores=True
                )
                current_mb = sum(score for _, score in mb_entries)

                return {
                    "requests": current_requests,
                    "requests_limit": self.rate_limit_requests_per_min,
                    "mb_used": round(current_mb, 2),
                    "mb_limit": self.rate_limit_mb_per_min,
                    "window_seconds": self.window_size_seconds,
                    "requests_remaining": max(0, self.rate_limit_requests_per_min - current_requests),
                    "mb_remaining": max(0, self.rate_limit_mb_per_min - current_mb)
                }
            except Exception as e:
                logger.error(f"Error getting usage stats: {e}")
                return {"error": str(e)}
        else:
            # Fallback mode
            state = self._read_state()
            current_time = time.time()

            # Reset if window expired
            if current_time - state['last_reset'] >= self.window_size_seconds:
                current_mb = 0.0
            else:
                current_mb = state['total_mb']

            return {
                "mb_used": round(current_mb, 2),
                "mb_limit": self.rate_limit_mb_per_min,
                "mb_remaining": max(0, self.rate_limit_mb_per_min - current_mb),
                "mode": "fallback"
            }

    def reset_limits(self):
        """Reset all rate limits (for testing purposes)."""
        if self.use_redis:
            try:
                self.redis_client.delete(self.mb_log_key, self.request_log_key)
                logger.info("Redis rate limit logs cleared")
            except Exception as e:
                logger.error(f"Error resetting Redis limits: {e}")
        else:
            # Reset file-based state
            self._write_state({
                'total_mb': 0.0,
                'last_reset': time.time()
            })

    def cleanup(self):
        """Clean up temporary files and optionally Redis keys."""
        if not self.use_redis:
            try:
                if hasattr(self, 'state_file') and self.state_file.exists():
                    self.state_file.unlink()
                    logger.debug("Cleaned up rate limiter state file")
            except Exception as e:
                logger.debug(f"Error cleaning up state file: {e}")
        # Note: We don't clean up Redis keys as they may be shared across instances
</file>

<file path="src/utils.py">
import os


def get_file_size_mb(file_path):
    """
    Calculate file size in megabytes.

    Args:
        file_path (str): Path to the file

    Returns:
        float: File size in megabytes
    """
    size_bytes = os.path.getsize(file_path)
    size_mb = size_bytes / (1024 * 1024)
    return size_mb


def chunk_and_retain_file(file_path, max_size_mb=3, max_chunks=3):
    """
    Chunk a file into smaller parts if it exceeds max_size_mb.
    Only retain the first max_chunks parts and delete the original file.

    Args:
        file_path (str): Path to the file to chunk
        max_size_mb (int): Maximum size in MB before chunking (default: 3)
        max_chunks (int): Maximum number of chunks to retain (default: 3)

    Returns:
        list: List of chunk file paths if file was chunked, or [file_path] if not chunked
    """
    import logging
    logger = logging.getLogger(__name__)

    # Check if file exists
    if not os.path.exists(file_path):
        logger.warning(f"📏 CHUNKING FLOW: File not found: {file_path}")
        return []

    # Check if file needs chunking
    file_size_mb = get_file_size_mb(file_path)
    logger.info(f"📏 CHUNKING FLOW: File size check: {os.path.basename(file_path)} = {file_size_mb:.2f} MB")

    if file_size_mb <= max_size_mb:
        logger.info(f"✅ CHUNKING FLOW: File within limit ({file_size_mb:.2f} MB ≤ {max_size_mb} MB) - no chunking needed")
        return [file_path]

    logger.info(f"🔄 CHUNKING FLOW: File exceeds limit ({file_size_mb:.2f} MB > {max_size_mb} MB) - starting chunking")
    logger.info(f"📊 CHUNKING FLOW: Chunking parameters: {max_size_mb} MB per chunk, max {max_chunks} chunks")

    # Calculate chunk size in bytes
    chunk_size_bytes = max_size_mb * 1024 * 1024

    # Read file in binary mode and create chunks
    chunk_paths = []
    base_name = file_path

    try:
        with open(file_path, 'rb') as f:
            chunk_num = 0
            while True:
                chunk_data = f.read(chunk_size_bytes)
                if not chunk_data:
                    break

                # Only create chunks up to max_chunks
                if chunk_num < max_chunks:
                    chunk_path = f"{base_name}.chunk_{chunk_num}"
                    logger.info(f"📝 CHUNKING FLOW: Creating chunk {chunk_num + 1}/{max_chunks}: {os.path.basename(chunk_path)}")

                    with open(chunk_path, 'w', encoding='utf-8') as chunk_file:
                        # Convert binary data to string for UTF-8 encoding
                        try:
                            chunk_text = chunk_data.decode('utf-8')
                        except UnicodeDecodeError:
                            # If binary data can't be decoded, use latin-1 as fallback
                            chunk_text = chunk_data.decode('latin-1')
                        chunk_file.write(chunk_text)

                    chunk_size_actual = len(chunk_data) / (1024 * 1024)
                    logger.info(f"✅ CHUNKING FLOW: Chunk {chunk_num + 1} created: {chunk_size_actual:.2f} MB")
                    chunk_paths.append(chunk_path)
                else:
                    logger.info(f"⏭️ CHUNKING FLOW: Skipping chunk {chunk_num + 1} (exceeds max_chunks limit of {max_chunks})")

                chunk_num += 1

        # Delete the original file
        logger.info(f"🗑️ CHUNKING FLOW: Deleting original file: {os.path.basename(file_path)}")
        os.remove(file_path)

        logger.info(f"🎯 CHUNKING FLOW: Chunking complete: {len(chunk_paths)} chunks created from {file_size_mb:.2f} MB file")
        logger.info(f"📋 CHUNKING FLOW: Chunk files: {[os.path.basename(f) for f in chunk_paths]}")

        return chunk_paths

    except FileNotFoundError:
        # If original file doesn't exist, return empty list
        return []
    except Exception as e:
        # If any error occurs, clean up partial chunks and re-raise
        for chunk_path in chunk_paths:
            try:
                os.remove(chunk_path)
            except FileNotFoundError:
                pass
        raise e
</file>

<file path="src/config.py">
import os
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    # Look for .env file in project root
    env_path = Path(__file__).parent.parent / '.env'
    load_dotenv(env_path)
    print(f"Loaded environment variables from {env_path}")
except ImportError:
    print("python-dotenv not installed, using system environment variables")
except Exception as e:
    print(f"Could not load .env file: {e}")

class ScraperConfig:
    # GitHub Configuration
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
    MAX_REPOS_PER_KEYWORD = int(os.getenv('MAX_REPOS_PER_KEYWORD', '20'))
    MIN_STARS = int(os.getenv('MIN_STARS', '30'))

    # Repomix Configuration
    REPOMIX_WORKERS = int(os.getenv('REPOMIX_WORKERS', '15'))
    FILE_INCLUDES = "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"

    # Chunking Configuration
    MAX_FILE_SIZE_MB = 3
    MAX_CHUNKS_TO_RETAIN = 3

    # LLM Configuration
    LLM_API_KEY = os.getenv('LLM_API_KEY')
    LLM_BASE_URL = os.getenv('LLM_BASE_URL', 'https://generativelanguage.googleapis.com/v1beta')
    LLM_MODEL = os.getenv('LLM_MODEL', 'gemini-2.0-flash')
    LLM_WORKERS = int(os.getenv('LLM_WORKERS', '4'))
    LLM_RATE_LIMIT_MB_PER_MIN = 12

    # Output Configuration
    OUTPUT_BASE = os.getenv('OUTPUT_BASE', 'output')

    # Cloud Configuration
    DEPLOYMENT_MODE = os.getenv('DEPLOYMENT_MODE', 'local')  # local, cloud
    USE_CLOUD_SERVICES = DEPLOYMENT_MODE == 'cloud'

    # Redis Configuration
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    REDIS_QUEUE_TIMEOUT = int(os.getenv('REDIS_QUEUE_TIMEOUT', '300'))

    # Supabase Configuration
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY')  # Service role key
    SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')  # Anonymous key

    # Storage Configuration
    STORAGE_BUCKET_REPOMIXES = os.getenv('STORAGE_BUCKET_REPOMIXES', 'repomixes')
    STORAGE_BUCKET_ANALYSES = os.getenv('STORAGE_BUCKET_ANALYSES', 'analyses')
    STORAGE_BUCKET_LOGS = os.getenv('STORAGE_BUCKET_LOGS', 'logs')

    # Enhanced LLM Configuration
    LLM_TIMEOUT = int(os.getenv('LLM_TIMEOUT', '90'))
    LLM_MAX_OUTPUT_TOKENS = int(os.getenv('LLM_MAX_OUTPUT_TOKENS', '8192'))

    # Custom prompt override
    CUSTOM_LLM_PROMPT = os.getenv('CUSTOM_LLM_PROMPT')

    # Default comprehensive analysis prompt
    DEFAULT_LLM_PROMPT = """Analyze this repository comprehensively and provide a detailed summary covering:

1. **Main Purpose and Functionality**: What does this repository do? What problem does it solve?

2. **Key Technologies and Frameworks Used**: List and briefly describe the main technologies, frameworks, libraries, and tools used.

3. **Architecture Overview**: Describe the overall architecture, design patterns, and structure of the codebase.

4. **Notable Features or Patterns**: Highlight interesting implementation details, design patterns, or unique approaches used.

5. **Potential Use Cases**: Describe scenarios where this repository could be useful or applied.

Please be comprehensive and specific, focusing on the actual implementation details and functionality rather than generic descriptions."""
</file>

<file path="src/pipeline.py">
import multiprocessing
import subprocess
import os
import tempfile
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import json
import time
import logging
import shutil
import random
from pathlib import Path
from datetime import datetime

# Sentry integration
from sentry_config import trace_api_call, log_worker_start, log_rate_limit_event
from monitoring import RepositoryMonitor
import sentry_sdk

from config import ScraperConfig
from utils import get_file_size_mb, chunk_and_retain_file
from llm_rate_limiter import LLMRateLimiter


def repomix_worker(repo_queue, file_queue, config, output_dir, monitor=None):
    """
    Worker function to process repositories using repomix.

    Args:
        repo_queue: Queue containing repository information to process
        file_queue: Queue to add processed files to
        config: Configuration object with repomix settings
        output_dir: Timestamped output directory path
        monitor: RepositoryMonitor instance for logging
    """
    # Initialize Sentry context for this worker
    worker_id = os.getpid()
    log_worker_start("repomix", worker_id)

    while True:
        try:
            # Get repository from queue
            repo_info = repo_queue.get()
            if repo_info is None:  # Poison pill to stop worker
                repo_queue.task_done()
                break

            repo_url = repo_info.get('html_url', repo_info.get('clone_url', ''))
            repo_name = repo_info.get('full_name', 'unknown')

            start_time = time.time()
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] REPOMIX Worker {worker_id}: Starting {repo_name}")

            # Log to monitor
            if monitor:
                monitor.log_repository_start(repo_name, worker_id, "repomix")

            # Use the timestamped repomixes directory
            repomixes_dir = Path(output_dir) / 'repomixes'
            repomixes_dir.mkdir(parents=True, exist_ok=True)

            safe_repo_name = repo_name.replace('/', '_')
            output_file = repomixes_dir / f"{safe_repo_name}.md"

            # Find npx executable
            npx_path = shutil.which('npx')
            if not npx_path:
                print(f"❌ npx not found in PATH")
                continue

            # Run repomix command
            cmd = [
                npx_path, 'repomix', '--remote', repo_url,
                '--include', config.FILE_INCLUDES,
                '--output', str(output_file)
            ]

            try:
                    repomix_start = time.time()
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] REPOMIX Worker {worker_id}: Running repomix for {repo_name}")

                    # Ensure PATH is available for subprocess with proper encoding
                    env = os.environ.copy()
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=600, env=env, encoding='utf-8', errors='replace')

                    repomix_end = time.time()
                    repomix_duration = repomix_end - repomix_start
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]

                    if result.returncode == 0 and os.path.exists(output_file):
                        # Check file size and chunk if necessary
                        file_size_mb = get_file_size_mb(output_file)
                        
                        if file_size_mb > config.MAX_FILE_SIZE_MB:
                            # Chunk the file
                            chunk_files = chunk_and_retain_file(
                                output_file, 
                                config.MAX_FILE_SIZE_MB, 
                                config.MAX_CHUNKS_TO_RETAIN
                            )
                            
                            # Add each chunk to the file queue
                            for chunk_file in chunk_files:
                                file_queue.put({
                                    'file_path': chunk_file,
                                    'repo_name': repo_name,
                                    'is_chunk': True,
                                    'original_size_mb': file_size_mb
                                })
                        else:
                            # Add the original file to the queue (keep in repomixes directory)
                            file_queue.put({
                                'file_path': str(output_file),
                                'repo_name': repo_name,
                                'is_chunk': False,
                                'original_size_mb': file_size_mb
                            })
                        
                        total_duration = time.time() - start_time
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] REPOMIX Worker {worker_id}: ✅ Completed {repo_name} ({file_size_mb:.2f}MB) - Repomix: {repomix_duration:.1f}s, Total: {total_duration:.1f}s")

                        # Log success to monitor
                        if monitor:
                            monitor.log_repository_success(repo_name, worker_id, "repomix", total_duration, file_size_mb)
                    else:
                        total_duration = time.time() - start_time
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        error_msg = result.stderr[:100] if result.stderr else "Unknown error"
                        print(f"[{timestamp}] REPOMIX Worker {worker_id}: ❌ Failed {repo_name} - {error_msg} - Duration: {total_duration:.1f}s")

                        # Log failure to monitor
                        if monitor:
                            monitor.log_repository_failure(repo_name, worker_id, "repomix", error_msg, total_duration)

            except subprocess.TimeoutExpired:
                print(f"Timeout processing {repo_name}")
            except Exception as e:
                print(f"Error processing {repo_name}: {e}")
            
            repo_queue.task_done()
            
        except Exception as e:
            print(f"Repomix worker error: {e}")
            repo_queue.task_done()


def create_robust_session():
    """Create a requests session with connection pooling and retry logic."""
    session = requests.Session()

    # Configure retry strategy for connection issues
    retry_strategy = Retry(
        total=5,  # Total number of retries
        backoff_factor=2,  # Exponential backoff: 2, 4, 8, 16, 32 seconds
        status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry
        allowed_methods=["POST"],  # Only retry POST requests
        raise_on_status=False  # Don't raise exception on retry exhaustion
    )

    # Configure HTTP adapter with connection pooling
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,  # Number of connection pools
        pool_maxsize=20,  # Max connections per pool
        pool_block=False  # Don't block when pool is full
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session

def llm_worker(file_queue, rate_limiter, config, analysis_file, monitor=None):
    """
    Worker function to analyze files using LLM API.

    Args:
        file_queue: Queue containing files to analyze
        rate_limiter: Rate limiter for LLM API calls
        config: Configuration object with LLM settings
        analysis_file: Path to the single analysis file to append to
        monitor: RepositoryMonitor instance for logging
    """
    # Initialize Sentry context for this worker
    worker_id = os.getpid()
    log_worker_start("llm", worker_id)

    # Create robust session for this worker
    session = create_robust_session()

    while True:
        try:
            # Get file from queue
            file_info = file_queue.get()
            if file_info is None:  # Poison pill to stop worker
                file_queue.task_done()
                break

            file_path = file_info['file_path']
            repo_name = file_info['repo_name']
            file_size_mb = get_file_size_mb(file_path)

            start_time = time.time()
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] LLM Worker {worker_id}: Starting analysis of {repo_name} ({file_size_mb:.2f}MB)")

            # Acquire rate limiting slot
            rate_limit_start = time.time()
            try:
                # Use wait_for_slot for blocking behavior (maintains backward compatibility)
                rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300)
                rate_limit_duration = time.time() - rate_limit_start

                if rate_limit_duration > 1.0:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] LLM Worker {worker_id}: Rate limit wait: {rate_limit_duration:.1f}s for {repo_name}")
            except TimeoutError:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                print(f"[{timestamp}] LLM Worker {worker_id}: ❌ Rate limit timeout for {repo_name}, skipping")
                continue
            
            try:
                # Read file content
                logger.info(f"📖 LLM FLOW: Reading file content: {os.path.basename(file_path)}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                content_size = len(content)
                logger.info(f"📏 LLM FLOW: File content size: {content_size:,} characters")

                # Prepare LLM API request
                logger.info(f"🤖 LLM FLOW: Preparing Gemini API request for {repo_name}")
                prompt = f"""Analyze this repository code and provide a comprehensive summary:

Repository: {repo_name}
File size: {file_size_mb:.2f}MB

Please provide:
1. Main purpose and functionality
2. Key technologies and frameworks used
3. Architecture overview
4. Notable features or patterns
5. Potential use cases

Code content:
{content[:50000]}  # Limit content to avoid token limits
"""
                
                # Make LLM API call
                api_start = time.time()
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                print(f"[{timestamp}] LLM Worker {worker_id}: Making API call for {repo_name}")

                headers = {
                    'Content-Type': 'application/json'
                }

                # Gemini API format
                data = {
                    'contents': [{
                        'parts': [{'text': prompt}]
                    }],
                    'generationConfig': {
                        'maxOutputTokens': 4000,
                        'temperature': 0.3
                    }
                }

                # Enhanced retry mechanism: UNLIMITED retries for all errors
                response = None
                rate_limit_retries = 0
                timeout_retries = 0
                network_error_retries = 0
                connection_error_retries = 0
                other_error_retries = 0

                logger.info(f"🌐 LLM FLOW: Starting Gemini API call for {repo_name}")

                with trace_api_call("gemini_analysis", repo_name) as tracer:
                    tracer.add_breadcrumb(f"Starting API call for {repo_name}")

                    # UNLIMITED retries - keep trying until success
                    logger.info(f"🔄 LLM FLOW: Entering retry loop for API call")
                    while True:
                        try:
                            # DEBUG: Log the exact request details
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Making request to: {config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - API Key: {config.LLM_API_KEY[:20]}...")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Prompt size: {len(prompt):,} chars")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Timeout: 30s")

                            response = requests.post(
                                f"{config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent?key={config.LLM_API_KEY}",
                                headers=headers,
                                json=data,
                                timeout=30  # Simple 30-second timeout like the working test
                            )

                            # DEBUG: Log response received
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Response received: {response.status_code}")

                            if response.status_code == 200:
                                tracer.set_status(200)
                                tracer.add_breadcrumb("API call successful")
                                break  # Success

                            elif response.status_code == 429:  # Rate limit - stall and retry
                                rate_limit_retries += 1
                                retry_delay = 5  # Always 5 seconds for rate limits
                                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                                print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Rate limited, stalling for {retry_delay}s (stall #{rate_limit_retries})")

                                # Log to Sentry
                                log_rate_limit_event(worker_id, repo_name, retry_delay, rate_limit_retries)
                                tracer.add_breadcrumb(f"Rate limited - stall #{rate_limit_retries}")

                                time.sleep(retry_delay)
                                continue  # Retry immediately

                            else:  # Other API error - stall and retry
                                other_error_retries += 1
                                error_msg = f"Status {response.status_code}"
                                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                                print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ API error {response.status_code}, retrying in 10s (error #{other_error_retries})")

                                tracer.set_status(response.status_code, error_msg)
                                tracer.add_breadcrumb(f"API error {response.status_code} - error #{other_error_retries}")

                                time.sleep(10)  # 10 second delay for API errors
                                continue  # Retry immediately

                        except requests.exceptions.Timeout:
                            timeout_retries += 1
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: ⏰ Timeout, retrying in 15s (timeout #{timeout_retries})")

                            tracer.add_breadcrumb(f"Timeout - retry #{timeout_retries}")

                            time.sleep(15)  # 15 second delay for timeouts
                            continue  # Retry immediately

                        except requests.exceptions.ConnectionError as e:
                            # Handle connection reset errors specifically
                            connection_error_retries += 1
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]

                            if "10054" in str(e) or "forcibly closed" in str(e).lower():
                                print(f"[{timestamp}] LLM Worker {worker_id}: 🔌 Connection reset by remote host, retrying in 30s (reset #{connection_error_retries})")
                                tracer.add_breadcrumb(f"Connection reset - retry #{connection_error_retries}")
                                time.sleep(30)  # Longer delay for connection resets
                            else:
                                print(f"[{timestamp}] LLM Worker {worker_id}: 🌐 Connection error: {str(e)[:100]}, retrying in 25s (conn #{connection_error_retries})")
                                tracer.add_breadcrumb(f"Connection error - retry #{connection_error_retries}: {str(e)[:100]}")
                                time.sleep(25)  # 25 second delay for connection errors

                            continue  # Retry immediately

                        except requests.exceptions.RequestException as e:
                            network_error_retries += 1
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🌐 Network error: {str(e)[:100]}, retrying in 20s (network #{network_error_retries})")

                            tracer.add_breadcrumb(f"Network error - retry #{network_error_retries}: {str(e)[:100]}")

                            time.sleep(20)  # 20 second delay for network errors
                            continue  # Retry immediately

                    # If we get here, we have a successful response
                    # Continue to process the response (don't break!)

                api_duration = time.time() - api_start

                if response and response.status_code == 200:
                    try:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Processing successful response...")

                        result = response.json()
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - JSON parsed successfully")

                        # Gemini API response format
                        candidates = result.get('candidates', [])
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Found {len(candidates)} candidates")

                        if candidates and 'content' in candidates[0]:
                            analysis = candidates[0]['content']['parts'][0]['text']
                            analysis_length = len(analysis)
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Analysis extracted: {analysis_length} chars")
                            logger.info(f"✅ LLM FLOW: Analysis completed for {repo_name}: {analysis_length} characters")
                            logger.info(f"📊 LLM FLOW: Analysis quality: {'Good' if analysis_length > 500 else 'Short'} length")
                        else:
                            analysis = 'No analysis content available in response'
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Empty response for {repo_name}")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Response structure: {json.dumps(result, indent=2)[:500]}...")
                    except (KeyError, IndexError, TypeError) as e:
                        analysis = f'Error parsing response: {str(e)}'
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] LLM Worker {worker_id}: ❌ Parse error for {repo_name}: {str(e)}")
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Raw response: {response.text[:500]}...")
                    
                    # Append analysis to JSON file with retry
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Preparing analysis JSON...")

                    # Create analysis object
                    analysis_object = {
                        "repository": {
                            "name": repo_name,
                            "url": f"https://github.com/{repo_name}",
                            "file_size_mb": round(file_size_mb, 2),
                            "repomix_file": f"repomixes/{repo_name.replace('/', '_')}.md"
                        },
                        "processing": {
                            "processed_at": time.strftime('%Y-%m-%d %H:%M:%S'),
                            "worker_id": worker_id,
                            "analysis_length": len(analysis)
                        },
                        "analysis": {
                            "content": analysis.strip(),
                            "format": "structured_text"
                        }
                    }

                    print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Analysis JSON prepared: {len(str(analysis_object))} chars")

                    # Retry mechanism for JSON append
                    max_append_retries = 3
                    append_delay = 2

                    for append_attempt in range(max_append_retries):
                        try:
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Writing to JSON file: {analysis_file}")

                            # Read existing JSON, append new analysis, write back with file locking
                            import json
                            try:
                                import fcntl  # Unix-only, not available on Windows
                            except ImportError:
                                fcntl = None  # Windows compatibility
                            import tempfile
                            # import os  # REMOVED: Use module-level import to avoid scoping conflict

                            # Use atomic write with temporary file to prevent corruption
                            temp_file = None
                            try:
                                with open(analysis_file, 'r', encoding='utf-8') as f:
                                    data = json.load(f)

                                data["analyses"].append(analysis_object)

                                # Write to temporary file first, then atomic rename
                                temp_file = analysis_file.with_suffix('.tmp')
                                with open(temp_file, 'w', encoding='utf-8') as f:
                                    json.dump(data, f, indent=2, ensure_ascii=False)

                                # Atomic rename (Windows compatible)
                                if os.name == 'nt':  # Windows
                                    if analysis_file.exists():
                                        analysis_file.unlink()
                                    temp_file.rename(analysis_file)
                                else:  # Unix/Linux
                                    temp_file.rename(analysis_file)

                            except Exception as e:
                                # Clean up temp file if it exists
                                if temp_file and temp_file.exists():
                                    temp_file.unlink()
                                raise e

                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - JSON write successful!")
                            break  # Success, exit retry loop
                        except (IOError, OSError, json.JSONDecodeError) as e:
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            if append_attempt < max_append_retries - 1:
                                print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ JSON write failed for {repo_name}, retrying in {append_delay}s (attempt {append_attempt + 1}/{max_append_retries})")
                                time.sleep(append_delay)
                            else:
                                print(f"[{timestamp}] LLM Worker {worker_id}: ❌ JSON write failed permanently for {repo_name}: {e}")
                    
                    total_duration = time.time() - start_time
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Analysis complete, marking task done...")
                    print(f"[{timestamp}] LLM Worker {worker_id}: ✅ Completed {repo_name} - API: {api_duration:.1f}s, Total: {total_duration:.1f}s")
                else:
                    total_duration = time.time() - start_time
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    error_msg = f"Status: {response.status_code if response else 'No response'}"
                    if response:
                        try:
                            error_detail = response.json().get('error', {}).get('message', 'Unknown error')
                            error_msg += f", Error: {error_detail[:100]}"
                        except:
                            error_msg += f", Response: {response.text[:100]}"
                    print(f"[{timestamp}] LLM Worker {worker_id}: ❌ API Error {repo_name}: {error_msg} - Duration: {total_duration:.1f}s")

                    # Log failed analysis to JSON file with retry
                    try:
                        failed_analysis_object = {
                            "repository": {
                                "name": repo_name,
                                "url": f"https://github.com/{repo_name}",
                                "file_size_mb": round(file_size_mb, 2),
                                "repomix_file": f"repomixes/{repo_name.replace('/', '_')}.md"
                            },
                            "processing": {
                                "failed_at": time.strftime('%Y-%m-%d %H:%M:%S'),
                                "worker_id": worker_id,
                                "error_message": error_msg,
                                "duration": round(total_duration, 1)
                            },
                            "analysis": {
                                "content": "ANALYSIS FAILED",
                                "format": "error",
                                "status": "failed"
                            }
                        }

                        # Retry mechanism for failed analysis logging
                        for append_attempt in range(3):
                            try:
                                import json
                                import tempfile
                                # import os  # REMOVED: Use module-level import to avoid scoping conflict

                                # Use atomic write with temporary file
                                temp_file = None
                                try:
                                    with open(analysis_file, 'r', encoding='utf-8') as f:
                                        data = json.load(f)

                                    data["analyses"].append(failed_analysis_object)

                                    # Write to temporary file first, then atomic rename
                                    temp_file = analysis_file.with_suffix('.tmp')
                                    with open(temp_file, 'w', encoding='utf-8') as f:
                                        json.dump(data, f, indent=2, ensure_ascii=False)

                                    # Atomic rename (Windows compatible)
                                    if os.name == 'nt':  # Windows
                                        if analysis_file.exists():
                                            analysis_file.unlink()
                                        temp_file.rename(analysis_file)
                                    else:  # Unix/Linux
                                        temp_file.rename(analysis_file)

                                except Exception as e:
                                    # Clean up temp file if it exists
                                    if temp_file and temp_file.exists():
                                        temp_file.unlink()
                                    raise e
                                break
                            except (IOError, OSError, json.JSONDecodeError) as e:
                                if append_attempt < 2:
                                    time.sleep(2)
                                else:
                                    print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Failed to log error for {repo_name}: {e}")
                    except Exception as log_error:
                        print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Failed to log error for {repo_name}: {log_error}")
            
            except Exception as e:
                print(f"Error analyzing {repo_name}: {e}")
            
            finally:
                # Keep repomix files - don't delete them!
                # They are saved in repomixes/ directory for research purposes
                pass
            
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Calling task_done()...")
            file_queue.task_done()
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - task_done() completed successfully")

        except Exception as e:
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] LLM Worker {worker_id}: ❌ LLM worker error: {e}")
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Calling task_done() after error...")
            file_queue.task_done()
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - task_done() after error completed")


class RepositoryPipeline:
    """
    Main pipeline class to orchestrate repository processing.
    """
    
    def __init__(self, config=None, first_keyword=None):
        """
        Initialize the pipeline.

        Args:
            config: Configuration object (defaults to ScraperConfig)
            first_keyword: First keyword for directory naming (optional)
        """
        self.config = config or ScraperConfig()
        self.repo_queue = multiprocessing.JoinableQueue()
        self.file_queue = multiprocessing.JoinableQueue()
        # Initialize rate limiter with Redis if available
        redis_client = None
        if hasattr(self.config, 'REDIS_URL') and self.config.REDIS_URL:
            try:
                import redis
                redis_client = redis.from_url(self.config.REDIS_URL, decode_responses=True)
                redis_client.ping()  # Test connection
                print(f"✅ Connected to Redis for distributed rate limiting: {self.config.REDIS_URL}")
            except Exception as e:
                print(f"⚠️ Redis connection failed, using local rate limiting: {e}")
                redis_client = None

        self.rate_limiter = LLMRateLimiter(
            redis_client=redis_client,
            rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN
        )

        # Worker configuration
        self.repomix_workers = self.config.REPOMIX_WORKERS
        self.llm_workers = self.config.LLM_WORKERS
        self.rate_limit_mb_per_min = self.config.LLM_RATE_LIMIT_MB_PER_MIN

        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None

        # Create unique timestamped output directory with first keyword
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # Include microseconds for uniqueness

        if first_keyword:
            # Sanitize keyword for directory name
            safe_keyword = first_keyword.replace(' ', '-').replace('/', '-').replace('\\', '-')
            # Remove special characters and limit length
            safe_keyword = ''.join(c for c in safe_keyword if c.isalnum() or c in '-_')[:20]
            dir_name = f"{timestamp_str}_{safe_keyword}"
        else:
            dir_name = timestamp_str

        self.output_dir = Path(self.config.OUTPUT_BASE) / dir_name

        # Ensure directory doesn't exist (handle rare collision case)
        counter = 1
        original_output_dir = self.output_dir
        while self.output_dir.exists():
            self.output_dir = original_output_dir.parent / f"{dir_name}_{counter}"
            counter += 1

        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Create analysis file
        self.analysis_file = self.output_dir / "analysis.json"

        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
    
    def add_repositories(self, repositories):
        """
        Add repositories to the processing queue.
        
        Args:
            repositories: List of repository information dictionaries
        """
        for repo in repositories:
            self.repo_queue.put(repo)
    
    def start_workers(self):
        """Start all worker processes."""
        print(f"Starting {self.config.REPOMIX_WORKERS} repomix workers...")
        
        # Start repomix workers
        for i in range(self.config.REPOMIX_WORKERS):
            p = multiprocessing.Process(
                target=repomix_worker,
                args=(self.repo_queue, self.file_queue, self.config, self.output_dir, self.monitor)
            )
            p.start()
            self.repomix_processes.append(p)
        
        print(f"Starting {self.config.LLM_WORKERS} LLM workers...")
        
        # Start LLM workers
        for i in range(self.config.LLM_WORKERS):
            p = multiprocessing.Process(
                target=llm_worker,
                args=(self.file_queue, self.rate_limiter, self.config, self.analysis_file, self.monitor)
            )
            p.start()
            self.llm_processes.append(p)
    
    def stop_workers(self):
        """Stop all worker processes gracefully."""
        print("Stopping workers...")

        try:
            # Send poison pills to stop repomix workers
            for _ in self.repomix_processes:
                try:
                    self.repo_queue.put(None, timeout=1)
                except:
                    pass  # Queue might be full or closed

            # Wait for repomix workers to finish gracefully
            for p in self.repomix_processes:
                if p.is_alive():
                    p.join(timeout=5)
                    if p.is_alive():
                        print(f"Terminating repomix worker {p.pid}")
                        p.terminate()
                        p.join(timeout=2)

            # Send poison pills to stop LLM workers
            for _ in self.llm_processes:
                try:
                    self.file_queue.put(None, timeout=1)
                except:
                    pass  # Queue might be full or closed

            # Wait for LLM workers to finish gracefully
            for p in self.llm_processes:
                if p.is_alive():
                    p.join(timeout=5)
                    if p.is_alive():
                        print(f"Terminating LLM worker {p.pid}")
                        p.terminate()
                        p.join(timeout=2)

        except Exception as e:
            print(f"Error during worker shutdown: {e}")
            # Force terminate all workers
            for p in self.repomix_processes + self.llm_processes:
                if p.is_alive():
                    try:
                        p.terminate()
                        p.join(timeout=1)
                    except:
                        pass

        print("All workers stopped.")

    def _initialize_analysis_file(self, repositories):
        """Initialize the single analysis file as JSON"""
        import json

        # Create initial JSON structure
        initial_data = {
            "metadata": {
                "generated": time.strftime('%Y-%m-%d %H:%M:%S'),
                "output_directory": self.output_dir.name,
                "total_repositories": len(repositories),
                "processing_configuration": {
                    "repomix_workers": self.config.REPOMIX_WORKERS,
                    "llm_workers": self.config.LLM_WORKERS,
                    "rate_limit_mb_per_min": self.config.LLM_RATE_LIMIT_MB_PER_MIN
                }
            },
            "analyses": []
        }

        # Write initial JSON structure
        with open(self.analysis_file, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2, ensure_ascii=False)

        print(f"📄 Analysis file initialized: {self.analysis_file}")
        print(f"📁 Repomixes directory: {self.output_dir / 'repomixes'}")

        return self.analysis_file

    def add_duplicate_info_to_metadata(self, duplicate_info):
        """Add duplicate detection information to the JSON metadata."""
        import json

        try:
            # Read existing JSON
            with open(self.analysis_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Add duplicate information to metadata
            data["metadata"]["duplicate_detection"] = duplicate_info

            # Write back to file
            with open(self.analysis_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print(f"📊 Added duplicate detection info to metadata: {duplicate_info['duplicates_removed']} duplicates removed")

        except Exception as e:
            print(f"⚠️ Failed to add duplicate info to metadata: {e}")

    def run(self, repositories):
        """
        Run the complete pipeline.

        Args:
            repositories: List of repository information dictionaries
        """
        import logging
        import time

        # Set up logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        logger = logging.getLogger(__name__)

        logger.info("🎯 REPOSITORY PIPELINE START")
        logger.info(f"📊 Processing {len(repositories)} repositories")

        # Log repository details
        logger.info("📋 Repository list:")
        for i, repo in enumerate(repositories, 1):
            logger.info(f"   {i:2d}. {repo['name']} ({repo.get('stars', 0)} stars)")
            logger.info(f"       URL: {repo['url']}")
            if 'search_keyword' in repo:
                logger.info(f"       Found via: '{repo['search_keyword']}'")

        # Log configuration
        logger.info("⚙️ Pipeline configuration:")
        logger.info(f"   Repomix workers: {self.repomix_workers}")
        logger.info(f"   LLM workers: {self.llm_workers}")
        logger.info(f"   Rate limit: {self.rate_limit_mb_per_min} MB/min")

        start_time = time.time()

        try:
            # Initialize single analysis file
            logger.info("📁 Initializing analysis file...")
            self._initialize_analysis_file(repositories)
            logger.info(f"✅ Analysis file initialized: {self.analysis_file}")

            # Add repositories to queue
            logger.info("📦 Adding repositories to processing queue...")
            self.add_repositories(repositories)
            logger.info(f"✅ {len(repositories)} repositories added to queue")

            # Start workers
            logger.info("🚀 Starting worker processes...")
            logger.info(f"   Starting {self.repomix_workers} repomix workers")
            logger.info(f"   Starting {self.llm_workers} LLM workers")
            self.start_workers()
            logger.info("✅ All workers started successfully")

            # Wait for all repositories to be processed
            logger.info("⏳ Waiting for repository processing to complete...")
            print("Waiting for repository processing to complete...")
            self.repo_queue.join()
            logger.info("✅ Repository processing completed")

            # Wait for all files to be analyzed
            logger.info("⏳ Waiting for file analysis to complete...")
            print("Waiting for file analysis to complete...")
            self.file_queue.join()
            logger.info("✅ File analysis completed")

            total_time = time.time() - start_time
            logger.info(f"🎉 PIPELINE COMPLETED SUCCESSFULLY!")
            logger.info(f"⏱️ Total processing time: {total_time:.1f} seconds")
            logger.info(f"📄 Analysis file: {self.analysis_file}")

        except Exception as e:
            logger.error(f"❌ PIPELINE FAILED: {e}")
            logger.error(f"📍 Error occurred in pipeline execution")
            raise

        finally:
            # Stop workers
            logger.info("🛑 Stopping worker processes...")
            self.stop_workers()
            logger.info("✅ All workers stopped")

            # Clean up rate limiter
            if hasattr(self.rate_limiter, 'cleanup'):
                logger.info("🧹 Cleaning up rate limiter...")
                self.rate_limiter.cleanup()
                logger.info("✅ Rate limiter cleaned up")

        return self.analysis_file
</file>

<file path="service.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Web Service
A Flask-based web service for running repository analysis jobs.
"""

import os
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_file
from werkzeug.exceptions import BadRequest
import subprocess
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import cloud components if available
try:
    from cloud_pipeline import CloudRepositoryPipeline
    from supabase_client import create_supabase_client
    from storage_manager import create_storage_manager
    from config import ScraperConfig
    CLOUD_AVAILABLE = ScraperConfig.USE_CLOUD_SERVICES
    print(f"Cloud services available: {CLOUD_AVAILABLE}")
except ImportError as e:
    print(f"Cloud components not available: {e}")
    CLOUD_AVAILABLE = False

app = Flask(__name__)

# Global job storage (fallback for local mode)
jobs = {}
job_lock = threading.Lock()

# Initialize cloud services if available
supabase_client = None
storage_manager = None

if CLOUD_AVAILABLE:
    try:
        supabase_client = create_supabase_client()
        storage_manager = create_storage_manager()
        print("Cloud services initialized successfully")
    except Exception as e:
        print(f"Failed to initialize cloud services: {e}")
        CLOUD_AVAILABLE = False

class JobStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

def run_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt=None, debug=False):
    """Run the repository analysis in a separate thread."""
    import logging

    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    logger.info(f"🚀 SERVICE FLOW: STARTING ANALYSIS JOB: {job_id}")
    logger.info(f"📋 SERVICE FLOW: Job parameters:")
    logger.info(f"   Keywords: {keywords}")
    logger.info(f"   Min stars: {min_stars}")
    logger.info(f"   Max repos: {max_repos} PER KEYWORD")
    logger.info(f"   Custom prompt: {custom_prompt is not None}")
    logger.info(f"   Debug mode: {debug}")

    # Calculate expected maximum repositories
    keyword_count = len([k.strip() for k in keywords.split(',')])
    expected_max = keyword_count * max_repos
    logger.info(f"🎯 SERVICE FLOW: Expected maximum repositories: {keyword_count} keywords × {max_repos} = {expected_max}")

    with job_lock:
        jobs[job_id]["status"] = JobStatus.RUNNING
        jobs[job_id]["started_at"] = datetime.now().isoformat()
        logger.info(f"✅ Job status updated to 'running'")

    try:
        # Import and run pipeline directly
        logger.info("📦 Importing pipeline components...")
        from pipeline import RepositoryPipeline
        from github_search import search_repositories

        # Search for repositories
        logger.info("🔍 SERVICE FLOW: Starting repository search...")
        logger.info(f"🌐 SERVICE FLOW: Searching GitHub with per-keyword logic...")
        repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos
        )

        logger.info(f"📊 SERVICE FLOW: Search completed: {len(repositories)} repositories found")
        logger.info(f"✅ SERVICE FLOW: Per-keyword logic working: {len(repositories)} ≤ {expected_max} (expected max)")

        if not repositories:
            logger.error("❌ No repositories found matching the criteria")
            raise Exception("No repositories found matching the criteria")

        # Update job with repository count
        with job_lock:
            jobs[job_id]["total_repositories"] = len(repositories)
            logger.info(f"✅ Job updated with repository count: {len(repositories)}")

        # Initialize and run local pipeline
        logger.info("🏭 SERVICE FLOW: Initializing repository pipeline...")
        logger.info(f"📦 SERVICE FLOW: Pipeline will process {len(repositories)} repositories")
        logger.info(f"🔧 SERVICE FLOW: Chunking enabled: 3MB limit with max 3 chunks per repo")
        pipeline = RepositoryPipeline()

        logger.info("🚀 SERVICE FLOW: Starting pipeline execution...")
        logger.info(f"⚡ SERVICE FLOW: Processing repositories with repomix + LLM analysis...")
        result_file = pipeline.run(repositories)

        logger.info(f"✅ SERVICE FLOW: Pipeline execution completed")
        logger.info(f"📄 SERVICE FLOW: Result file: {result_file}")
        
        with job_lock:
            jobs[job_id]["completed_at"] = datetime.now().isoformat()

            if result_file:
                jobs[job_id]["status"] = JobStatus.COMPLETED
                jobs[job_id]["analysis_file"] = str(result_file)

                # Get file size and directory info
                result_path = Path(result_file)
                if result_path.exists():
                    jobs[job_id]["analysis_size"] = result_path.stat().st_size
                    jobs[job_id]["output_directory"] = str(result_path.parent)

                print(f"✅ Local analysis job {job_id} completed: {result_file}")
            else:
                jobs[job_id]["status"] = JobStatus.FAILED
                jobs[job_id]["error"] = "Pipeline returned no result file"
                
    except Exception as e:
        with job_lock:
            jobs[job_id]["status"] = JobStatus.FAILED
            jobs[job_id]["error"] = str(e)
            jobs[job_id]["completed_at"] = datetime.now().isoformat()

def start_cloud_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt=None, debug=False):
    """Start cloud analysis by adding repositories to the processing queue."""
    try:
        # Import required modules
        from github_search import search_repositories
        from redis_queue import QueueManager, RepositoryQueueItem
        import uuid

        # Search for repositories
        repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos
        )

        if not repositories:
            raise Exception("No repositories found matching the criteria")

        # Initialize queue manager
        queue_manager = QueueManager(config.REDIS_URL)
        repo_queue = queue_manager.get_repository_queue()

        # Add repositories to the processing queue
        for repo in repositories:
            repo_item = RepositoryQueueItem(
                repository_url=repo['url'],
                repository_name=repo['name'],
                job_id=job_id
            )
            repo_queue.put(repo_item)

        # Update job status to running (workers will process the repositories)
        if supabase_client:
            supabase_client.update_job_status(job_id, JobStatus.RUNNING)

            # Create repository records in database
            from supabase_client import RepositoryRecord
            for repo in repositories:
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=job_id,
                    name=repo['name'],
                    url=repo['url'],
                    stars=repo.get('stars', 0),
                    status='pending'
                )
                supabase_client.create_repository(repo_record)
        else:
            with job_lock:
                jobs[job_id]["status"] = JobStatus.RUNNING
                jobs[job_id]["started_at"] = datetime.now().isoformat()
                jobs[job_id]["total_repositories"] = len(repositories)

        print(f"✅ Added {len(repositories)} repositories to processing queue for job {job_id}")

    except Exception as e:
        print(f"Failed to start cloud analysis job {job_id}: {e}")

        # Update job status to failed
        if supabase_client:
            supabase_client.update_job_status(job_id, JobStatus.FAILED, error_message=str(e))
        else:
            with job_lock:
                jobs[job_id]["status"] = JobStatus.FAILED
                jobs[job_id]["error"] = str(e)
                jobs[job_id]["completed_at"] = datetime.now().isoformat()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "Repository Research Tool",
        "version": "1.0",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/start', methods=['POST'])
def start_analysis():
    """Start a new repository analysis job."""
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("JSON data required")
        
        # Validate required parameters
        keywords = data.get('keywords')
        if not keywords:
            raise BadRequest("keywords parameter is required")
        
        min_stars = data.get('min_stars', 30)
        max_repos = data.get('max_repos', 20)
        custom_prompt = data.get('custom_prompt')
        debug = data.get('debug', False)
        
        # Create job
        job_id = str(uuid.uuid4())

        if CLOUD_AVAILABLE and supabase_client:
            # Use cloud services
            try:
                from supabase_client import JobRecord
                job_record = JobRecord(
                    id=job_id,
                    keywords=keywords,
                    min_stars=min_stars,
                    max_repos=max_repos,
                    status=JobStatus.PENDING,
                    created_at=datetime.now().isoformat(),
                    custom_prompt=custom_prompt,
                    debug=debug
                )
                supabase_client.create_job(job_record)
                print(f"Created cloud job {job_id}")
            except Exception as e:
                print(f"Failed to create cloud job, falling back to local: {e}")
                # Fallback to local storage
                with job_lock:
                    jobs[job_id] = {
                        "id": job_id,
                        "status": JobStatus.PENDING,
                        "created_at": datetime.now().isoformat(),
                        "parameters": {
                            "keywords": keywords,
                            "min_stars": min_stars,
                            "max_repos": max_repos,
                            "custom_prompt": custom_prompt,
                            "debug": debug
                        }
                    }
        else:
            # Use local storage
            with job_lock:
                jobs[job_id] = {
                    "id": job_id,
                    "status": JobStatus.PENDING,
                    "created_at": datetime.now().isoformat(),
                    "parameters": {
                        "keywords": keywords,
                        "min_stars": min_stars,
                        "max_repos": max_repos,
                        "custom_prompt": custom_prompt,
                        "debug": debug
                    }
                }

        # Start analysis based on deployment mode
        if CLOUD_AVAILABLE:
            # In cloud mode, add job to queue for worker processing
            start_cloud_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt, debug)
        else:
            # In local mode, run analysis in background thread
            thread = threading.Thread(
                target=run_analysis_job,
                args=(job_id, keywords, min_stars, max_repos, custom_prompt, debug)
            )
            thread.daemon = True
            thread.start()
        
        return jsonify({
            "job_id": job_id,
            "status": JobStatus.PENDING,
            "message": "Analysis job started"
        }), 202
        
    except BadRequest as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a specific job."""
    if CLOUD_AVAILABLE and supabase_client:
        try:
            # Get job from cloud database
            job_record = supabase_client.get_job(job_id)
            if job_record:
                job_data = {
                    "id": job_record.id,
                    "status": job_record.status,
                    "created_at": job_record.created_at,
                    "started_at": job_record.started_at,
                    "completed_at": job_record.completed_at,
                    "parameters": {
                        "keywords": job_record.keywords,
                        "min_stars": job_record.min_stars,
                        "max_repos": job_record.max_repos,
                        "custom_prompt": job_record.custom_prompt,
                        "debug": job_record.debug
                    },
                    "total_repositories": job_record.total_repositories,
                    "analysis_file_url": job_record.analysis_file_url,
                    "error_message": job_record.error_message
                }
                return jsonify(job_data)
        except Exception as e:
            print(f"Error getting job from cloud: {e}")
            # Fall back to local storage

    # Use local storage
    with job_lock:
        job = jobs.get(job_id)

    if not job:
        return jsonify({"error": "Job not found"}), 404

    return jsonify(job)

@app.route('/results/<job_id>', methods=['GET'])
def get_job_results(job_id):
    """Get the results of a completed job."""
    with job_lock:
        job = jobs.get(job_id)
    
    if not job:
        return jsonify({"error": "Job not found"}), 404
    
    if job["status"] != JobStatus.COMPLETED:
        return jsonify({"error": "Job not completed"}), 400
    
    analysis_file = job.get("analysis_file")
    if not analysis_file or not Path(analysis_file).exists():
        return jsonify({"error": "Analysis file not found"}), 404
    
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis_data = json.load(f)
        return jsonify(analysis_data)
    except Exception as e:
        return jsonify({"error": f"Error reading analysis file: {str(e)}"}), 500

@app.route('/download/<job_id>', methods=['GET'])
def download_analysis(job_id):
    """Download the analysis file for a completed job."""
    with job_lock:
        job = jobs.get(job_id)
    
    if not job:
        return jsonify({"error": "Job not found"}), 404
    
    if job["status"] != JobStatus.COMPLETED:
        return jsonify({"error": "Job not completed"}), 400
    
    analysis_file = job.get("analysis_file")
    if not analysis_file or not Path(analysis_file).exists():
        return jsonify({"error": "Analysis file not found"}), 404
    
    return send_file(
        analysis_file,
        as_attachment=True,
        download_name=f"repository_analysis_{job_id}.json"
    )

@app.route('/jobs', methods=['GET'])
def list_jobs():
    """List all jobs."""
    with job_lock:
        job_list = list(jobs.values())
    
    return jsonify({
        "jobs": job_list,
        "total_jobs": len(job_list)
    })

@app.route('/config', methods=['GET'])
def get_config():
    """Get current service configuration."""
    try:
        from config import ScraperConfig
        config = ScraperConfig()
        
        return jsonify({
            "repomix_workers": config.REPOMIX_WORKERS,
            "llm_workers": config.LLM_WORKERS,
            "rate_limit_mb_per_min": config.LLM_RATE_LIMIT_MB_PER_MIN,
            "max_file_size_mb": config.MAX_FILE_SIZE_MB,
            "llm_model": config.LLM_MODEL,
            "api_configured": bool(config.LLM_API_KEY)
        })
    except Exception as e:
        return jsonify({"error": f"Error getting config: {str(e)}"}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    
    print(f"🚀 Starting Repository Research Tool Service")
    print(f"   Port: {port}")
    print(f"   Debug: {debug}")
    print(f"   Health check: http://localhost:{port}/health")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
</file>

</files>
