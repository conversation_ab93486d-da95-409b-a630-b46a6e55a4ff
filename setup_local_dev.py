#!/usr/bin/env python3
"""
Repository Research Tool - Local Development Setup

This script sets up the local development environment with Docker containers
for Redis and Supabase, following the unified architecture.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def run_command(cmd, check=True, capture_output=False):
    """Run a shell command with proper error handling."""
    print(f"Running: {cmd}")
    result = subprocess.run(
        cmd, shell=True, check=check, 
        capture_output=capture_output, text=True
    )
    if capture_output:
        return result.stdout.strip()
    return result.returncode == 0

def check_docker():
    """Check if Docker is available and running."""
    try:
        run_command("docker --version", capture_output=True)
        run_command("docker-compose --version", capture_output=True)
        print("✅ Docker and Docker Compose are available")
        return True
    except subprocess.CalledProcessError:
        print("❌ Docker or Docker Compose not found")
        print("Please install Docker Desktop: https://www.docker.com/products/docker-desktop")
        return False

def setup_environment():
    """Set up the local environment file."""
    env_local = Path(".env.local")
    env_file = Path(".env")
    
    if not env_file.exists():
        if env_local.exists():
            print("📄 Copying .env.local to .env")
            import shutil
            shutil.copy(env_local, env_file)
        else:
            print("❌ .env.local not found. Please create it first.")
            return False
    
    print("✅ Environment file ready")
    return True

def start_services():
    """Start the local development services."""
    print("🚀 Starting local development services...")
    
    # Stop any existing services
    run_command("docker-compose -f docker-compose.local.yml down", check=False)
    
    # Start services
    if not run_command("docker-compose -f docker-compose.local.yml up -d"):
        print("❌ Failed to start services")
        return False
    
    print("⏳ Waiting for services to be ready...")
    time.sleep(10)
    
    return True

def wait_for_services():
    """Wait for all services to be healthy."""
    services = {
        "Redis": ("localhost", 6379),
        "PostgreSQL": ("localhost", 5432),
        "Supabase REST": ("localhost", 3000),
        "Supabase Storage": ("localhost", 5000)
    }
    
    for service_name, (host, port) in services.items():
        print(f"⏳ Waiting for {service_name} on {host}:{port}...")
        
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                if service_name == "Supabase REST":
                    response = requests.get(f"http://{host}:{port}/", timeout=2)
                    if response.status_code in [200, 404]:  # 404 is OK for PostgREST root
                        break
                elif service_name == "Supabase Storage":
                    response = requests.get(f"http://{host}:{port}/status", timeout=2)
                    if response.status_code == 200:
                        break
                else:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2)
                    result = sock.connect_ex((host, port))
                    sock.close()
                    if result == 0:
                        break
            except:
                pass
            
            time.sleep(2)
            if attempt == max_attempts - 1:
                print(f"❌ {service_name} failed to start")
                return False
        
        print(f"✅ {service_name} is ready")
    
    return True

def initialize_database():
    """Initialize the database schema."""
    print("🗄️ Initializing database schema...")
    
    # The schema is automatically loaded via docker-entrypoint-initdb.d
    # But we can verify it worked by checking if tables exist
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="postgres",
            user="postgres",
            password="postgres"
        )
        cursor = conn.cursor()
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
        tables = cursor.fetchall()
        cursor.close()
        conn.close()
        
        if tables:
            print(f"✅ Database initialized with {len(tables)} tables")
            return True
        else:
            print("⚠️ No tables found, schema may not have loaded")
            return False
    except Exception as e:
        print(f"❌ Database initialization check failed: {e}")
        return False

def test_connection():
    """Test the unified pipeline connection."""
    print("🧪 Testing unified pipeline connection...")
    
    try:
        # Add src to path
        sys.path.insert(0, 'src')
        
        from config import ScraperConfig
        from pipeline import RepositoryPipeline
        
        config = ScraperConfig()
        pipeline = RepositoryPipeline(config=config)
        
        print("✅ Unified pipeline initialized successfully")
        print(f"   Redis URL: {config.REDIS_URL}")
        print(f"   Supabase URL: {config.SUPABASE_URL}")
        
        return True
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🔧 Repository Research Tool - Local Development Setup")
    print("=" * 60)
    
    # Check prerequisites
    if not check_docker():
        return 1
    
    # Setup environment
    if not setup_environment():
        return 1
    
    # Start services
    if not start_services():
        return 1
    
    # Wait for services
    if not wait_for_services():
        return 1
    
    # Initialize database
    if not initialize_database():
        print("⚠️ Database initialization had issues, but continuing...")
    
    # Test connection
    if not test_connection():
        return 1
    
    print("\n🎉 Local development environment is ready!")
    print("\nNext steps:")
    print("1. Update .env with your GitHub token and Gemini API key")
    print("2. Run: python main.py --keywords 'test' --max-repos 1 --min-stars 100")
    print("3. Or start the service: python service.py")
    print("\nTo stop services: docker-compose -f docker-compose.local.yml down")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
