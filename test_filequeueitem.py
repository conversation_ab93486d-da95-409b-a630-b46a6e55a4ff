#!/usr/bin/env python3
"""
Test FileQueueItem constructor to understand the exact signature.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Test FileQueueItem constructor."""
    print("🧪 TESTING FILEQUEUEITEM CONSTRUCTOR")
    print("=" * 50)
    
    try:
        from redis_queue import FileQueueItem
        print("✅ Successfully imported FileQueueItem from redis_queue")
        
        # Test the constructor
        print("\n🔍 Testing constructor with positional arguments...")
        file_item = FileQueueItem(
            "test_file_path",
            "test_repo_name", 
            "test_job_id"
        )
        print("✅ Constructor with positional arguments works!")
        print(f"   file_path: {file_item.file_path}")
        print(f"   repository_name: {file_item.repository_name}")
        print(f"   job_id: {file_item.job_id}")
        print(f"   data: {file_item.data}")
        
        print("\n🔍 Testing constructor with keyword arguments...")
        file_item2 = FileQueueItem(
            file_path="test_file_path2",
            repository_name="test_repo_name2",
            job_id="test_job_id2"
        )
        print("✅ Constructor with keyword arguments works!")
        print(f"   file_path: {file_item2.file_path}")
        print(f"   repository_name: {file_item2.repository_name}")
        print(f"   job_id: {file_item2.job_id}")
        print(f"   data: {file_item2.data}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing FileQueueItem: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
