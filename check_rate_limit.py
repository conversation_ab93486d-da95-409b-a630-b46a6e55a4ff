#!/usr/bin/env python3
import requests
import os
from datetime import datetime

headers = {'Authorization': f'token {os.getenv("GITHUB_TOKEN")}'}
response = requests.get('https://api.github.com/rate_limit', headers=headers)
data = response.json()

print('GitHub API Rate Limit Response:')
print(data)

if 'rate' in data:
    print(f'  Remaining: {data["rate"]["remaining"]}/{data["rate"]["limit"]}')
    reset_time = datetime.fromtimestamp(data['rate']['reset'])
    print(f'  Resets at: {reset_time}')
    print(f'  Current time: {datetime.now()}')

    if data["rate"]["remaining"] > 0:
        print("✅ API calls available")
    else:
        print("❌ Rate limit exceeded")
        wait_minutes = (reset_time - datetime.now()).total_seconds() / 60
        print(f"⏳ Wait {wait_minutes:.1f} minutes")
else:
    print("❌ Unexpected response format")
