-- Database schema for Repository Research Tool
-- Supabase PostgreSQL schema for job tracking and metadata

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Jobs table - tracks analysis jobs
CREATE TABLE IF NOT EXISTS jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keywords TEXT NOT NULL,
    min_stars INTEGER NOT NULL DEFAULT 30,
    max_repos INTEGER NOT NULL DEFAULT 20,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    analysis_file_url TEXT,
    total_repositories INTEGER,
    custom_prompt TEXT,
    debug BOOLEAN DEFAULT FALSE
);

-- Repositories table - tracks individual repositories
CREATE TABLE IF NOT EXISTS repositories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    stars INTEGER NOT NULL DEFAULT 0,
    file_size_mb DECIMAL(10,3) NOT NULL DEFAULT 0,
    repomix_file_url TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    analysis_status TEXT NOT NULL DEFAULT 'pending' CHECK (analysis_status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analyses table - tracks LLM analysis results
CREATE TABLE IF NOT EXISTS analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    repository_id UUID NOT NULL REFERENCES repositories(id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    worker_id TEXT NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    analysis_content TEXT NOT NULL,
    analysis_length INTEGER NOT NULL DEFAULT 0,
    chunk_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sentry events table - tracks errors and monitoring
CREATE TABLE IF NOT EXISTS sentry_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    event_id TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    level TEXT NOT NULL DEFAULT 'info' CHECK (level IN ('debug', 'info', 'warning', 'error', 'critical')),
    message TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_repositories_job_id ON repositories(job_id);
CREATE INDEX IF NOT EXISTS idx_repositories_status ON repositories(analysis_status);
CREATE INDEX IF NOT EXISTS idx_analyses_job_id ON analyses(job_id);
CREATE INDEX IF NOT EXISTS idx_analyses_repository_id ON analyses(repository_id);
CREATE INDEX IF NOT EXISTS idx_sentry_events_job_id ON sentry_events(job_id);
CREATE INDEX IF NOT EXISTS idx_sentry_events_level ON sentry_events(level);
CREATE INDEX IF NOT EXISTS idx_sentry_events_timestamp ON sentry_events(timestamp DESC);

-- Row Level Security (RLS) policies
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE repositories ENABLE ROW LEVEL SECURITY;
ALTER TABLE analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE sentry_events ENABLE ROW LEVEL SECURITY;

-- Allow all operations for authenticated users (service role)
-- In production, you might want more restrictive policies
CREATE POLICY "Allow all for authenticated users" ON jobs
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all for authenticated users" ON repositories
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all for authenticated users" ON analyses
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all for authenticated users" ON sentry_events
    FOR ALL USING (auth.role() = 'authenticated');

-- Allow service role to bypass RLS
CREATE POLICY "Allow service role" ON jobs
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Allow service role" ON repositories
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Allow service role" ON analyses
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Allow service role" ON sentry_events
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Functions for statistics and monitoring
CREATE OR REPLACE FUNCTION get_job_statistics(job_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'job_id', job_uuid,
        'total_repositories', COUNT(r.id),
        'completed_repositories', COUNT(r.id) FILTER (WHERE r.analysis_status = 'completed'),
        'failed_repositories', COUNT(r.id) FILTER (WHERE r.analysis_status = 'failed'),
        'pending_repositories', COUNT(r.id) FILTER (WHERE r.analysis_status = 'pending'),
        'processing_repositories', COUNT(r.id) FILTER (WHERE r.analysis_status = 'processing'),
        'total_analyses', COUNT(a.id),
        'total_file_size_mb', COALESCE(SUM(r.file_size_mb), 0),
        'average_analysis_length', COALESCE(AVG(a.analysis_length), 0)
    ) INTO result
    FROM repositories r
    LEFT JOIN analyses a ON r.id = a.repository_id
    WHERE r.job_id = job_uuid;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup old jobs and related data
CREATE OR REPLACE FUNCTION cleanup_old_jobs(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM jobs 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_old
    AND status IN ('completed', 'failed');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get queue statistics
CREATE OR REPLACE FUNCTION get_queue_statistics()
RETURNS JSON AS $$
BEGIN
    RETURN json_build_object(
        'total_jobs', (SELECT COUNT(*) FROM jobs),
        'pending_jobs', (SELECT COUNT(*) FROM jobs WHERE status = 'pending'),
        'running_jobs', (SELECT COUNT(*) FROM jobs WHERE status = 'running'),
        'completed_jobs', (SELECT COUNT(*) FROM jobs WHERE status = 'completed'),
        'failed_jobs', (SELECT COUNT(*) FROM jobs WHERE status = 'failed'),
        'total_repositories', (SELECT COUNT(*) FROM repositories),
        'pending_repositories', (SELECT COUNT(*) FROM repositories WHERE analysis_status = 'pending'),
        'processing_repositories', (SELECT COUNT(*) FROM repositories WHERE analysis_status = 'processing'),
        'completed_repositories', (SELECT COUNT(*) FROM repositories WHERE analysis_status = 'completed'),
        'failed_repositories', (SELECT COUNT(*) FROM repositories WHERE analysis_status = 'failed'),
        'total_analyses', (SELECT COUNT(*) FROM analyses)
    );
END;
$$ LANGUAGE plpgsql;

-- Create storage buckets (these need to be created via Supabase dashboard or API)
-- Bucket: 'repomixes' - for storing repomix markdown files
-- Bucket: 'analyses' - for storing final analysis JSON files
-- Bucket: 'logs' - for storing log files

-- Comments for bucket creation (to be done via Supabase dashboard):
-- 1. Create bucket 'repomixes' with public access for repomix files
-- 2. Create bucket 'analyses' with public access for analysis results
-- 3. Create bucket 'logs' with restricted access for log files

-- Grant necessary permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
