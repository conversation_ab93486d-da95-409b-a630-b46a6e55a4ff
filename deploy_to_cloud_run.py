#!/usr/bin/env python3
"""
Google Cloud Run deployment script for Repository Research Tool.
Builds and deploys the containerized service to Google Cloud Run.
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

def check_gcloud_auth():
    """Check if gcloud is authenticated."""
    print("🔐 Checking Google Cloud authentication...")
    
    try:
        result = subprocess.run([
            "gcloud", "auth", "list", "--filter=status:ACTIVE", "--format=value(account)"
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            account = result.stdout.strip()
            print(f"✅ Authenticated as: {account}")
            return True
        else:
            print("❌ Not authenticated with Google Cloud")
            print("   Run: gcloud auth login")
            return False
    except FileNotFoundError:
        print("❌ gcloud CLI not found")
        print("   Install from: https://cloud.google.com/sdk/docs/install")
        return False

def get_project_config():
    """Get Google Cloud project configuration."""
    print("\n📋 Getting project configuration...")
    
    try:
        # Get current project
        result = subprocess.run([
            "gcloud", "config", "get-value", "project"
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            project_id = result.stdout.strip()
            print(f"✅ Project ID: {project_id}")
        else:
            print("❌ No project configured")
            print("   Run: gcloud config set project YOUR_PROJECT_ID")
            return None
        
        # Get current region
        result = subprocess.run([
            "gcloud", "config", "get-value", "run/region"
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            region = result.stdout.strip()
            print(f"✅ Region: {region}")
        else:
            region = "us-central1"  # Default
            print(f"⚠️ No region configured, using default: {region}")
            print("   Set with: gcloud config set run/region YOUR_REGION")
        
        return {
            "project_id": project_id,
            "region": region
        }
        
    except Exception as e:
        print(f"❌ Error getting project config: {e}")
        return None

def enable_apis(project_id):
    """Enable required Google Cloud APIs."""
    print("\n🔧 Enabling required APIs...")
    
    apis = [
        "run.googleapis.com",
        "cloudbuild.googleapis.com",
        "containerregistry.googleapis.com",
        "redis.googleapis.com",
        "vpcaccess.googleapis.com"
    ]
    
    for api in apis:
        try:
            result = subprocess.run([
                "gcloud", "services", "enable", api, "--project", project_id
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Enabled {api}")
            else:
                print(f"❌ Failed to enable {api}: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error enabling {api}: {e}")
            return False
    
    return True

def build_and_push_image(project_id):
    """Build and push Docker image to Google Container Registry."""
    print("\n🐳 Building and pushing Docker image...")
    
    image_name = f"gcr.io/{project_id}/repository-research-tool"
    tag = "latest"
    full_image = f"{image_name}:{tag}"
    
    try:
        # Build image using Cloud Build
        print("   Building image with Cloud Build...")
        result = subprocess.run([
            "gcloud", "builds", "submit",
            "--tag", full_image,
            "--file", "Dockerfile.production",
            "."
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Image built and pushed: {full_image}")
            return full_image
        else:
            print(f"❌ Build failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Error building image: {e}")
        return None

def create_secrets(project_id):
    """Create secrets for sensitive configuration."""
    print("\n🔐 Creating secrets...")
    
    secrets = {
        "supabase-config": {
            "url": os.getenv("SUPABASE_URL", ""),
            "service-key": os.getenv("SUPABASE_KEY", ""),
            "anon-key": os.getenv("SUPABASE_ANON_KEY", "")
        },
        "api-keys": {
            "gemini-api-key": os.getenv("LLM_API_KEY", ""),
            "github-token": os.getenv("GITHUB_TOKEN", "")
        }
    }
    
    for secret_name, secret_data in secrets.items():
        try:
            # Create secret
            result = subprocess.run([
                "gcloud", "secrets", "create", secret_name,
                "--project", project_id
            ], capture_output=True, text=True)
            
            if result.returncode != 0 and "already exists" not in result.stderr:
                print(f"❌ Failed to create secret {secret_name}: {result.stderr}")
                continue
            
            # Add secret versions
            for key, value in secret_data.items():
                if value:
                    version_result = subprocess.run([
                        "gcloud", "secrets", "versions", "add", secret_name,
                        "--data-file=-",
                        "--project", project_id
                    ], input=value, text=True, capture_output=True)
                    
                    if version_result.returncode == 0:
                        print(f"✅ Added secret version for {secret_name}")
                    else:
                        print(f"❌ Failed to add secret version for {secret_name}")
                else:
                    print(f"⚠️ Empty value for {secret_name}.{key}")
        
        except Exception as e:
            print(f"❌ Error creating secret {secret_name}: {e}")
    
    return True

def deploy_service(project_id, region, image):
    """Deploy service to Cloud Run."""
    print("\n🚀 Deploying to Cloud Run...")
    
    service_name = "repository-research-tool"
    
    try:
        # Deploy service
        result = subprocess.run([
            "gcloud", "run", "deploy", service_name,
            "--image", image,
            "--platform", "managed",
            "--region", region,
            "--allow-unauthenticated",
            "--memory", "4Gi",
            "--cpu", "2",
            "--timeout", "3600",
            "--max-instances", "10",
            "--min-instances", "1",
            "--concurrency", "10",
            "--set-env-vars", f"DEPLOYMENT_MODE=cloud,PORT=8080,REPOMIX_WORKERS=15,LLM_WORKERS=4",
            "--project", project_id
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Service deployed successfully")
            
            # Get service URL
            url_result = subprocess.run([
                "gcloud", "run", "services", "describe", service_name,
                "--region", region,
                "--format", "value(status.url)",
                "--project", project_id
            ], capture_output=True, text=True)
            
            if url_result.returncode == 0:
                service_url = url_result.stdout.strip()
                print(f"🌐 Service URL: {service_url}")
                return service_url
            
            return True
        else:
            print(f"❌ Deployment failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Error deploying service: {e}")
        return None

def test_deployed_service(service_url):
    """Test the deployed service."""
    print(f"\n🧪 Testing deployed service: {service_url}")
    
    try:
        import requests
        
        # Test health endpoint
        health_url = f"{service_url}/health"
        response = requests.get(health_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing service: {e}")
        return False

def main():
    """Main deployment function."""
    print("🚀 Google Cloud Run Deployment for Repository Research Tool")
    print("=" * 70)
    
    # Check authentication
    if not check_gcloud_auth():
        return 1
    
    # Get project configuration
    config = get_project_config()
    if not config:
        return 1
    
    project_id = config["project_id"]
    region = config["region"]
    
    # Enable APIs
    if not enable_apis(project_id):
        return 1
    
    # Build and push image
    image = build_and_push_image(project_id)
    if not image:
        return 1
    
    # Create secrets
    if not create_secrets(project_id):
        print("⚠️ Some secrets may not be configured properly")
    
    # Deploy service
    service_url = deploy_service(project_id, region, image)
    if not service_url:
        return 1
    
    # Test deployment
    if isinstance(service_url, str) and service_url.startswith("http"):
        if test_deployed_service(service_url):
            print("\n🎉 Deployment successful!")
            print("=" * 70)
            print(f"✅ Service deployed to: {service_url}")
            print(f"✅ Health check: {service_url}/health")
            print(f"✅ Configuration: {service_url}/config")
            
            print("\n📋 Next Steps:")
            print("1. Configure Redis instance for production")
            print("2. Set up monitoring and alerting")
            print("3. Configure custom domain (optional)")
            print("4. Set up CI/CD pipeline")
            
            return 0
    
    print("\n✅ Deployment completed (URL not available for testing)")
    return 0

if __name__ == "__main__":
    sys.exit(main())
