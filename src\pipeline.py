"""
Cloud-integrated pipeline for Repository Research Tool.
Uses Redis queues, Supabase database, and cloud storage.
"""

import os
import uuid
import time
import json
import logging
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any

from config import ScraperConfig
from redis_queue import QueueManager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord, AnalysisRecord
from storage_manager import create_storage_manager
from monitoring import RepositoryMonitor
from llm_rate_limiter import LLMRateLimiter

# Standalone worker functions that initialize their own resources

def repomix_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone repomix worker function that initializes its own resources."""
    import subprocess
    import shutil
    import tempfile
    from pathlib import Path

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from queue_manager import get_queue_manager, RepositoryQueueItem, FileQueueItem
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client
    from utils import get_file_size_mb

    config = ScraperConfig()  # Loads from .env
    queue_manager = get_queue_manager(config.REDIS_URL)  # Use unified queue manager
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    logger = logging.getLogger(f'repomix_worker_{worker_id}')
    logger.info(f"Repomix Worker {worker_id} for job {job_id} started. Using queue type: {queue_manager.get_queue_type()}")

    repo_queue = queue_manager.get_repository_queue()
    file_queue = queue_manager.get_file_queue()
    repomix_validated_queue = queue_manager.get_repomix_validated_queue()

    while True:
        try:
            # Get repository from Redis queue
            repo_item = repo_queue.get(timeout=30)
            if repo_item is None:
                break

            repo_name = repo_item.repository_name  # Access properties directly from QueueItem
            repo_url = repo_item.repository_url   # Access properties directly from QueueItem
            logger.info(f"Repomix Worker {worker_id}: Starting {repo_name}")

            # Create a temporary local file for repomix output
            temp_dir = Path(tempfile.gettempdir()) / job_id
            temp_dir.mkdir(parents=True, exist_ok=True)
            safe_repo_name = repo_name.replace('/', '_')
            local_output_file = temp_dir / f"{safe_repo_name}.md"

            # Run the actual repomix command
            npx_path = shutil.which('npx')
            if not npx_path:
                raise RuntimeError("npx not found in PATH")

            cmd = [
                npx_path, 'repomix', '--remote', repo_url,
                '--include', config.FILE_INCLUDES,
                '--output', str(local_output_file)
            ]
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=600,
                encoding='utf-8', errors='replace'
            )

            if result.returncode != 0 or not local_output_file.exists():
                logger.error(f"Repomix Worker {worker_id}: Failed {repo_name} - {result.stderr[:200]}")
                # Clean up and continue
                if local_output_file.exists():
                    local_output_file.unlink()
                repo_queue.task_done(repo_item)
                continue

            # TOKEN-BASED CHUNKING: Check token count and chunk if necessary
            file_size_mb = get_file_size_mb(str(local_output_file))
            logger.info(f"Repomix Worker {worker_id}: {repo_name} file size: {file_size_mb:.2f}MB")

            # Read the full file content
            with open(local_output_file, 'r', encoding='utf-8') as f:
                repomix_content = f.read()

            # Import token chunking utilities
            from token_chunking import count_tokens_in_text, get_token_chunker

            # Check token count
            token_count = count_tokens_in_text(repomix_content)
            logger.info(f"Repomix Worker {worker_id}: {repo_name} token count: {token_count:,}")

            chunks_created = 0  # Track number of chunks for logging

            if token_count > 850000:
                # Use TokenChunker to split into parts, keeping only first 3
                logger.info(f"Repomix Worker {worker_id}: {repo_name} exceeds 850K tokens - chunking required")
                chunker = get_token_chunker(max_tokens_per_chunk=850000, max_chunks=3)
                chunks = chunker.chunk_content_by_tokens(repomix_content)
                chunks_created = len(chunks)

                logger.info(f"Repomix Worker {worker_id}: {repo_name} split into {chunks_created} chunks")

                # Upload each chunk and queue for LLM analysis
                for i, chunk_content in enumerate(chunks):
                    chunk_filename = f"{safe_repo_name}_chunk_{i}.md"
                    chunk_url = storage_manager.upload_repomix_file(
                        job_id, chunk_filename, chunk_content
                    )
                    logger.info(f"Repomix Worker {worker_id}: Uploaded chunk {i}: {chunk_filename}")

                    # Queue each chunk for LLM analysis
                    file_item = FileQueueItem(
                        file_path=chunk_url,
                        repository_name=repo_name,
                        job_id=job_id,
                        chunk_id=str(i)
                    )
                    file_queue.put(file_item)
                    logger.info(f"Repomix Worker {worker_id}: Queued chunk {i} for LLM analysis")

                # Upload the full file for repository record (for reference)
                repomix_url = storage_manager.upload_repomix_file(
                    job_id, f"{safe_repo_name}_full.md", repomix_content
                )
            else:
                # File is within token limit, use original file
                logger.info(f"Repomix Worker {worker_id}: {repo_name} within 850K token limit - no chunking needed")
                repomix_filename = f"{safe_repo_name}.md"
                repomix_url = storage_manager.upload_repomix_file(
                    job_id, repomix_filename, repomix_content
                )
                logger.info(f"Repomix Worker {worker_id}: Uploaded original file: {repomix_filename}")

                # Queue the single file for LLM analysis
                file_item = FileQueueItem(
                    file_path=repomix_url,
                    repository_name=repo_name,
                    job_id=job_id
                )
                file_queue.put(file_item)
                logger.info(f"Repomix Worker {worker_id}: Queued original file for LLM analysis")

            # Update repository record in database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if repo_record:
                supabase_client.update_repository_status(
                    repo_record.id,
                    "repomix_processed",
                    repomix_file_url=repomix_url,  # Use the URL of the full file
                    file_size_mb=file_size_mb      # Use the size of the full file
                )

            # Clean up temporary file
            local_output_file.unlink()
            repo_queue.task_done(repo_item)

            if chunks_created > 0:
                logger.info(f"Repomix Worker {worker_id}: Completed {repo_name} - chunked into {chunks_created} parts, queued for LLM analysis")
            else:
                logger.info(f"Repomix Worker {worker_id}: Completed {repo_name} - single file ({file_size_mb:.2f}MB), queued for LLM analysis")

        except Exception as e:
            logger.error(f"Repomix Worker {worker_id}: Error processing repository: {e}", exc_info=True)
            if 'repo_item' in locals() and repo_item is not None:
                repo_queue.task_failed(repo_item, str(e))  # Use task_failed for proper error handling
            continue  # Continue to next item instead of breaking


def llm_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone LLM worker function that initializes its own resources."""
    import requests
    import uuid
    from datetime import datetime

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from queue_manager import get_queue_manager, FileQueueItem
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client, AnalysisRecord
    from llm_rate_limiter import LLMRateLimiter

    config = ScraperConfig()  # Loads from .env
    queue_manager = get_queue_manager(config.REDIS_URL)  # Use unified queue manager
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    # Initialize Redis client for rate limiter with fallback
    import redis
    redis_client_for_limiter = None
    if config.REDIS_URL and config.REDIS_URL != 'local://memory':
        try:
            temp_redis_client = redis.from_url(config.REDIS_URL)
            temp_redis_client.ping()
            redis_client_for_limiter = temp_redis_client
            logger.info(f"LLM Worker {worker_id}: Connected to Redis for Rate Limiter.")
        except redis.ConnectionError:
            logger.warning(f"LLM Worker {worker_id}: Redis not available for Rate Limiter, using fallback.")
    else:
        logger.info(f"LLM Worker {worker_id}: Redis URL not set or is local, using file-based rate limiter fallback.")

    logger = logging.getLogger(f'llm_worker_{worker_id}')  # Move logger before rate limiter

    rate_limiter = LLMRateLimiter(redis_client=redis_client_for_limiter)

    logger.info(f"LLM Worker {worker_id} for job {job_id} started. Using queue type: {queue_manager.get_queue_type()}")

    file_queue = queue_manager.get_file_queue()

    def build_analysis_prompt(repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(config, 'CUSTOM_PROMPT') and config.CUSTOM_PROMPT:
            return config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    while True:
        try:
            # Get file from Redis queue
            file_item = file_queue.get(timeout=30)
            if file_item is None:
                break

            repo_name = file_item.repository_name  # Access properties directly from QueueItem
            file_url = file_item.file_path        # Access properties directly from QueueItem
            chunk_id = getattr(file_item, 'chunk_id', None)  # Get chunk_id if available
            logger.info(f"LLM Worker {worker_id}: Starting analysis of {repo_name}")

            # Download repomix content from cloud storage
            repomix_content = storage_manager.download_repomix_file(file_url)
            file_size_mb = len(repomix_content.encode('utf-8')) / (1024 * 1024)

            # Wait for rate limit slot
            if not rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300):
                logger.warning(f"LLM Worker {worker_id} timed out waiting for rate limit slot")
                continue

            # Use Gemini Flash 2.5 for analysis
            from gemini_flash_25 import get_gemini_client

            gemini_client = get_gemini_client(config.LLM_API_KEY, max_workers=config.LLM_WORKERS)

            # Analyze with Gemini Flash 2.5
            result = gemini_client.analyze_repository(
                content=repomix_content,
                repository_name=repo_name,
                chunk_id=chunk_id
            )

            if not result['success']:
                logger.error(f"LLM Worker {worker_id}: Failed analysis for {repo_name} - {result.get('error', 'Unknown error')}")
                file_queue.task_done(file_item)
                continue

            # Extract analysis from Gemini Flash 2.5 result
            analysis_content = result['analysis']
            input_tokens = result.get('input_tokens', 0)
            output_tokens = result.get('output_tokens', 0)
            duration = result.get('duration', 0)

            logger.info(f"LLM Worker {worker_id}: Analysis complete for {repo_name}")
            logger.info(f"   Duration: {duration:.1f}s")
            logger.info(f"   Input tokens: {input_tokens:,}")
            logger.info(f"   Output tokens: {output_tokens:,}")
            logger.info(f"   Analysis length: {len(analysis_content)} characters")

            # Find the corresponding repository_id from the database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if not repo_record:
                logger.error(f"LLM Worker {worker_id}: Could not find repository record for {repo_name}")
                file_queue.task_done(file_item)
                continue

            # Create analysis record
            analysis_record = AnalysisRecord(
                id=str(uuid.uuid4()),
                repository_id=repo_record.id,
                job_id=job_id,
                worker_id=str(worker_id),
                processed_at=datetime.now().isoformat(),
                analysis_content=analysis_content,
                analysis_length=len(analysis_content)
            )

            supabase_client.create_analysis(analysis_record)
            file_queue.task_done(file_item)

            logger.info(f"LLM Worker {worker_id}: Completed analysis for {repo_name}")

        except Exception as e:
            logger.error(f"LLM Worker {worker_id}: Error processing file: {e}", exc_info=True)
            if 'file_item' in locals() and file_item is not None:
                file_queue.task_failed(file_item, str(e))  # Use task_failed for proper error handling
            continue  # Continue to next item instead of breaking


logger = logging.getLogger(__name__)

class RepositoryPipeline:
    """
    Cloud-native pipeline for repository processing.
    Uses Redis for distributed queues, Supabase for persistence, and cloud storage.
    """
    
    def __init__(self, config=None, job_id=None):
        """
        Initialize the cloud pipeline.
        
        Args:
            config: Configuration object (defaults to ScraperConfig)
            job_id: Unique job identifier (generated if not provided)
        """
        self.config = config or ScraperConfig()
        self.job_id = job_id or str(uuid.uuid4())
        
        # Initialize cloud services
        self._initialize_cloud_services()
        
        # Initialize local components
        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None
        
        # Create local output directory for temporary files
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
        self.output_dir = Path(self.config.OUTPUT_BASE) / f"cloud_{timestamp_str}"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
        
        logger.info(f"Initialized cloud pipeline for job {self.job_id}")
    
    def _initialize_cloud_services(self):
        """Initialize cloud service connections."""
        try:
            # Initialize unified queue manager (handles Redis fallback to local)
            from queue_manager import get_queue_manager
            self.queue_manager = get_queue_manager(self.config.REDIS_URL)
            self.repo_queue = self.queue_manager.get_repository_queue()
            self.file_queue = self.queue_manager.get_file_queue()

            # Initialize distributed rate limiter with Redis client (if available)
            redis_client_for_limiter = None
            if hasattr(self.queue_manager, '_manager') and hasattr(self.queue_manager._manager, 'redis_client'):
                redis_client_for_limiter = self.queue_manager._manager.redis_client

            self.rate_limiter = LLMRateLimiter(
                redis_client=redis_client_for_limiter,
                rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN,
                rate_limit_requests_per_min=getattr(self.config, 'LLM_RATE_LIMIT_REQUESTS_PER_MIN', 1000)
            )

            # Initialize Supabase client
            self.supabase_client = create_supabase_client()

            # Initialize storage manager
            self.storage_manager = create_storage_manager()
            
            logger.info("Cloud services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cloud services: {e}")
            raise
    
    def create_job(self, keywords: str, min_stars: int, max_repos: int,
                   custom_prompt: Optional[str] = None, debug: bool = False) -> JobRecord:
        """
        Create a new analysis job in the database.
        
        Args:
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            custom_prompt: Optional custom LLM prompt
            debug: Debug mode flag
            
        Returns:
            Created job record
        """
        try:
            job = JobRecord(
                id=self.job_id,
                keywords=keywords,
                min_stars=min_stars,
                max_repos=max_repos,
                status="pending",
                created_at=datetime.now().isoformat(),
                custom_prompt=custom_prompt,
                debug=debug
            )
            
            created_job = self.supabase_client.create_job(job)
            logger.info(f"Created job {self.job_id} in database")
            return created_job
            
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def add_repositories(self, repositories: List[Dict[str, Any]]) -> int:
        """
        Add repositories to the processing queue and database.
        
        Args:
            repositories: List of repository information dictionaries
            
        Returns:
            Number of repositories added
        """
        try:
            added_count = 0
            
            for repo_info in repositories:
                # Create repository record in database
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=self.job_id,
                    name=repo_info.get('full_name', 'unknown'),
                    url=repo_info.get('html_url', ''),
                    stars=repo_info.get('stargazers_count', 0),
                    file_size_mb=0.0  # Will be updated after repomix processing
                )
                
                self.supabase_client.create_repository(repo_record)
                
                # Add to Redis queue
                queue_item = RepositoryQueueItem(
                    repository_url=repo_info.get('html_url', ''),
                    repository_name=repo_info.get('full_name', 'unknown'),
                    job_id=self.job_id
                )
                
                self.repo_queue.put(queue_item)
                added_count += 1
            
            # Update job with total repositories
            self.supabase_client.update_job_status(
                self.job_id, 
                "running", 
                total_repositories=added_count
            )
            
            logger.info(f"Added {added_count} repositories to queue and database")
            return added_count
            
        except Exception as e:
            logger.error(f"Failed to add repositories: {e}")
            raise
    
    def start_workers(self):
        """Start repomix and LLM worker processes."""
        try:
            # Convert config to dict for passing to workers
            config_dict = {
                'REDIS_URL': self.config.REDIS_URL,
                'SUPABASE_URL': self.config.SUPABASE_URL,
                'SUPABASE_KEY': self.config.SUPABASE_KEY,
                'LLM_API_KEY': self.config.LLM_API_KEY,
                'LLM_BASE_URL': self.config.LLM_BASE_URL,
                'LLM_MODEL': self.config.LLM_MODEL,
                'FILE_INCLUDES': self.config.FILE_INCLUDES
            }

            # Start parallel repomix workers
            from parallel_repomix import run_parallel_repomix_worker

            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=run_parallel_repomix_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.repomix_processes.append(p)

            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=llm_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.llm_processes.append(p)

            logger.info(f"Started {len(self.repomix_processes)} repomix and {len(self.llm_processes)} LLM workers")

        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            raise


    
    def wait_for_completion(self, timeout: Optional[int] = None):
        """
        Wait for all processing to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
        """
        try:
            start_time = time.time()
            
            while True:
                # Check queue statistics
                stats = self.queue_manager.get_all_stats()
                repo_pending = stats.get('repositories', {}).get('pending', 0)
                file_pending = stats.get('files', {}).get('pending', 0)
                
                if repo_pending == 0 and file_pending == 0:
                    logger.info("All processing completed")
                    break
                
                if timeout and (time.time() - start_time) > timeout:
                    logger.warning(f"Timeout reached after {timeout} seconds")
                    break
                
                logger.info(f"Waiting... Repos: {repo_pending}, Files: {file_pending}")
                time.sleep(10)
            
        except Exception as e:
            logger.error(f"Error waiting for completion: {e}")
            raise
    
    def finalize_job(self) -> str:
        """
        Finalize the job by creating the final analysis file.
        
        Returns:
            URL of the final analysis file
        """
        try:
            # Get all analyses for this job
            analyses = self.supabase_client.get_analyses_by_job(self.job_id)
            repositories = self.supabase_client.get_repositories_by_job(self.job_id)
            
            # Calculate chunking statistics
            total_chunks = len(analyses)
            chunked_repos = len([r for r in repositories if r.file_size_mb and r.file_size_mb > 3.0])

            # Create final analysis data
            analysis_data = {
                "metadata": {
                    "job_id": self.job_id,
                    "generated": datetime.now().isoformat(),
                    "total_repositories": len(repositories),
                    "total_analyses": len(analyses),
                    "total_chunks": total_chunks,
                    "chunked_repositories": chunked_repos,
                    "processing_configuration": {
                        "repomix_workers": self.config.REPOMIX_WORKERS,
                        "llm_workers": self.config.LLM_WORKERS,
                        "deployment_mode": "unified",
                        "max_file_size_mb": self.config.MAX_FILE_SIZE_MB,
                        "max_chunks_retained": self.config.MAX_CHUNKS_TO_RETAIN
                    }
                },
                "analyses": []
            }
            
            # Group analyses by repository to handle chunks
            repo_analyses_map = {}
            for analysis in analyses:
                repo_id = analysis.repository_id
                if repo_id not in repo_analyses_map:
                    repo_analyses_map[repo_id] = []
                repo_analyses_map[repo_id].append(analysis)

            # Add grouped analyses to final data
            for repo in repositories:
                repo_analyses = repo_analyses_map.get(repo.id, [])

                if repo_analyses:
                    # Sort analyses by chunk_id (None values first for non-chunked)
                    repo_analyses.sort(key=lambda x: int(x.chunk_id) if x.chunk_id else 0)

                    # Create repository entry with all its chunks
                    repo_entry = {
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url,
                            "file_size_mb": repo.file_size_mb,
                            "total_chunks": len(repo_analyses)
                        },
                        "analysis_chunks": [],
                        "combined_analysis": ""
                    }

                    # Add each chunk analysis
                    combined_content = []
                    for analysis in repo_analyses:
                        chunk_entry = {
                            "chunk_id": analysis.chunk_id,
                            "worker_id": analysis.worker_id,
                            "processed_at": analysis.processed_at,
                            "analysis_length": analysis.analysis_length,
                            "content": analysis.analysis_content
                        }
                        repo_entry["analysis_chunks"].append(chunk_entry)
                        combined_content.append(analysis.analysis_content)

                    # Create combined analysis for easy reading
                    repo_entry["combined_analysis"] = "\n\n--- CHUNK SEPARATOR ---\n\n".join(combined_content)

                    analysis_data["analyses"].append(repo_entry)
                else:
                    # Repository without analysis (failed processing)
                    analysis_data["analyses"].append({
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url,
                            "file_size_mb": repo.file_size_mb,
                            "total_chunks": 0
                        },
                        "analysis_chunks": [],
                        "combined_analysis": "No analysis available - processing may have failed"
                    })
            
            # Upload final analysis file
            analysis_url = self.storage_manager.upload_analysis_file(
                job_id=self.job_id,
                analysis_data=analysis_data,
                filename="final_analysis.json"
            )
            
            # Update job status
            self.supabase_client.update_job_status(
                self.job_id,
                "completed",
                analysis_file_url=analysis_url
            )
            
            logger.info(f"Job {self.job_id} finalized with analysis file: {analysis_url}")
            return analysis_url
            
        except Exception as e:
            logger.error(f"Failed to finalize job: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise

    def _build_analysis_prompt(self, repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(self.config, 'CUSTOM_PROMPT') and self.config.CUSTOM_PROMPT:
            return self.config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    def stop_workers(self):
        """Stop all worker processes."""
        try:
            # Send poison pills to stop workers
            for _ in range(len(self.repomix_processes)):
                self.repo_queue.put(None)
            
            for _ in range(len(self.llm_processes)):
                self.file_queue.put(None)
            
            # Wait for processes to finish
            for p in self.repomix_processes + self.llm_processes:
                p.join(timeout=10)
                if p.is_alive():
                    p.terminate()
            
            logger.info("All workers stopped")
            
        except Exception as e:
            logger.error(f"Error stopping workers: {e}")
    
    def run(self, repositories: List[Dict[str, Any]], keywords: str, 
            min_stars: int, max_repos: int, **kwargs) -> str:
        """
        Run the complete cloud pipeline.
        
        Args:
            repositories: List of repository information
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            **kwargs: Additional job parameters
            
        Returns:
            URL of the final analysis file
        """
        try:
            # Create job
            self.create_job(keywords, min_stars, max_repos, **kwargs)
            
            # Add repositories
            self.add_repositories(repositories)
            
            # Start workers
            self.start_workers()
            
            # Wait for completion
            self.wait_for_completion(timeout=3600)  # 1 hour timeout
            
            # Finalize job
            analysis_url = self.finalize_job()
            
            return analysis_url
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
        finally:
            # Always stop workers
            self.stop_workers()
