import os


def get_file_size_mb(file_path):
    """
    Calculate file size in megabytes.

    Args:
        file_path (str): Path to the file

    Returns:
        float: File size in megabytes
    """
    size_bytes = os.path.getsize(file_path)
    size_mb = size_bytes / (1024 * 1024)
    return size_mb


def chunk_and_retain_file(file_path, max_size_mb=3, max_chunks=3):
    """
    Chunk a file into smaller parts if it exceeds max_size_mb.
    Only retain the first max_chunks parts and delete the original file.

    Args:
        file_path (str): Path to the file to chunk
        max_size_mb (int): Maximum size in MB before chunking (default: 3)
        max_chunks (int): Maximum number of chunks to retain (default: 3)

    Returns:
        list: List of chunk file paths if file was chunked, or [file_path] if not chunked
    """
    import logging
    logger = logging.getLogger(__name__)

    # Check if file exists
    if not os.path.exists(file_path):
        logger.warning(f"📏 CHUNKING FLOW: File not found: {file_path}")
        return []

    # Check if file needs chunking
    file_size_mb = get_file_size_mb(file_path)
    logger.info(f"📏 CHUNKING FLOW: File size check: {os.path.basename(file_path)} = {file_size_mb:.2f} MB")

    if file_size_mb <= max_size_mb:
        logger.info(f"✅ CHUNKING FLOW: File within limit ({file_size_mb:.2f} MB ≤ {max_size_mb} MB) - no chunking needed")
        return [file_path]

    logger.info(f"🔄 CHUNKING FLOW: File exceeds limit ({file_size_mb:.2f} MB > {max_size_mb} MB) - starting chunking")
    logger.info(f"📊 CHUNKING FLOW: Chunking parameters: {max_size_mb} MB per chunk, max {max_chunks} chunks")

    # Calculate chunk size in bytes
    chunk_size_bytes = max_size_mb * 1024 * 1024

    # Read file in binary mode and create chunks
    chunk_paths = []
    base_name = file_path

    try:
        with open(file_path, 'rb') as f:
            chunk_num = 0
            while True:
                chunk_data = f.read(chunk_size_bytes)
                if not chunk_data:
                    break

                # Only create chunks up to max_chunks
                if chunk_num < max_chunks:
                    chunk_path = f"{base_name}.chunk_{chunk_num}"
                    logger.info(f"📝 CHUNKING FLOW: Creating chunk {chunk_num + 1}/{max_chunks}: {os.path.basename(chunk_path)}")

                    with open(chunk_path, 'w', encoding='utf-8') as chunk_file:
                        # Convert binary data to string for UTF-8 encoding
                        try:
                            chunk_text = chunk_data.decode('utf-8')
                        except UnicodeDecodeError:
                            # If binary data can't be decoded, use latin-1 as fallback
                            chunk_text = chunk_data.decode('latin-1')
                        chunk_file.write(chunk_text)

                    chunk_size_actual = len(chunk_data) / (1024 * 1024)
                    logger.info(f"✅ CHUNKING FLOW: Chunk {chunk_num + 1} created: {chunk_size_actual:.2f} MB")
                    chunk_paths.append(chunk_path)
                else:
                    logger.info(f"⏭️ CHUNKING FLOW: Skipping chunk {chunk_num + 1} (exceeds max_chunks limit of {max_chunks})")

                chunk_num += 1

        # Delete the original file
        logger.info(f"🗑️ CHUNKING FLOW: Deleting original file: {os.path.basename(file_path)}")
        os.remove(file_path)

        logger.info(f"🎯 CHUNKING FLOW: Chunking complete: {len(chunk_paths)} chunks created from {file_size_mb:.2f} MB file")
        logger.info(f"📋 CHUNKING FLOW: Chunk files: {[os.path.basename(f) for f in chunk_paths]}")

        return chunk_paths

    except FileNotFoundError:
        # If original file doesn't exist, return empty list
        return []
    except Exception as e:
        # If any error occurs, clean up partial chunks and re-raise
        for chunk_path in chunk_paths:
            try:
                os.remove(chunk_path)
            except FileNotFoundError:
                pass
        raise e
