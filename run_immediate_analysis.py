#!/usr/bin/env python3
"""
Immediate Repository Analysis - Performs complete LLM analysis without Redis
by processing repositories directly in sequence.
"""

import os
import sys
import uuid
import json
import logging
import subprocess
import tempfile
import requests
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from github_search import search_repositories
from config import ScraperConfig

def process_repository_with_repomix(repo_url, repo_name, output_dir):
    """Process a single repository with repomix."""
    
    print(f"   📦 Processing {repo_name} with repomix...")
    
    try:
        # Create repomix output file
        safe_name = repo_name.replace('/', '_').replace('-', '_')
        repomix_file = output_dir / f"{safe_name}.md"
        
        # Run repomix command (use shell=True on Windows)
        cmd = f'npx repomix --remote "{repo_url}" --include "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md" --output "{repomix_file}"'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0 and repomix_file.exists():
            file_size = repomix_file.stat().st_size
            print(f"   ✅ Repomix completed: {file_size:,} bytes")
            return str(repomix_file)
        else:
            print(f"   ❌ Repomix failed: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ Repomix timeout for {repo_name}")
        return None
    except Exception as e:
        print(f"   ❌ Repomix error: {e}")
        return None

def analyze_with_llm(repomix_content, repo_name, config):
    """Analyze repository content with LLM."""
    
    print(f"   🤖 Analyzing {repo_name} with Gemini...")
    
    try:
        # Build analysis prompt
        prompt = f"""Please provide a comprehensive analysis of this repository: {repo_name}

Analyze the following repository content and provide:

1. **Primary Purpose & Functionality**
   - What does this repository do?
   - What problem does it solve?

2. **Key Features & Capabilities**
   - List all major features and functionalities
   - Include technical capabilities and tools provided

3. **Architecture & Implementation**
   - Programming languages used
   - Key dependencies and frameworks
   - Architecture patterns and design choices

4. **Usage & Applications**
   - How is this tool/library used?
   - Target audience and use cases
   - Integration methods

5. **Quality & Maintenance**
   - Code quality indicators
   - Documentation quality
   - Active maintenance status

Repository Content:
{repomix_content[:100000]}  # Limit to ~100k chars for token limits
"""

        # Prepare API request
        data = {
            'contents': [{'parts': [{'text': prompt}]}],
            'generationConfig': {
                'maxOutputTokens': config.LLM_MAX_OUTPUT_TOKENS,
                'temperature': 0.1
            }
        }

        # Make API request
        response = requests.post(
            f"{config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent?key={config.LLM_API_KEY}",
            json=data,
            timeout=config.LLM_TIMEOUT
        )

        if response.status_code == 200:
            result = response.json()
            analysis_content = result['candidates'][0]['content']['parts'][0]['text']
            print(f"   ✅ LLM analysis completed: {len(analysis_content):,} chars")
            return analysis_content
        else:
            print(f"   ❌ LLM API error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ LLM analysis error: {e}")
        return None

def main():
    """Run immediate analysis without Redis dependency."""
    
    print("🚀 IMMEDIATE REPOSITORY ANALYSIS - COMPLETE LLM PROCESSING")
    print("=" * 70)
    print("This will perform the FULL analysis pipeline:")
    print("1. Search GitHub for repositories")
    print("2. Process each repository with repomix")
    print("3. Analyze content with Gemini LLM")
    print("4. Generate comprehensive analysis files")
    print()
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = ScraperConfig()
        print("✅ Configuration loaded successfully")
        
        # Search for repositories (limit to 5 for immediate testing)
        print("\n🔍 Searching GitHub repositories...")
        keywords = "proxy list,free proxy,proxy scrape"
        
        repositories = search_repositories(
            keywords=keywords,
            min_stars=30,
            max_repos=5  # Limit for immediate testing
        )
        
        print(f"✅ Found {len(repositories)} repositories for immediate analysis")
        
        if not repositories:
            print("❌ No repositories found.")
            return False
        
        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        output_dir = Path(f"output/immediate_analysis_{timestamp}")
        repomix_dir = output_dir / "repomixes"
        repomix_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n📁 Output directory: {output_dir}")
        
        # Process each repository
        analysis_results = []
        
        for i, repo in enumerate(repositories):
            print(f"\n📊 Processing {i+1}/{len(repositories)}: {repo['name']} ({repo['stars']} stars)")
            
            # Process with repomix
            repomix_file = process_repository_with_repomix(
                repo['html_url'], 
                repo['full_name'], 
                repomix_dir
            )
            
            if repomix_file:
                # Read repomix content
                with open(repomix_file, 'r', encoding='utf-8') as f:
                    repomix_content = f.read()
                
                # Analyze with LLM
                analysis = analyze_with_llm(repomix_content, repo['full_name'], config)
                
                if analysis:
                    # Save individual analysis
                    analysis_result = {
                        "repository": {
                            "name": repo['full_name'],
                            "url": repo['html_url'],
                            "stars": repo['stars'],
                            "description": repo.get('description', ''),
                            "language": repo.get('language', 'Unknown')
                        },
                        "processing": {
                            "repomix_file": repomix_file,
                            "repomix_size": len(repomix_content),
                            "analysis_size": len(analysis),
                            "processed_at": datetime.now().isoformat()
                        },
                        "analysis": analysis
                    }
                    
                    analysis_results.append(analysis_result)
                    
                    # Save individual analysis file
                    safe_name = repo['full_name'].replace('/', '_')
                    analysis_file = output_dir / f"{safe_name}_analysis.json"
                    with open(analysis_file, 'w', encoding='utf-8') as f:
                        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
                    
                    print(f"   💾 Analysis saved: {analysis_file}")
        
        # Create comprehensive analysis file
        comprehensive_analysis = {
            "metadata": {
                "generated": datetime.now().isoformat(),
                "keywords": keywords,
                "total_repositories": len(repositories),
                "successful_analyses": len(analysis_results),
                "processing_mode": "immediate_analysis"
            },
            "summary": {
                "repositories_processed": len(analysis_results),
                "total_repomix_size": sum(r["processing"]["repomix_size"] for r in analysis_results),
                "total_analysis_size": sum(r["processing"]["analysis_size"] for r in analysis_results),
                "languages": {},
                "average_stars": sum(r["repository"]["stars"] for r in analysis_results) / len(analysis_results) if analysis_results else 0
            },
            "analyses": analysis_results
        }
        
        # Update language summary
        for result in analysis_results:
            lang = result["repository"]["language"]
            comprehensive_analysis["summary"]["languages"][lang] = comprehensive_analysis["summary"]["languages"].get(lang, 0) + 1
        
        # Save comprehensive analysis
        final_analysis_file = output_dir / "comprehensive_llm_analysis.json"
        with open(final_analysis_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_analysis, f, indent=2, ensure_ascii=False)
        
        print(f"\n🎉 IMMEDIATE ANALYSIS COMPLETED!")
        print(f"   Repositories processed: {len(analysis_results)}/{len(repositories)}")
        print(f"   Comprehensive analysis: {final_analysis_file}")
        print(f"   Individual analyses: {len(analysis_results)} files")
        print(f"   Repomix files: {repomix_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Immediate analysis failed: {e}")
        logger.error(f"Immediate analysis failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
