#!/usr/bin/env python3
"""
Test the real GitHub search functionality to see how many repositories
we actually find for proxy-related keywords.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from github_search import search_repositories

def main():
    print('🔍 REAL GITHUB SEARCH TEST')
    print('=' * 50)
    print('Testing actual GitHub API search vs hardcoded test data')
    print()

    # Test the actual search function with the same parameters
    keywords = ['proxy list', 'free proxy', 'proxy scrape']
    total_found = 0
    
    for keyword in keywords:
        print(f'Searching for: "{keyword}"')
        print(f'Parameters: min_stars=30, max_repos=60')
        
        try:
            repos = search_repositories(
                keywords=keyword,
                min_stars=30,
                max_repos=60
            )
            
            print(f'✅ Found: {len(repos)} repositories')
            total_found += len(repos)
            
            if repos:
                print('   Top repositories:')
                for i, repo in enumerate(repos[:5]):
                    print(f'     {i+1}. {repo["name"]} ({repo["stars"]} stars)')
                if len(repos) > 5:
                    print(f'     ... and {len(repos)-5} more')
            else:
                print('   No repositories found')
                
        except Exception as e:
            print(f'❌ Error searching for "{keyword}": {e}')
        
        print()

    print('=' * 50)
    print(f'🎯 SUMMARY:')
    print(f'   Expected total: ~180 repositories (60 per keyword × 3)')
    print(f'   Actual total: {total_found} repositories')
    print(f'   Test script used: 5 hardcoded repositories')
    print()
    
    if total_found < 50:
        print('⚠️  LOW COUNT ANALYSIS:')
        print('   - Keywords may be too specific')
        print('   - GitHub API rate limits may be active')
        print('   - Search strategy may need optimization')
        print()
        print('💡 RECOMMENDED BROADER KEYWORDS:')
        print('   - "proxy" (instead of "proxy list")')
        print('   - "socks5"')
        print('   - "http proxy"')
        print('   - "proxy server"')
        print('   - "proxy scraper"')
    else:
        print('✅ Search results look normal for these specific keywords')

if __name__ == '__main__':
    main()
