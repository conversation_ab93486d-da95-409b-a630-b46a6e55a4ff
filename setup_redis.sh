#!/bin/bash
# Redis Setup Script for Repository Research Tool
# Based on Redis documentation from Context7

echo "🔧 REDIS SETUP FOR REPOSITORY RESEARCH TOOL"
echo "============================================"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

echo "📋 Redis Setup Options:"
echo "1. Local Redis (Docker container)"
echo "2. Redis Stack (with RedisInsight GUI)"
echo "3. Use existing Redis URL"
echo ""

read -p "Choose option (1-3): " choice

case $choice in
    1)
        echo "🐳 Setting up local Redis container..."
        
        # Stop existing Redis container if running
        docker stop redis-repo-tool 2>/dev/null || true
        docker rm redis-repo-tool 2>/dev/null || true
        
        # Run Redis container
        echo "🚀 Starting Redis container..."
        docker run -d \
            --name redis-repo-tool \
            -p 6379:6379 \
            redis:latest
        
        if [ $? -eq 0 ]; then
            echo "✅ Redis container started successfully"
            echo "📡 Redis URL: redis://localhost:6379"
            
            # Update .env file
            if grep -q "REDIS_URL=" .env; then
                sed -i 's|# REDIS_URL=.*|REDIS_URL=redis://localhost:6379|' .env
                sed -i 's|REDIS_URL=.*|REDIS_URL=redis://localhost:6379|' .env
            else
                echo "REDIS_URL=redis://localhost:6379" >> .env
            fi
            
            echo "✅ Updated .env file with Redis URL"
        else
            echo "❌ Failed to start Redis container"
            exit 1
        fi
        ;;
        
    2)
        echo "🐳 Setting up Redis Stack with RedisInsight..."
        
        # Stop existing containers if running
        docker stop redis-stack-repo-tool 2>/dev/null || true
        docker rm redis-stack-repo-tool 2>/dev/null || true
        
        # Run Redis Stack container
        echo "🚀 Starting Redis Stack container..."
        docker run -d \
            --name redis-stack-repo-tool \
            -p 6379:6379 \
            -p 8001:8001 \
            redis/redis-stack:latest
        
        if [ $? -eq 0 ]; then
            echo "✅ Redis Stack container started successfully"
            echo "📡 Redis URL: redis://localhost:6379"
            echo "🖥️  RedisInsight GUI: http://localhost:8001"
            
            # Update .env file
            if grep -q "REDIS_URL=" .env; then
                sed -i 's|# REDIS_URL=.*|REDIS_URL=redis://localhost:6379|' .env
                sed -i 's|REDIS_URL=.*|REDIS_URL=redis://localhost:6379|' .env
            else
                echo "REDIS_URL=redis://localhost:6379" >> .env
            fi
            
            echo "✅ Updated .env file with Redis URL"
        else
            echo "❌ Failed to start Redis Stack container"
            exit 1
        fi
        ;;
        
    3)
        echo "🔗 Using existing Redis URL..."
        read -p "Enter your Redis URL (e.g., redis://your-host:6379): " redis_url
        
        if [ -z "$redis_url" ]; then
            echo "❌ Redis URL cannot be empty"
            exit 1
        fi
        
        # Update .env file
        if grep -q "REDIS_URL=" .env; then
            sed -i "s|# REDIS_URL=.*|REDIS_URL=$redis_url|" .env
            sed -i "s|REDIS_URL=.*|REDIS_URL=$redis_url|" .env
        else
            echo "REDIS_URL=$redis_url" >> .env
        fi
        
        echo "✅ Updated .env file with Redis URL: $redis_url"
        ;;
        
    *)
        echo "❌ Invalid option selected"
        exit 1
        ;;
esac

# Test Redis connection
echo ""
echo "🧪 Testing Redis connection..."

# Install redis-py if not available
python -c "import redis" 2>/dev/null || pip install redis

# Test connection
python -c "
import redis
import os
from dotenv import load_dotenv

load_dotenv()
redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')

try:
    r = redis.from_url(redis_url)
    r.ping()
    print('✅ Redis connection successful!')
    
    # Test basic operations
    r.set('test_key', 'Repository Research Tool')
    value = r.get('test_key').decode('utf-8')
    r.delete('test_key')
    
    print(f'✅ Redis operations working: {value}')
    
except Exception as e:
    print(f'❌ Redis connection failed: {e}')
    exit(1)
"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 REDIS SETUP COMPLETE!"
    echo "========================"
    echo "✅ Redis is running and accessible"
    echo "✅ Environment configured"
    echo "✅ Connection tested successfully"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Set DEPLOYMENT_MODE=cloud in .env to enable cloud features"
    echo "   2. Run: python setup_cloud_infrastructure.py"
    echo "   3. Deploy with: docker run --env-file .env repository-research-tool:latest"
else
    echo "❌ Redis setup failed"
    exit 1
fi
