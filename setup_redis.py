#!/usr/bin/env python3
"""
Quick Redis setup for local development
"""

import subprocess
import time
import requests
import os

def setup_redis():
    """Set up Redis for local development."""
    
    print("🔧 SETTING UP REDIS FOR FULL ANALYSIS")
    print("=" * 50)
    
    # Try to start Redis with Docker
    print("1. Attempting to start Redis with Docker...")
    
    try:
        # Check if Docker is available
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Docker not available")
            return setup_cloud_redis()
        
        print("✅ Docker available")
        
        # Start Redis container
        print("   Starting Redis container...")
        result = subprocess.run([
            "docker", "run", "-d", 
            "--name", "repo-research-redis",
            "-p", "6379:6379",
            "redis:7-alpine"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Redis container started successfully")
            time.sleep(3)  # Wait for Redis to start
            
            # Test connection
            try:
                import redis
                client = redis.from_url("redis://localhost:6379")
                client.ping()
                print("✅ Redis connection test successful")
                return True
            except Exception as e:
                print(f"❌ Redis connection test failed: {e}")
                return False
        else:
            print(f"❌ Failed to start Redis container: {result.stderr}")
            return setup_cloud_redis()
            
    except Exception as e:
        print(f"❌ Docker setup failed: {e}")
        return setup_cloud_redis()

def setup_cloud_redis():
    """Set up cloud Redis service."""
    
    print("\n2. Setting up cloud Redis service...")
    print("   Recommended: Upstash Redis (free tier)")
    print("   Visit: https://upstash.com/")
    print()
    print("   Steps:")
    print("   1. Create free account at upstash.com")
    print("   2. Create new Redis database")
    print("   3. Copy the Redis URL")
    print("   4. Update .env file: REDIS_URL=redis://...")
    print()
    print("   Alternative: Redis Labs, Railway, or other cloud Redis")
    
    return False

if __name__ == '__main__':
    setup_redis()
