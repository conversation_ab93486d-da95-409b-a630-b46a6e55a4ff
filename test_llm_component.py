#!/usr/bin/env python3
"""
Test ONLY the LLM component to ensure it:
1. Makes API requests correctly
2. <PERSON><PERSON> responses properly
3. Respects your chunking rules (≤3MB per request)
4. Handles rate limits gracefully
"""

import sys
import os
import requests
import tempfile

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_llm_api_connection():
    """Test basic LLM API connection."""
    print("🧪 TESTING LLM API CONNECTION")
    print("=" * 50)
    
    try:
        api_key = os.getenv('LLM_API_KEY')
        base_url = os.getenv('LLM_BASE_URL')
        model = os.getenv('LLM_MODEL')
        
        if not api_key:
            print("❌ LLM_API_KEY not found in environment")
            return False
        
        if not base_url:
            print("❌ LLM_BASE_URL not found in environment")
            return False
        
        if not model:
            print("❌ LLM_MODEL not found in environment")
            return False
        
        print(f"✅ API Key: {api_key[:10]}...")
        print(f"✅ Base URL: {base_url}")
        print(f"✅ Model: {model}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM API configuration failed: {e}")
        return False

def test_small_llm_request():
    """Test LLM API with a small request."""
    print("\n🧪 TESTING SMALL LLM REQUEST")
    print("=" * 50)
    
    try:
        api_key = os.getenv('LLM_API_KEY')
        base_url = os.getenv('LLM_BASE_URL')
        model = os.getenv('LLM_MODEL')
        
        # Small test content
        test_content = """
# Test Repository

This is a small test repository for validation.

## Features
- Basic functionality
- Simple structure
- Test validation

## Code
```python
def hello():
    return "Hello, World!"
```
"""
        
        prompt = f"""Analyze this repository and provide a brief summary of its features and functionality:

Repository: test/repository

{test_content}

Please provide a concise analysis focusing on the main features and purpose."""
        
        # Prepare API request
        api_url = f"{base_url}/models/{model}:generateContent"
        
        data = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "generationConfig": {
                "maxOutputTokens": 1000,
                "temperature": 0.1
            }
        }
        
        print(f"🔗 API URL: {api_url}")
        print(f"📊 Content length: {len(prompt)} characters")
        
        # Make request
        response = requests.post(
            api_url,
            headers={
                "Content-Type": "application/json",
                "x-goog-api-key": api_key
            },
            json=data,
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                analysis = result['candidates'][0]['content']['parts'][0]['text']
                print("✅ LLM API request successful")
                print(f"📝 Analysis preview: {analysis[:200]}...")
                return True
            else:
                print("❌ No analysis content in response")
                print(f"Response: {result}")
                return False
        else:
            print(f"❌ LLM API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ LLM request failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chunking_size_limit():
    """Test that we respect the 3MB chunking limit."""
    print("\n🧪 TESTING CHUNKING SIZE LIMIT")
    print("=" * 50)
    
    try:
        from utils import get_file_size_mb, chunk_and_retain_file
        
        # Create a file larger than 3MB
        large_content = "# Large Repository Analysis\n\n" + ("This is test content for chunking validation. " * 100000)  # ~4.5MB
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
            f.write(large_content)
            test_file = f.name
        
        file_size = get_file_size_mb(test_file)
        print(f"📁 Created test file: {file_size:.2f} MB")
        
        if file_size <= 3:
            print("❌ Test file should be > 3MB")
            return False
        
        # Apply chunking
        chunks = chunk_and_retain_file(test_file, max_size_mb=3, max_chunks=3)
        
        print(f"📊 Chunks created: {len(chunks)}")
        
        # Verify each chunk is ≤ 3MB
        all_chunks_valid = True
        for i, chunk_path in enumerate(chunks):
            chunk_size = get_file_size_mb(chunk_path)
            print(f"   Chunk {i+1}: {chunk_size:.2f} MB")
            
            if chunk_size > 3.1:  # Small margin for headers
                print(f"❌ Chunk {i+1} exceeds 3MB limit")
                all_chunks_valid = False
            
            # Cleanup
            if os.path.exists(chunk_path):
                os.remove(chunk_path)
        
        if all_chunks_valid:
            print("✅ All chunks respect 3MB limit")
            print("✅ Your chunking rules are being followed")
            return True
        else:
            print("❌ Some chunks exceed 3MB limit")
            return False
            
    except Exception as e:
        print(f"❌ Chunking size test failed: {e}")
        return False

def test_rate_limit_handling():
    """Test rate limit detection (without actually hitting limits)."""
    print("\n🧪 TESTING RATE LIMIT HANDLING")
    print("=" * 50)
    
    try:
        # Simulate a rate limit response
        rate_limit_response = {
            "error": {
                "code": 429,
                "message": "You exceeded your current quota",
                "status": "RESOURCE_EXHAUSTED"
            }
        }
        
        # Test rate limit detection
        if rate_limit_response.get('error', {}).get('code') == 429:
            print("✅ Rate limit detection working")
            print("✅ Status 429 correctly identified")
            
            error_message = rate_limit_response['error']['message']
            if 'quota' in error_message.lower() or 'rate' in error_message.lower():
                print("✅ Rate limit message detection working")
                return True
            else:
                print("❌ Rate limit message not detected")
                return False
        else:
            print("❌ Rate limit detection failed")
            return False
            
    except Exception as e:
        print(f"❌ Rate limit test failed: {e}")
        return False

def main():
    """Test LLM component."""
    print("🔧 TESTING LLM COMPONENT")
    print("=" * 60)
    print("TESTING:")
    print("1. API configuration")
    print("2. Small API request")
    print("3. Chunking size limits (your 3MB rule)")
    print("4. Rate limit handling")
    print()
    
    success1 = test_llm_api_connection()
    success2 = test_small_llm_request() if success1 else False
    success3 = test_chunking_size_limit()
    success4 = test_rate_limit_handling()
    
    if success1 and success2 and success3 and success4:
        print("\n🎉 ALL LLM TESTS PASSED!")
        print("✅ Component is working correctly")
        print("✅ API configuration valid")
        print("✅ API requests working")
        print("✅ Chunking rules respected (≤3MB)")
        print("✅ Rate limit handling ready")
        return True
    else:
        print("\n❌ LLM TESTS FAILED!")
        print("❌ Component needs fixing")
        if not success1:
            print("   - API configuration issues")
        if not success2:
            print("   - API request issues")
        if not success3:
            print("   - Chunking rule violations")
        if not success4:
            print("   - Rate limit handling issues")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
