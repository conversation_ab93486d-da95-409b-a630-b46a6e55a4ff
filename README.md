# 🔍 Repository Research Tool v2.0.0

**AI-Powered Repository Analysis with Cloud-Native Architecture**

A comprehensive tool that searches GitHub repositories by keywords, processes their content with repomix, and generates detailed AI analysis using Gemini 2.0 Flash. Features per-keyword search logic, 3MB chunking system, and cloud-ready infrastructure.

## 🎯 **Key Features**

### **✅ Core Functionality**
- **Per-keyword search**: `max_repos` applied per keyword (not total)
- **GitHub integration**: Searches repositories with star filtering
- **Repomix processing**: Extracts and formats repository content
- **AI analysis**: Gemini 2.0 Flash generates comprehensive summaries
- **3MB chunking**: Automatic file splitting with max 3 chunks per repo
- **Custom prompts**: Targeted analysis for specific use cases

### **✅ Architecture Options**
- **Local execution**: Direct Python script execution
- **Containerized service**: Docker-based REST API
- **Cloud infrastructure**: Redis queues + Supabase database

### **✅ Enhanced Monitoring**
- **Complete flow tracking**: Every step logged with flow-specific prefixes
- **Real-time monitoring**: Job status and progress tracking
- **Quality metrics**: Analysis length and processing statistics
- **Error handling**: Comprehensive error capture and reporting

## 🚀 **Quick Start**

### **Option 1: Local Execution**
```bash
# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your API keys

# Run analysis
python main.py --keywords "python cli,golang tool" --min-stars 100 --max-repos 5
```

### **Option 2: Docker Service**
```bash
# Build container
docker build -f Dockerfile.production -t repository-research-tool:v2.0 .

# Run service
docker run -p 8080:8080 --env-file .env repository-research-tool:v2.0

# Use REST API
curl -X POST http://localhost:8080/start \
  -H 'Content-Type: application/json' \
  -d '{"keywords": "python cli", "min_stars": 100, "max_repos": 5}'
```

### **Option 3: Cloud Infrastructure**
```bash
# Set up Redis and Supabase
python setup_cloud_infrastructure.py

# Enable cloud mode
echo "DEPLOYMENT_MODE=cloud" >> .env

# Deploy with cloud features
docker run -p 8080:8080 --env-file .env repository-research-tool:v2.0
```

## 📊 **How It Works**

### **1. Repository Discovery**
```
Keywords: "python cli,golang tool"
Max repos: 5 per keyword

GitHub Search:
├── "python cli" → Find 5 repositories
├── "golang tool" → Find 5 repositories
└── Deduplicate → Final list (≤10 repositories)
```

### **2. Content Processing**
```
For each repository:
├── Repomix extraction → Single markdown file
├── Size check → If >3MB, chunk into max 3 parts
└── Queue for analysis → Add to processing queue
```

### **3. AI Analysis**
```
For each file/chunk:
├── Gemini 2.0 Flash → Generate comprehensive analysis
├── Quality check → Validate analysis length
└── Store results → Save to analysis file
```

### **4. Result Generation**
```
Final output:
├── analysis.json → Complete structured results
├── repomixes/ → Individual repository content
└── logs/ → Processing logs and metrics
```

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Required
LLM_API_KEY=your_gemini_api_key
GITHUB_TOKEN=your_github_token

# Optional Cloud Infrastructure
DEPLOYMENT_MODE=cloud
REDIS_URL=redis://localhost:6379
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_key

# Processing Configuration
MAX_FILE_SIZE_MB=3
MAX_CHUNKS_TO_RETAIN=3
REPOMIX_WORKERS=15
LLM_WORKERS=4
```

### **Command Line Options**
```bash
python main.py \
  --keywords "keyword1,keyword2" \
  --min-stars 100 \
  --max-repos 5 \
  --custom-prompt "Analyze the main features..." \
  --debug
```

### **REST API Endpoints**
```bash
GET  /health              # Service status
GET  /config              # Service configuration
GET  /jobs                # List all jobs
POST /start               # Start new analysis
GET  /status/{job_id}     # Job status and results
```

## 📁 **Project Structure**

```
repository-research-tool/
├── src/                          # Core source code
│   ├── config.py                 # Configuration management
│   ├── github_search.py          # GitHub API integration
│   ├── pipeline.py               # Main processing pipeline
│   ├── utils.py                  # Utility functions (chunking)
│   ├── cloud_pipeline.py         # Cloud-native processing
│   ├── redis_queue.py            # Redis queue management
│   ├── supabase_client.py        # Supabase integration
│   └── storage_manager.py        # File storage management
├── main.py                       # Local execution entry point
├── service.py                    # REST API service
├── worker.py                     # Background worker processes
├── Dockerfile.production         # Production container
├── docker-compose.yml            # Multi-service deployment
├── requirements.txt              # Python dependencies
├── .env                          # Environment configuration
├── DEPLOYMENT.md                 # Deployment guide
└── output/                       # Analysis results
    └── YYYYMMDD_HHMMSS_keywords/
        ├── analysis.json         # Structured results
        ├── repomixes/           # Repository content
        └── logs/                # Processing logs
```

## 🎯 **Usage Examples**

### **Basic Analysis**
```bash
python main.py --keywords "redis" --min-stars 1000 --max-repos 3
```

### **Multi-keyword with Custom Prompt**
```bash
python main.py \
  --keywords "proxy list,free proxy" \
  --min-stars 30 \
  --max-repos 10 \
  --custom-prompt "Analyze if this is a static proxy list or runtime scraper"
```

### **Service API Usage**
```bash
# Start job
curl -X POST http://localhost:8080/start \
  -H 'Content-Type: application/json' \
  -d '{
    "keywords": "ai agent,machine learning",
    "min_stars": 100,
    "max_repos": 5,
    "custom_prompt": "Analyze the AI capabilities and features"
  }'

# Check status
curl http://localhost:8080/status/{job_id}

# List all jobs
curl http://localhost:8080/jobs
```

---

*For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md)*
