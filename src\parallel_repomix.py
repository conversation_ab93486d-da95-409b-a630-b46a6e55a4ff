#!/usr/bin/env python3
"""
Parallel Repomix Processing System for Repository Research Tool

Implements 30 parallel repomix workers with Python multiprocessing for high-throughput
repository processing as specified in user requirements.

Key Features:
- 30 workers with Python multiprocessing (not async)
- npx repomix --remote processing without cloning
- File includes: --include '**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md'
- Redis queue distribution
- Processing rate optimization
- Error handling and retry logic
"""

import os
import sys
import time
import shutil
import tempfile
import subprocess
import multiprocessing
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

from queue_manager import get_queue_manager, RepositoryQueueItem, FileQueueItem
from storage_manager import StorageManager
from utils import get_file_size_mb
from config import ScraperConfig

logger = logging.getLogger(__name__)

# Parallel processing configuration
DEFAULT_REPOMIX_WORKERS = 30  # 30 workers as specified
DEFAULT_PROCESSING_TIMEOUT = 600  # 10 minutes per repository
DEFAULT_RETRY_ATTEMPTS = 3  # Retry failed repositories

class ParallelRepomixProcessor:
    """
    High-performance parallel repomix processing system.
    """
    
    def __init__(self, config: ScraperConfig, job_id: str, num_workers: int = DEFAULT_REPOMIX_WORKERS):
        """
        Initialize parallel repomix processor.
        
        Args:
            config: Configuration object
            job_id: Unique job identifier
            num_workers: Number of parallel workers (default: 30)
        """
        self.config = config
        self.job_id = job_id
        self.num_workers = num_workers
        
        # Initialize queue manager
        self.queue_manager = get_queue_manager(config.REDIS_URL)
        self.repo_queue = self.queue_manager.get_repository_queue()
        self.repomix_validated_queue = self.queue_manager.get_repomix_validated_queue()
        
        # Initialize storage manager
        self.storage_manager = StorageManager(config)
        
        # Worker process tracking
        self.worker_processes: List[multiprocessing.Process] = []
        self.worker_stats = multiprocessing.Manager().dict()
        
        # File includes as specified
        self.file_includes = config.FILE_INCLUDES or "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"
        
        logger.info(f"🔧 Parallel Repomix Processor initialized")
        logger.info(f"   Workers: {num_workers}")
        logger.info(f"   Job ID: {job_id}")
        logger.info(f"   File includes: {self.file_includes}")
        logger.info(f"   Redis URL: {config.REDIS_URL}")
    
    def start_workers(self):
        """Start all parallel repomix workers."""
        logger.info(f"🚀 Starting {self.num_workers} parallel repomix workers...")

        try:
            # Convert config to dict for passing to workers (avoid pickling issues)
            config_dict = {
                'REDIS_URL': self.config.REDIS_URL,
                'SUPABASE_URL': self.config.SUPABASE_URL,
                'SUPABASE_KEY': self.config.SUPABASE_KEY,
                'FILE_INCLUDES': self.config.FILE_INCLUDES,
                'STORAGE_BUCKET_REPOMIXES': self.config.STORAGE_BUCKET_REPOMIXES
            }

            # Start worker processes
            for worker_id in range(self.num_workers):
                process = multiprocessing.Process(
                    target=run_parallel_repomix_worker,
                    args=(worker_id, config_dict, self.job_id),
                    name=f"RepomixWorker-{worker_id}"
                )
                process.start()
                self.worker_processes.append(process)

                # Initialize worker stats
                self.worker_stats[worker_id] = {
                    'processed': 0,
                    'failed': 0,
                    'started_at': datetime.now().isoformat(),
                    'last_activity': datetime.now().isoformat()
                }

            logger.info(f"✅ Started {len(self.worker_processes)} repomix workers")

        except Exception as e:
            logger.error(f"❌ Failed to start repomix workers: {e}")
            self.stop_workers()
            raise
    
    def stop_workers(self):
        """Stop all repomix workers."""
        logger.info(f"🛑 Stopping {len(self.worker_processes)} repomix workers...")
        
        # Send termination signals
        for _ in range(len(self.worker_processes)):
            self.repo_queue.put(None)  # Sentinel value
        
        # Wait for workers to finish
        for process in self.worker_processes:
            if process.is_alive():
                process.join(timeout=30)
                if process.is_alive():
                    logger.warning(f"Force terminating worker {process.name}")
                    process.terminate()
                    process.join(timeout=5)
        
        self.worker_processes.clear()
        logger.info("✅ All repomix workers stopped")
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """Get statistics for all workers."""
        stats = dict(self.worker_stats)
        
        total_processed = sum(worker['processed'] for worker in stats.values())
        total_failed = sum(worker['failed'] for worker in stats.values())
        active_workers = len([w for w in stats.values() if w['processed'] > 0 or w['failed'] > 0])
        
        return {
            'total_workers': self.num_workers,
            'active_workers': active_workers,
            'total_processed': total_processed,
            'total_failed': total_failed,
            'success_rate': (total_processed / (total_processed + total_failed) * 100) if (total_processed + total_failed) > 0 else 0,
            'worker_details': stats
        }
    
    def _repomix_worker(self, worker_id: int):
        """
        Individual repomix worker process.
        
        Args:
            worker_id: Unique worker identifier
        """
        logger.info(f"🔧 Repomix Worker {worker_id}: Starting")
        
        try:
            # Verify npx is available
            npx_path = shutil.which('npx')
            if not npx_path:
                logger.error(f"Repomix Worker {worker_id}: npx not found in PATH")
                return
            
            logger.info(f"Repomix Worker {worker_id}: Using npx at {npx_path}")
            
            # Worker processing loop
            while True:
                try:
                    # Get repository from queue
                    repo_item = self.repo_queue.get(timeout=30)
                    if repo_item is None:
                        logger.info(f"Repomix Worker {worker_id}: Received termination signal")
                        break
                    
                    # Update worker activity
                    self.worker_stats[worker_id]['last_activity'] = datetime.now().isoformat()
                    
                    # Process repository
                    success = self._process_repository(worker_id, repo_item)
                    
                    # Update statistics
                    if success:
                        self.worker_stats[worker_id]['processed'] += 1
                    else:
                        self.worker_stats[worker_id]['failed'] += 1
                    
                    # Mark task as completed
                    self.repo_queue.task_done(repo_item)
                    
                except Exception as e:
                    logger.error(f"Repomix Worker {worker_id}: Error in processing loop: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Repomix Worker {worker_id}: Fatal error: {e}")
        finally:
            logger.info(f"Repomix Worker {worker_id}: Stopped")
    
    def _process_repository(self, worker_id: int, repo_item: RepositoryQueueItem) -> bool:
        """
        Process a single repository with repomix.
        
        Args:
            worker_id: Worker identifier
            repo_item: Repository to process
            
        Returns:
            bool: True if successful, False otherwise
        """
        repo_name = repo_item.repository_name
        repo_url = repo_item.repository_url
        
        logger.info(f"Repomix Worker {worker_id}: Processing {repo_name}")
        
        try:
            # Create temporary directory for this worker
            temp_dir = Path(tempfile.gettempdir()) / f"repomix_worker_{worker_id}" / self.job_id
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            # Create safe filename
            safe_repo_name = repo_name.replace('/', '_').replace('\\', '_')
            local_output_file = temp_dir / f"{safe_repo_name}.md"
            
            # Build repomix command with specified file includes
            cmd = [
                shutil.which('npx'), 'repomix', '--remote', repo_url,
                '--include', self.file_includes,
                '--output', str(local_output_file)
            ]
            
            logger.info(f"Repomix Worker {worker_id}: Running repomix for {repo_name}")
            logger.debug(f"Repomix Worker {worker_id}: Command: {' '.join(cmd)}")
            
            # Execute repomix command
            start_time = time.time()
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=DEFAULT_PROCESSING_TIMEOUT,
                encoding='utf-8',
                errors='replace'
            )
            duration = time.time() - start_time
            
            # Check if repomix succeeded
            if result.returncode != 0 or not local_output_file.exists():
                logger.error(f"Repomix Worker {worker_id}: Failed {repo_name} (exit code: {result.returncode})")
                logger.error(f"Repomix Worker {worker_id}: Error output: {result.stderr[:500]}")
                
                # Clean up
                if local_output_file.exists():
                    local_output_file.unlink()
                
                return False
            
            # Get file size
            file_size_mb = get_file_size_mb(str(local_output_file))
            
            # Read repomix content
            with open(local_output_file, 'r', encoding='utf-8') as f:
                repomix_content = f.read()
            
            logger.info(f"Repomix Worker {worker_id}: Completed {repo_name} ({file_size_mb:.2f}MB, {duration:.1f}s)")
            
            # Upload to cloud storage
            repomix_filename = f"{safe_repo_name}.md"
            repomix_url = self.storage_manager.upload_repomix_file(
                self.job_id, repomix_filename, repomix_content
            )
            
            logger.info(f"Repomix Worker {worker_id}: Uploaded {repo_name} to {repomix_url}")
            
            # Queue for chunking worker
            repomix_validated_item = FileQueueItem(
                file_path=repomix_url,
                repository_name=repo_name,
                job_id=self.job_id
            )
            self.repomix_validated_queue.put(repomix_validated_item)
            
            logger.info(f"Repomix Worker {worker_id}: Queued {repo_name} for chunking")
            
            # Clean up local file
            local_output_file.unlink()
            
            return True
            
        except subprocess.TimeoutExpired:
            logger.error(f"Repomix Worker {worker_id}: Timeout processing {repo_name} (>{DEFAULT_PROCESSING_TIMEOUT}s)")
            return False
        except Exception as e:
            logger.error(f"Repomix Worker {worker_id}: Exception processing {repo_name}: {e}")
            return False


def run_parallel_repomix_worker(worker_id: int, config_dict: Dict[str, Any], job_id: str):
    """
    Entry point for running a parallel repomix worker.

    Args:
        worker_id: Worker identifier
        config_dict: Configuration dictionary
        job_id: Job identifier
    """
    try:
        logger.info(f"🔧 Parallel Repomix Worker {worker_id}: Starting")

        # Initialize queue manager and storage manager directly
        from queue_manager import get_queue_manager
        from storage_manager import StorageManager

        # Create config object
        config = ScraperConfig()
        for key, value in config_dict.items():
            setattr(config, key, value)

        # Initialize services
        queue_manager = get_queue_manager(config.REDIS_URL)
        repo_queue = queue_manager.get_repository_queue()
        repomix_validated_queue = queue_manager.get_repomix_validated_queue()
        storage_manager = StorageManager(config)

        # File includes
        file_includes = config.FILE_INCLUDES or "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"

        # Verify npx is available
        npx_path = shutil.which('npx')
        if not npx_path:
            logger.error(f"Parallel Repomix Worker {worker_id}: npx not found in PATH")
            return

        logger.info(f"Parallel Repomix Worker {worker_id}: Using npx at {npx_path}")

        # Worker processing loop
        while True:
            try:
                # Get repository from queue
                repo_item = repo_queue.get(timeout=30)
                if repo_item is None:
                    logger.info(f"Parallel Repomix Worker {worker_id}: Received termination signal")
                    break

                # Process repository
                success = _process_repository_worker(
                    worker_id, repo_item, storage_manager,
                    repomix_validated_queue, job_id, file_includes
                )

                # Mark task as completed
                repo_queue.task_done(repo_item)

            except Exception as e:
                logger.error(f"Parallel Repomix Worker {worker_id}: Error in processing loop: {e}")
                continue

        logger.info(f"Parallel Repomix Worker {worker_id}: Stopped")

    except Exception as e:
        logger.error(f"Parallel repomix worker {worker_id} failed: {e}")


def _process_repository_worker(worker_id: int, repo_item, storage_manager,
                              repomix_validated_queue, job_id: str, file_includes: str) -> bool:
    """
    Process a single repository with repomix (worker function).

    Args:
        worker_id: Worker identifier
        repo_item: Repository to process
        storage_manager: Storage manager instance
        repomix_validated_queue: Queue for validated repomix files
        job_id: Job identifier
        file_includes: File include patterns

    Returns:
        bool: True if successful, False otherwise
    """
    repo_name = repo_item.repository_name
    repo_url = repo_item.repository_url

    logger.info(f"Parallel Repomix Worker {worker_id}: Processing {repo_name}")

    try:
        # Create temporary directory for this worker
        temp_dir = Path(tempfile.gettempdir()) / f"repomix_worker_{worker_id}" / job_id
        temp_dir.mkdir(parents=True, exist_ok=True)

        # Create safe filename
        safe_repo_name = repo_name.replace('/', '_').replace('\\', '_')
        local_output_file = temp_dir / f"{safe_repo_name}.md"

        # Build repomix command with specified file includes
        cmd = [
            shutil.which('npx'), 'repomix', '--remote', repo_url,
            '--include', file_includes,
            '--output', str(local_output_file)
        ]

        logger.info(f"Parallel Repomix Worker {worker_id}: Running repomix for {repo_name}")
        logger.debug(f"Parallel Repomix Worker {worker_id}: Command: {' '.join(cmd)}")

        # Execute repomix command
        start_time = time.time()
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=DEFAULT_PROCESSING_TIMEOUT,
            encoding='utf-8',
            errors='replace'
        )
        duration = time.time() - start_time

        # Check if repomix succeeded
        if result.returncode != 0 or not local_output_file.exists():
            logger.error(f"Parallel Repomix Worker {worker_id}: Failed {repo_name} (exit code: {result.returncode})")
            logger.error(f"Parallel Repomix Worker {worker_id}: Error output: {result.stderr[:500]}")

            # Clean up
            if local_output_file.exists():
                local_output_file.unlink()

            return False

        # Get file size
        file_size_mb = get_file_size_mb(str(local_output_file))

        # Read repomix content
        with open(local_output_file, 'r', encoding='utf-8') as f:
            repomix_content = f.read()

        logger.info(f"Parallel Repomix Worker {worker_id}: Completed {repo_name} ({file_size_mb:.2f}MB, {duration:.1f}s)")

        # Upload to cloud storage
        repomix_filename = f"{safe_repo_name}.md"
        repomix_url = storage_manager.upload_repomix_file(
            job_id, repomix_filename, repomix_content
        )

        logger.info(f"Parallel Repomix Worker {worker_id}: Uploaded {repo_name} to {repomix_url}")

        # Queue for chunking worker
        from queue_manager import FileQueueItem
        repomix_validated_item = FileQueueItem(
            file_path=repomix_url,
            repository_name=repo_name,
            job_id=job_id
        )
        repomix_validated_queue.put(repomix_validated_item)

        logger.info(f"Parallel Repomix Worker {worker_id}: Queued {repo_name} for chunking")

        # Clean up local file
        local_output_file.unlink()

        return True

    except subprocess.TimeoutExpired:
        logger.error(f"Parallel Repomix Worker {worker_id}: Timeout processing {repo_name} (>{DEFAULT_PROCESSING_TIMEOUT}s)")
        return False
    except Exception as e:
        logger.error(f"Parallel Repomix Worker {worker_id}: Exception processing {repo_name}: {e}")
        return False


if __name__ == "__main__":
    # For testing purposes
    import sys
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    
    from config import ScraperConfig
    
    config = ScraperConfig()
    processor = ParallelRepomixProcessor(config, "test_job", num_workers=2)
    
    print("Testing parallel repomix processor...")
    processor.start_workers()
    
    try:
        time.sleep(10)  # Let workers run for a bit
        stats = processor.get_worker_stats()
        print(f"Worker stats: {stats}")
    finally:
        processor.stop_workers()
