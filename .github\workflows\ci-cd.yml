name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  SERVICE_NAME: repository-research-tool
  REGION: us-central1

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Install repomix
      run: npm install -g repomix
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Create test environment file
      run: |
        cat > .env << EOF
        DEPLOYMENT_MODE=local
        REDIS_URL=redis://localhost:6379
        LLM_API_KEY=test_key
        GITHUB_TOKEN=test_token
        REPOMIX_WORKERS=2
        LLM_WORKERS=1
        EOF
    
    - name: Run unit tests
      run: |
        python -m pytest tests/ -v --cov=src --cov-report=xml --cov-report=term
    
    - name: Run integration tests
      run: |
        python validate_cloud_integration.py
        python test_cloud_service_integration.py
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  build:
    name: Build Container Images
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' || github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
    
    - name: Configure Docker for GCR
      run: gcloud auth configure-docker
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
    
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
    
    - name: Deploy API service to staging
      run: |
        gcloud run deploy ${{ env.SERVICE_NAME }}-api-staging \
          --image gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:develop \
          --platform managed \
          --region ${{ env.REGION }} \
          --allow-unauthenticated \
          --memory 1Gi \
          --cpu 1 \
          --timeout 300 \
          --max-instances 3 \
          --min-instances 0 \
          --concurrency 80 \
          --set-env-vars DEPLOYMENT_MODE=cloud \
          --set-env-vars REDIS_URL=${{ secrets.STAGING_REDIS_URL }} \
          --set-secrets SUPABASE_URL=supabase-config:url \
          --set-secrets SUPABASE_KEY=supabase-config:service-key \
          --set-secrets LLM_API_KEY=api-keys:gemini-api-key \
          --set-secrets GITHUB_TOKEN=api-keys:github-token \
          --args api
    
    - name: Deploy Worker service to staging
      run: |
        gcloud run deploy ${{ env.SERVICE_NAME }}-worker-staging \
          --image gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:develop \
          --platform managed \
          --region ${{ env.REGION }} \
          --no-allow-unauthenticated \
          --memory 4Gi \
          --cpu 2 \
          --timeout 3600 \
          --max-instances 2 \
          --min-instances 1 \
          --concurrency 1 \
          --set-env-vars DEPLOYMENT_MODE=cloud \
          --set-env-vars REDIS_URL=${{ secrets.STAGING_REDIS_URL }} \
          --set-env-vars REPOMIX_WORKERS=10 \
          --set-env-vars LLM_WORKERS=3 \
          --set-secrets SUPABASE_URL=supabase-config:url \
          --set-secrets SUPABASE_KEY=supabase-config:service-key \
          --set-secrets LLM_API_KEY=api-keys:gemini-api-key \
          --set-secrets GITHUB_TOKEN=api-keys:github-token \
          --args worker
    
    - name: Run staging tests
      run: |
        # Get staging API URL
        API_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }}-api-staging \
          --region ${{ env.REGION }} --format 'value(status.url)')
        
        # Test staging deployment
        curl -f $API_URL/health
        curl -f $API_URL/config

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
    
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
    
    - name: Deploy API service to production
      run: |
        gcloud run deploy ${{ env.SERVICE_NAME }}-api \
          --image gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.event.release.tag_name }} \
          --platform managed \
          --region ${{ env.REGION }} \
          --allow-unauthenticated \
          --memory 1Gi \
          --cpu 1 \
          --timeout 300 \
          --max-instances 10 \
          --min-instances 1 \
          --concurrency 80 \
          --set-env-vars DEPLOYMENT_MODE=cloud \
          --set-env-vars REDIS_URL=${{ secrets.PRODUCTION_REDIS_URL }} \
          --set-secrets SUPABASE_URL=supabase-config:url \
          --set-secrets SUPABASE_KEY=supabase-config:service-key \
          --set-secrets LLM_API_KEY=api-keys:gemini-api-key \
          --set-secrets GITHUB_TOKEN=api-keys:github-token \
          --args api
    
    - name: Deploy Worker service to production
      run: |
        gcloud run deploy ${{ env.SERVICE_NAME }}-worker \
          --image gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.event.release.tag_name }} \
          --platform managed \
          --region ${{ env.REGION }} \
          --no-allow-unauthenticated \
          --memory 4Gi \
          --cpu 2 \
          --timeout 3600 \
          --max-instances 5 \
          --min-instances 1 \
          --concurrency 1 \
          --set-env-vars DEPLOYMENT_MODE=cloud \
          --set-env-vars REDIS_URL=${{ secrets.PRODUCTION_REDIS_URL }} \
          --set-env-vars REPOMIX_WORKERS=15 \
          --set-env-vars LLM_WORKERS=4 \
          --set-secrets SUPABASE_URL=supabase-config:url \
          --set-secrets SUPABASE_KEY=supabase-config:service-key \
          --set-secrets LLM_API_KEY=api-keys:gemini-api-key \
          --set-secrets GITHUB_TOKEN=api-keys:github-token \
          --args worker
    
    - name: Run production smoke tests
      run: |
        # Get production API URL
        API_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }}-api \
          --region ${{ env.REGION }} --format 'value(status.url)')
        
        # Test production deployment
        curl -f $API_URL/health
        curl -f $API_URL/config
        
        # Run comprehensive tests
        python test_cloud_deployment.py --url $API_URL
    
    - name: Create deployment notification
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          🚀 Repository Research Tool deployed to production!
          Version: ${{ github.event.release.tag_name }}
          API URL: ${{ steps.get-url.outputs.url }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: always()
