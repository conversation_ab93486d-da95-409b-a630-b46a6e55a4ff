#!/usr/bin/env python3
"""
Repository Research Tool - Unified Worker Process

This worker process starts and monitors the standalone repomix_worker and 
llm_worker functions from src/pipeline.py. It does not contain any pipeline 
logic itself - it simply manages the worker processes.
"""

import os
import sys
import time
import signal
import logging
import multiprocessing
from typing import List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import unified components
from config import ScraperConfig
from pipeline import repomix_worker, llm_worker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedWorkerManager:
    """Manages the standalone worker processes for the unified architecture."""
    
    def __init__(self):
        self.config = ScraperConfig()
        self.repomix_processes: List[multiprocessing.Process] = []
        self.llm_processes: List[multiprocessing.Process] = []
        self.running = False
        
        # Convert config to dict for passing to workers
        self.config_dict = {
            'REDIS_URL': self.config.REDIS_URL,
            'SUPABASE_URL': self.config.SUPABASE_URL,
            'SUPABASE_KEY': self.config.SUPABASE_KEY,
            'LLM_API_KEY': self.config.LLM_API_KEY,
            'LLM_BASE_URL': self.config.LLM_BASE_URL,
            'LLM_MODEL': self.config.LLM_MODEL,
            'FILE_INCLUDES': self.config.FILE_INCLUDES,
            'REPOMIX_WORKERS': self.config.REPOMIX_WORKERS,
            'LLM_WORKERS': self.config.LLM_WORKERS
        }
        
        logger.info("✅ Unified Worker Manager initialized")
        logger.info(f"   Repomix workers: {self.config.REPOMIX_WORKERS}")
        logger.info(f"   LLM workers: {self.config.LLM_WORKERS}")
        logger.info(f"   Redis URL: {self.config.REDIS_URL}")
        logger.info(f"   Supabase URL: {self.config.SUPABASE_URL}")
    
    def start_workers(self):
        """Start all worker processes."""
        try:
            logger.info("🚀 Starting unified worker processes...")
            
            # Start repomix workers
            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=repomix_worker,
                    args=(i, self.config_dict, "worker-pool"),  # Generic job_id for worker pool
                    name=f"repomix-worker-{i}"
                )
                p.start()
                self.repomix_processes.append(p)
                logger.info(f"✅ Started repomix worker {i} (PID: {p.pid})")
            
            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=llm_worker,
                    args=(i, self.config_dict, "worker-pool"),  # Generic job_id for worker pool
                    name=f"llm-worker-{i}"
                )
                p.start()
                self.llm_processes.append(p)
                logger.info(f"✅ Started LLM worker {i} (PID: {p.pid})")
            
            self.running = True
            logger.info(f"🎉 All workers started successfully!")
            logger.info(f"   Total processes: {len(self.repomix_processes) + len(self.llm_processes)}")
            
        except Exception as e:
            logger.error(f"❌ Failed to start workers: {e}")
            self.stop_workers()
            raise
    
    def monitor_workers(self):
        """Monitor worker processes and restart if they die."""
        logger.info("👀 Starting worker monitoring...")
        
        while self.running:
            try:
                # Check repomix workers
                for i, process in enumerate(self.repomix_processes):
                    if not process.is_alive():
                        logger.warning(f"⚠️ Repomix worker {i} died, restarting...")
                        process.terminate()
                        process.join(timeout=5)
                        
                        # Start new worker
                        new_process = multiprocessing.Process(
                            target=repomix_worker,
                            args=(i, self.config_dict, "worker-pool"),
                            name=f"repomix-worker-{i}"
                        )
                        new_process.start()
                        self.repomix_processes[i] = new_process
                        logger.info(f"✅ Restarted repomix worker {i} (PID: {new_process.pid})")
                
                # Check LLM workers
                for i, process in enumerate(self.llm_processes):
                    if not process.is_alive():
                        logger.warning(f"⚠️ LLM worker {i} died, restarting...")
                        process.terminate()
                        process.join(timeout=5)
                        
                        # Start new worker
                        new_process = multiprocessing.Process(
                            target=llm_worker,
                            args=(i, self.config_dict, "worker-pool"),
                            name=f"llm-worker-{i}"
                        )
                        new_process.start()
                        self.llm_processes[i] = new_process
                        logger.info(f"✅ Restarted LLM worker {i} (PID: {new_process.pid})")
                
                # Sleep before next check
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                logger.info("🛑 Received interrupt signal, stopping workers...")
                break
            except Exception as e:
                logger.error(f"❌ Error in worker monitoring: {e}")
                time.sleep(10)  # Wait before retrying
    
    def stop_workers(self):
        """Stop all worker processes."""
        logger.info("🛑 Stopping all worker processes...")
        self.running = False
        
        # Stop repomix workers
        for i, process in enumerate(self.repomix_processes):
            if process.is_alive():
                logger.info(f"Stopping repomix worker {i}...")
                process.terminate()
                process.join(timeout=10)
                if process.is_alive():
                    logger.warning(f"Force killing repomix worker {i}")
                    process.kill()
        
        # Stop LLM workers
        for i, process in enumerate(self.llm_processes):
            if process.is_alive():
                logger.info(f"Stopping LLM worker {i}...")
                process.terminate()
                process.join(timeout=10)
                if process.is_alive():
                    logger.warning(f"Force killing LLM worker {i}")
                    process.kill()
        
        logger.info("✅ All workers stopped")
    
    def get_worker_status(self):
        """Get the status of all worker processes."""
        repomix_alive = sum(1 for p in self.repomix_processes if p.is_alive())
        llm_alive = sum(1 for p in self.llm_processes if p.is_alive())
        
        return {
            "repomix_workers": {
                "total": len(self.repomix_processes),
                "alive": repomix_alive,
                "dead": len(self.repomix_processes) - repomix_alive
            },
            "llm_workers": {
                "total": len(self.llm_processes),
                "alive": llm_alive,
                "dead": len(self.llm_processes) - llm_alive
            },
            "total_processes": len(self.repomix_processes) + len(self.llm_processes),
            "running": self.running
        }

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    global worker_manager
    if worker_manager:
        worker_manager.stop_workers()
    sys.exit(0)

def main():
    """Main worker process entry point."""
    global worker_manager
    
    logger.info("🚀 Repository Research Tool - Unified Worker Process")
    logger.info("=" * 60)
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize worker manager
        worker_manager = UnifiedWorkerManager()
        
        # Start workers
        worker_manager.start_workers()
        
        # Monitor workers (blocks until interrupted)
        worker_manager.monitor_workers()
        
    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
    finally:
        if worker_manager:
            worker_manager.stop_workers()

if __name__ == '__main__':
    main()
