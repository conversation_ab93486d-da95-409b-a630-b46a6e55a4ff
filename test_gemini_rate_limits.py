#!/usr/bin/env python3
"""
Test Gemini API rate limits and TPM (Tokens Per Minute) handling
Simulates the exact repository scraper workflow
"""

import requests
import json
import time
import tiktoken
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
from datetime import datetime, timedelta

# API Configuration
API_KEY = "AIzaSyDmNPg_95eUXq8beQuGcGm2iKPwxYuQv20"
BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models"
MODEL = "gemini-2.5-flash"

# Rate limiting configuration (Gemini 2.5 Flash limits)
MAX_RPM = 1000  # Requests per minute
MAX_TPM = 2000000  # Tokens per minute (2M TPM)
TARGET_TPM = 1800000  # Target 1.8M TPM to stay under limit

# Global rate limiting state
rate_limit_lock = threading.Lock()
request_times = []
token_usage = []

def count_tokens(text):
    """Count tokens using tiktoken"""
    try:
        encoding = tiktoken.get_encoding("cl100k_base")
        return len(encoding.encode(text))
    except:
        return len(text.split()) * 1.3

def check_rate_limits(estimated_tokens):
    """Check if we can make a request without exceeding rate limits"""
    global request_times, token_usage
    
    with rate_limit_lock:
        current_time = time.time()
        one_minute_ago = current_time - 60
        
        # Clean old entries
        request_times = [t for t in request_times if t > one_minute_ago]
        token_usage = [(t, tokens) for t, tokens in token_usage if t > one_minute_ago]
        
        # Check RPM limit
        if len(request_times) >= MAX_RPM:
            return False, "RPM limit exceeded"
        
        # Check TPM limit
        current_tokens = sum(tokens for _, tokens in token_usage)
        if current_tokens + estimated_tokens > TARGET_TPM:
            return False, f"TPM limit would be exceeded ({current_tokens + estimated_tokens:,} > {TARGET_TPM:,})"
        
        return True, "OK"

def make_rate_limited_request(url, payload, headers, timeout=60):
    """Make a rate-limited request to Gemini API"""
    global request_times, token_usage
    
    # Estimate tokens for this request
    prompt_text = payload['contents'][0]['parts'][0]['text']
    estimated_tokens = count_tokens(prompt_text)
    
    # Check rate limits
    can_proceed, reason = check_rate_limits(estimated_tokens)
    if not can_proceed:
        return None, f"Rate limit check failed: {reason}"
    
    try:
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=timeout)
        end_time = time.time()
        
        # Record this request
        with rate_limit_lock:
            request_times.append(start_time)
            token_usage.append((start_time, estimated_tokens))
        
        if response.status_code == 200:
            return response, None
        else:
            return None, f"HTTP {response.status_code}: {response.text}"
            
    except Exception as e:
        return None, f"Exception: {str(e)}"

def analyze_repository_chunk(chunk_id, content, repo_name):
    """Analyze a repository chunk with rate limiting"""
    print(f"  🔄 Processing {repo_name} chunk {chunk_id}...")
    
    url = f"{BASE_URL}/{MODEL}:generateContent?key={API_KEY}"
    
    prompt = f"""
Analyze this repository chunk and provide comprehensive feature analysis:

Repository: {repo_name}
Chunk: {chunk_id}

Provide detailed analysis of:
1. **Core Features**: List all features with underlying functionalities
2. **Technologies**: All frameworks, libraries, and technologies used
3. **Architecture**: Describe patterns and components
4. **API Endpoints**: Document any APIs or interfaces
5. **Database/Storage**: Describe data handling
6. **Security**: Note security considerations
7. **Dependencies**: External integrations

Content:
{content}
"""
    
    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 4000,
            "topP": 0.8,
            "topK": 40
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    start_time = time.time()
    response, error = make_rate_limited_request(url, payload, headers)
    end_time = time.time()
    
    if error:
        print(f"  ❌ {repo_name} chunk {chunk_id} failed: {error}")
        return {
            'repo': repo_name,
            'chunk_id': chunk_id,
            'success': False,
            'error': error,
            'response_time': end_time - start_time
        }
    
    try:
        data = response.json()
        candidates = data.get('candidates', [])
        if candidates:
            content = candidates[0].get('content', {})
            parts = content.get('parts', [])
            if parts:
                analysis = parts[0].get('text', '')
                
                result = {
                    'repo': repo_name,
                    'chunk_id': chunk_id,
                    'success': True,
                    'analysis': analysis,
                    'response_time': end_time - start_time,
                    'tokens_estimated': count_tokens(prompt)
                }
                
                print(f"  ✅ {repo_name} chunk {chunk_id} completed ({end_time - start_time:.2f}s)")
                return result
    
    except Exception as e:
        print(f"  ❌ {repo_name} chunk {chunk_id} parsing failed: {e}")
    
    return {
        'repo': repo_name,
        'chunk_id': chunk_id,
        'success': False,
        'error': "Failed to parse response",
        'response_time': end_time - start_time
    }

def simulate_repository_batch():
    """Simulate processing a batch of repositories"""
    print("📦 Simulating Repository Batch Processing...")
    
    # Simulate 4 repositories with different content sizes
    repositories = [
        {
            'name': 'web-app-frontend',
            'content': """
# React Frontend Application
## Components
- UserDashboard: Main user interface
- AuthenticationForm: Login/signup handling
- DataVisualization: Charts and graphs
- APIClient: Backend communication
## Technologies
- React 18, TypeScript, Material-UI
- Axios for API calls, React Router
- Chart.js for visualizations
""" * 20
        },
        {
            'name': 'api-backend',
            'content': """
# FastAPI Backend Service
## Endpoints
- /auth/login: User authentication
- /api/users: User management
- /api/data: Data processing
- /api/reports: Report generation
## Technologies
- FastAPI, SQLAlchemy, PostgreSQL
- JWT authentication, Redis caching
- Celery for background tasks
""" * 25
        },
        {
            'name': 'data-pipeline',
            'content': """
# Data Processing Pipeline
## Components
- DataIngestion: ETL processes
- DataValidation: Quality checks
- DataTransformation: Processing logic
- DataStorage: Warehouse operations
## Technologies
- Apache Airflow, Pandas, NumPy
- PostgreSQL, Redis, Docker
- Kubernetes for orchestration
""" * 30
        },
        {
            'name': 'mobile-app',
            'content': """
# React Native Mobile App
## Features
- UserProfile: Account management
- Notifications: Push messaging
- OfflineSync: Data synchronization
- CameraIntegration: Photo capture
## Technologies
- React Native, Expo, TypeScript
- AsyncStorage, React Navigation
- Firebase for push notifications
""" * 15
        }
    ]
    
    return repositories

def test_batch_processing():
    """Test processing multiple repositories with rate limiting"""
    print("⚡ Testing Batch Processing with Rate Limiting...")
    
    repositories = simulate_repository_batch()
    
    # Create tasks for all repositories
    tasks = []
    for repo in repositories:
        # For simplicity, treating each repo as a single chunk
        tasks.append((1, repo['content'], repo['name']))
    
    print(f"📊 Processing {len(tasks)} repository chunks...")
    
    # Calculate total estimated tokens
    total_tokens = sum(count_tokens(content) for _, content, _ in tasks)
    print(f"📊 Total estimated tokens: {total_tokens:,}")
    
    # Process with 2 workers (as per requirements)
    max_workers = 2
    results = []
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_task = {
            executor.submit(analyze_repository_chunk, chunk_id, content, repo_name): (chunk_id, repo_name)
            for chunk_id, content, repo_name in tasks
        }
        
        # Collect results
        for future in as_completed(future_to_task):
            chunk_id, repo_name = future_to_task[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"❌ {repo_name} chunk {chunk_id} failed with exception: {e}")
                results.append({
                    'repo': repo_name,
                    'chunk_id': chunk_id,
                    'success': False,
                    'error': str(e)
                })
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Analyze results
    successful = [r for r in results if r.get('success', False)]
    failed = [r for r in results if not r.get('success', False)]
    
    print(f"\n📊 Batch Processing Results:")
    print(f"  Total time: {total_time:.2f}s")
    print(f"  Successful: {len(successful)}/{len(results)}")
    print(f"  Failed: {len(failed)}/{len(results)}")
    
    if successful:
        avg_response_time = sum(r['response_time'] for r in successful) / len(successful)
        total_processed_tokens = sum(r.get('tokens_estimated', 0) for r in successful)
        print(f"  Average response time: {avg_response_time:.2f}s")
        print(f"  Total tokens processed: {total_processed_tokens:,}")
        print(f"  Effective TPM: {(total_processed_tokens / total_time * 60):,.0f}")
    
    # Show rate limiting stats
    with rate_limit_lock:
        current_time = time.time()
        recent_requests = len([t for t in request_times if t > current_time - 60])
        recent_tokens = sum(tokens for t, tokens in token_usage if t > current_time - 60)
        
        print(f"\n📈 Rate Limiting Stats:")
        print(f"  Requests in last minute: {recent_requests}/{MAX_RPM}")
        print(f"  Tokens in last minute: {recent_tokens:,}/{TARGET_TPM:,}")
        print(f"  RPM utilization: {(recent_requests/MAX_RPM)*100:.1f}%")
        print(f"  TPM utilization: {(recent_tokens/TARGET_TPM)*100:.1f}%")
    
    # Save results
    if successful:
        output = {
            'processing_summary': {
                'total_time': total_time,
                'successful_analyses': len(successful),
                'failed_analyses': len(failed),
                'total_tokens': total_processed_tokens,
                'effective_tpm': (total_processed_tokens / total_time * 60) if total_time > 0 else 0
            },
            'analyses': successful
        }
        
        with open('batch_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Results saved to: batch_analysis_results.json")
    
    return len(successful) == len(results)

def main():
    """Run rate limiting and batch processing tests"""
    print("🚀 Starting Gemini Rate Limiting Tests\n")
    
    # Test batch processing with rate limiting
    batch_ok = test_batch_processing()
    
    # Summary
    print("\n" + "="*50)
    print("📋 RATE LIMITING TEST SUMMARY:")
    print(f"  Batch Processing: {'✅ PASS' if batch_ok else '❌ FAIL'}")
    
    print(f"\n🎯 Overall: {'✅ RATE LIMITING TESTS PASSED' if batch_ok else '❌ RATE LIMITING TESTS FAILED'}")
    
    if batch_ok:
        print("\n📄 Check 'batch_analysis_results.json' for detailed results!")
    
    return batch_ok

if __name__ == "__main__":
    main()
