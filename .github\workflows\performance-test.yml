name: Performance Testing

on:
  push:
    branches: [ main ]
  schedule:
    # Run performance tests weekly on Sundays at 3 AM UTC
    - cron: '0 3 * * 0'
  workflow_dispatch:
    inputs:
      target_url:
        description: 'Target URL for performance testing'
        required: false
        default: 'https://repository-research-api-staging-xyz.run.app'

jobs:
  performance-test:
    name: Performance and Load Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install locust requests
    
    - name: Create performance test script
      run: |
        cat > performance_test.py << 'EOF'
        import time
        import requests
        from locust import HttpUser, task, between
        
        class RepositoryResearchUser(HttpUser):
            wait_time = between(1, 3)
            
            def on_start(self):
                # Test health endpoint on start
                response = self.client.get("/health")
                if response.status_code != 200:
                    print(f"Health check failed: {response.status_code}")
            
            @task(3)
            def test_health_endpoint(self):
                """Test health endpoint (most frequent)"""
                self.client.get("/health")
            
            @task(2)
            def test_config_endpoint(self):
                """Test configuration endpoint"""
                self.client.get("/config")
            
            @task(1)
            def test_jobs_listing(self):
                """Test jobs listing endpoint"""
                self.client.get("/jobs")
            
            @task(1)
            def test_job_creation(self):
                """Test job creation endpoint"""
                payload = {
                    "keywords": "test repository",
                    "min_stars": 10,
                    "max_repos": 2
                }
                response = self.client.post("/start", json=payload)
                if response.status_code == 202:
                    # Get job ID and test status endpoint
                    job_data = response.json()
                    job_id = job_data.get("job_id")
                    if job_id:
                        self.client.get(f"/status/{job_id}")
        EOF
    
    - name: Set target URL
      run: |
        if [ "${{ github.event.inputs.target_url }}" != "" ]; then
          echo "TARGET_URL=${{ github.event.inputs.target_url }}" >> $GITHUB_ENV
        else
          echo "TARGET_URL=https://repository-research-api-staging-xyz.run.app" >> $GITHUB_ENV
        fi
    
    - name: Run load test
      run: |
        # Run locust load test
        locust -f performance_test.py --headless \
          --users 10 --spawn-rate 2 \
          --run-time 5m \
          --host $TARGET_URL \
          --html performance-report.html \
          --csv performance-results
    
    - name: Run response time test
      run: |
        python << 'EOF'
        import requests
        import time
        import json
        import os
        
        target_url = os.environ.get('TARGET_URL', 'http://localhost:8080')
        
        def test_endpoint_performance(endpoint, method='GET', payload=None):
            """Test endpoint performance"""
            times = []
            errors = 0
            
            for i in range(20):  # 20 requests
                start_time = time.time()
                try:
                    if method == 'GET':
                        response = requests.get(f"{target_url}{endpoint}", timeout=30)
                    else:
                        response = requests.post(f"{target_url}{endpoint}", json=payload, timeout=30)
                    
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000  # Convert to ms
                    times.append(response_time)
                    
                    if response.status_code >= 400:
                        errors += 1
                        
                except Exception as e:
                    errors += 1
                    print(f"Request failed: {e}")
                
                time.sleep(0.1)  # Small delay between requests
            
            if times:
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                p95_time = sorted(times)[int(len(times) * 0.95)]
                
                return {
                    'endpoint': endpoint,
                    'avg_ms': round(avg_time, 2),
                    'min_ms': round(min_time, 2),
                    'max_ms': round(max_time, 2),
                    'p95_ms': round(p95_time, 2),
                    'errors': errors,
                    'total_requests': len(times) + errors
                }
            else:
                return {'endpoint': endpoint, 'error': 'All requests failed'}
        
        # Test different endpoints
        results = []
        
        # Health endpoint
        results.append(test_endpoint_performance('/health'))
        
        # Config endpoint
        results.append(test_endpoint_performance('/config'))
        
        # Jobs listing
        results.append(test_endpoint_performance('/jobs'))
        
        # Job creation
        payload = {"keywords": "test", "min_stars": 10, "max_repos": 1}
        results.append(test_endpoint_performance('/start', 'POST', payload))
        
        # Save results
        with open('response_time_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        # Print results
        print("\n📊 Response Time Test Results:")
        print("=" * 60)
        for result in results:
            if 'error' not in result:
                print(f"Endpoint: {result['endpoint']}")
                print(f"  Average: {result['avg_ms']}ms")
                print(f"  Min: {result['min_ms']}ms")
                print(f"  Max: {result['max_ms']}ms")
                print(f"  P95: {result['p95_ms']}ms")
                print(f"  Errors: {result['errors']}/{result['total_requests']}")
                print()
        EOF
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          performance-report.html
          performance-results_stats.csv
          performance-results_failures.csv
          response_time_results.json
      if: always()
    
    - name: Check performance thresholds
      run: |
        python << 'EOF'
        import json
        import sys
        
        # Load response time results
        try:
            with open('response_time_results.json') as f:
                results = json.load(f)
        except FileNotFoundError:
            print("❌ No performance results found")
            sys.exit(1)
        
        # Define thresholds (in milliseconds)
        thresholds = {
            '/health': 500,
            '/config': 1000,
            '/jobs': 1000,
            '/start': 2000
        }
        
        failed_checks = []
        
        print("🎯 Performance Threshold Checks:")
        print("=" * 40)
        
        for result in results:
            if 'error' in result:
                print(f"❌ {result['endpoint']}: {result['error']}")
                failed_checks.append(result['endpoint'])
                continue
            
            endpoint = result['endpoint']
            avg_time = result['avg_ms']
            threshold = thresholds.get(endpoint, 2000)
            
            if avg_time <= threshold:
                print(f"✅ {endpoint}: {avg_time}ms (threshold: {threshold}ms)")
            else:
                print(f"❌ {endpoint}: {avg_time}ms > {threshold}ms threshold")
                failed_checks.append(endpoint)
        
        if failed_checks:
            print(f"\n❌ Performance checks failed for: {', '.join(failed_checks)}")
            sys.exit(1)
        else:
            print("\n✅ All performance checks passed!")
        EOF
