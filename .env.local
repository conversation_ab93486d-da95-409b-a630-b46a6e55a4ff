# Repository Research Tool - Local Development Configuration
# Copy this to .env for local development with Docker containers

# GitHub API Configuration
GITHUB_TOKEN=your_github_token_here

# LLM Configuration (Gemini)
LLM_API_KEY=your_gemini_api_key_here
LLM_BASE_URL=https://generativelanguage.googleapis.com/v1beta
LLM_MODEL=gemini-2.0-flash-exp
LLM_MAX_OUTPUT_TOKENS=8192
LLM_TIMEOUT=90

# Local Redis Configuration (Docker container)
REDIS_URL=redis://localhost:6379

# Local Supabase Configuration (Docker containers)
SUPABASE_URL=http://localhost:3000
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
SUPABASE_STORAGE_URL=http://localhost:5000

# Database Configuration (for direct connections)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres

# Processing Configuration
REPOMIX_WORKERS=15
LLM_WORKERS=4
FILE_INCLUDES="**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"
RATE_LIMIT_MB_PER_MIN=12

# Optional: Sentry Configuration (leave empty for local development)
SENTRY_DSN=
SENTRY_ENVIRONMENT=local
SENTRY_TRACES_SAMPLE_RATE=0.1
