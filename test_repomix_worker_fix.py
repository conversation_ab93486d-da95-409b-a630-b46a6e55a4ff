#!/usr/bin/env python3
"""
Test the repomix worker fix to ensure no NameError occurs.
This validates that the repomix worker can process files without undefined variable errors.
"""

import sys
import os

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_repomix_worker_variables():
    """Test that repomix worker doesn't reference undefined variables."""
    print("🧪 TESTING REPOMIX WORKER VARIABLE REFERENCES")
    print("=" * 50)
    
    try:
        # Read the repomix worker code
        with open('src/pipeline.py', 'r') as f:
            pipeline_code = f.read()
        
        # Check for problematic variable references
        problematic_vars = ['uploaded_chunk_urls', 'total_chunked_size_mb']
        issues_found = []
        
        for var in problematic_vars:
            if var in pipeline_code:
                # Find the line number
                lines = pipeline_code.split('\n')
                for i, line in enumerate(lines, 1):
                    if var in line and not line.strip().startswith('#'):
                        issues_found.append(f"Line {i}: {line.strip()}")
        
        if issues_found:
            print("❌ Found references to undefined variables:")
            for issue in issues_found:
                print(f"   {issue}")
            return False
        else:
            print("✅ No references to undefined variables found")
            print("✅ Repomix worker should not have NameError issues")
            return True
            
    except Exception as e:
        print(f"❌ Error checking repomix worker code: {e}")
        return False

def test_new_workflow_variables():
    """Test that the new workflow uses correct variables."""
    print("\n🧪 TESTING NEW WORKFLOW VARIABLE USAGE")
    print("=" * 50)
    
    try:
        with open('src/pipeline.py', 'r') as f:
            pipeline_code = f.read()
        
        # Check for correct variable usage
        required_vars = ['repomix_url', 'file_size_mb']
        found_vars = []
        
        for var in required_vars:
            if var in pipeline_code:
                found_vars.append(var)
                print(f"✅ Found correct variable: {var}")
            else:
                print(f"❌ Missing variable: {var}")
        
        if len(found_vars) == len(required_vars):
            print("✅ All required variables are present")
            return True
        else:
            print(f"❌ Missing {len(required_vars) - len(found_vars)} required variables")
            return False
            
    except Exception as e:
        print(f"❌ Error checking new workflow variables: {e}")
        return False

def test_workflow_logic():
    """Test the new workflow logic structure."""
    print("\n🧪 TESTING NEW WORKFLOW LOGIC")
    print("=" * 50)
    
    try:
        with open('src/pipeline.py', 'r') as f:
            pipeline_code = f.read()
        
        # Check for new workflow components
        workflow_components = [
            'upload_repomix_file',  # Should upload full file
            'repomix_validated_queue',  # Should queue for chunking worker
            'NEW WORKFLOW',  # Comment indicating new workflow
        ]
        
        found_components = []
        for component in workflow_components:
            if component in pipeline_code:
                found_components.append(component)
                print(f"✅ Found workflow component: {component}")
            else:
                print(f"❌ Missing workflow component: {component}")
        
        # Check that old chunking logic is removed
        old_components = [
            'chunk_and_retain_file',  # Should not be in repomix worker anymore
        ]
        
        old_found = []
        for component in old_components:
            if component in pipeline_code:
                old_found.append(component)
                print(f"⚠️ Found old component (should be removed): {component}")
        
        if len(found_components) >= 2 and len(old_found) == 0:
            print("✅ New workflow logic is correctly implemented")
            return True
        else:
            print("❌ Workflow logic needs adjustment")
            return False
            
    except Exception as e:
        print(f"❌ Error checking workflow logic: {e}")
        return False

def main():
    """Test repomix worker fix."""
    print("🔧 TESTING REPOMIX WORKER FIX")
    print("=" * 60)
    print("TESTING:")
    print("1. No undefined variable references")
    print("2. Correct new workflow variables")
    print("3. New workflow logic structure")
    print()
    
    # Test variable references
    var_check = test_repomix_worker_variables()
    
    # Test new workflow variables
    new_vars_check = test_new_workflow_variables()
    
    # Test workflow logic
    logic_check = test_workflow_logic()
    
    # Final assessment
    if var_check and new_vars_check and logic_check:
        print("\n🎉 ALL REPOMIX WORKER FIX TESTS PASSED!")
        print("✅ No undefined variable references")
        print("✅ Correct new workflow variables present")
        print("✅ New workflow logic implemented")
        print()
        print("🚀 CRITICAL BUG FIXED:")
        print("   - Removed references to undefined variables")
        print("   - Uses repomix_url and file_size_mb for database updates")
        print("   - Maintains new workflow: Upload Full → Queue for Chunking")
        print("   - Ready for integration testing")
        return True
    else:
        print("\n❌ REPOMIX WORKER FIX TESTS FAILED!")
        print(f"   Variable references: {'✅' if var_check else '❌'}")
        print(f"   New workflow variables: {'✅' if new_vars_check else '❌'}")
        print(f"   Workflow logic: {'✅' if logic_check else '❌'}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
