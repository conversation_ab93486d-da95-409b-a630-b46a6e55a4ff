#!/usr/bin/env python3
"""
Full Repository Analysis - Bridges GitHub search with unified architecture
to perform complete LLM analysis of found repositories.
"""

import os
import sys
import uuid
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from github_search import search_repositories
from config import ScraperConfig
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord
from redis_queue import QueueManager, RepositoryQueueItem

def main():
    """Run full analysis with GitHub search + unified architecture pipeline."""
    
    print("🚀 FULL REPOSITORY ANALYSIS - GITHUB SEARCH + LLM ANALYSIS")
    print("=" * 70)
    print("This will:")
    print("1. Search GitHub for repositories")
    print("2. Queue them in Redis for processing")
    print("3. Process with repomix + LLM analysis")
    print("4. Generate comprehensive analysis files")
    print()
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = ScraperConfig()
        print("✅ Configuration loaded successfully")
        
        # Search for repositories
        print("\n🔍 Searching GitHub repositories...")
        keywords = "proxy list,free proxy,proxy scrape"
        
        repositories = search_repositories(
            keywords=keywords,
            min_stars=30,
            max_repos=60
        )
        
        print(f"✅ Found {len(repositories)} repositories total")
        
        if not repositories:
            print("❌ No repositories found. Check GitHub token and rate limits.")
            return False
        
        # Show top repositories
        print("\n📊 Top repositories to analyze:")
        for i, repo in enumerate(repositories[:10]):
            print(f"   {i+1:2d}. {repo['name']} ({repo['stars']} stars)")
        
        if len(repositories) > 10:
            print(f"   ... and {len(repositories) - 10} more repositories")
        
        # Initialize unified architecture services
        print("\n🔗 Connecting to unified architecture...")
        
        try:
            supabase_client = create_supabase_client()
            queue_manager = QueueManager(config.REDIS_URL)
            print("✅ Connected to Supabase and Redis")
        except Exception as e:
            print(f"❌ Failed to connect to services: {e}")
            print("\n💡 SOLUTION:")
            print("   1. Start Redis: docker-compose -f docker-compose.local.yml up -d redis")
            print("   2. Or use cloud Redis service")
            print("   3. Ensure Supabase credentials are correct")
            return False
        
        # Create job record
        job_id = str(uuid.uuid4())
        print(f"\n📝 Creating analysis job: {job_id}")
        
        job_record = JobRecord(
            id=job_id,
            keywords=keywords,
            min_stars=30,
            max_repos=60,
            status="running",
            created_at=datetime.now().isoformat(),
            custom_prompt=None,
            debug=False
        )
        supabase_client.create_job(job_record)
        print("✅ Job record created in Supabase")
        
        # Queue repositories for processing
        print(f"\n📤 Queuing {len(repositories)} repositories for analysis...")
        repo_queue = queue_manager.get_repository_queue()
        
        for i, repo in enumerate(repositories):
            # Create repository record in Supabase
            repo_record = RepositoryRecord(
                id=str(uuid.uuid4()),
                job_id=job_id,
                name=repo['full_name'],
                url=repo['html_url'],
                stars=repo['stars'],
                file_size_mb=0.0,  # Will be updated after repomix
                analysis_status="pending"
            )
            supabase_client.create_repository(repo_record)
            
            # Add to Redis queue for worker processing
            queue_item = RepositoryQueueItem(
                repository_name=repo['full_name'],
                repository_url=repo['html_url'],
                job_id=job_id
            )
            repo_queue.put(queue_item)
            
            if (i + 1) % 10 == 0:
                print(f"   Queued {i + 1}/{len(repositories)} repositories...")
        
        print(f"✅ All {len(repositories)} repositories queued successfully")
        
        # Create output directory for local results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        output_dir = f"output/full_analysis_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        
        # Save job info
        job_info = {
            "job_id": job_id,
            "created_at": datetime.now().isoformat(),
            "keywords": keywords,
            "repositories_queued": len(repositories),
            "status": "processing",
            "supabase_url": config.SUPABASE_URL,
            "redis_url": config.REDIS_URL
        }
        
        import json
        with open(os.path.join(output_dir, "job_info.json"), 'w') as f:
            json.dump(job_info, f, indent=2)
        
        print(f"\n📁 Output directory: {output_dir}")
        print(f"📄 Job info saved: {output_dir}/job_info.json")
        
        print("\n🚀 ANALYSIS PIPELINE INITIATED!")
        print("=" * 70)
        print("NEXT STEPS:")
        print("1. Start workers: python worker_unified.py")
        print("2. Monitor progress: Check Supabase database")
        print("3. Get results: python -c \"")
        print("   import requests")
        print(f"   response = requests.get('http://localhost:8080/status/{job_id}')")
        print("   print(response.json())")
        print("   \"")
        print()
        print("🎯 WORKERS WILL:")
        print("   - Download and process each repository with repomix")
        print("   - Send processed content to Gemini for AI analysis")
        print("   - Save comprehensive analysis results to Supabase")
        print("   - Generate final analysis files")
        print()
        print(f"📊 Job ID: {job_id}")
        print(f"🔗 Track progress in Supabase: {config.SUPABASE_URL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis initiation failed: {e}")
        logger.error(f"Full analysis failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
