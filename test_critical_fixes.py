#!/usr/bin/env python3
"""
Test the critical fixes implemented:
1. Search sorting by last updated
2. Chunking integration in repomix worker
3. Enhanced final merge for chunks
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_search_sorting():
    """Test that search results are sorted by last updated."""
    print("🔍 Testing search sorting fix...")
    
    try:
        from github_search import search_repositories
        
        # Test with a small search
        repos = search_repositories(
            keywords="python test",
            min_stars=100,
            max_repos=5
        )
        
        if repos:
            print(f"✅ Found {len(repos)} repositories")
            print("📅 Checking sort order (by updated_at):")
            
            for i, repo in enumerate(repos[:3]):
                print(f"   {i+1}. {repo['name']} - Updated: {repo.get('updated_at', 'N/A')}")
            
            # Verify sorting
            if len(repos) >= 2:
                first_updated = repos[0].get('updated_at', '')
                second_updated = repos[1].get('updated_at', '')
                
                if first_updated >= second_updated:
                    print("✅ Sort order correct: Most recent first")
                else:
                    print("❌ Sort order incorrect: Not sorted by updated_at")
            
            return True
        else:
            print("❌ No repositories found")
            return False
            
    except Exception as e:
        print(f"❌ Search sorting test failed: {e}")
        return False

def test_chunking_utility():
    """Test that chunking utility works correctly."""
    print("\n🔧 Testing chunking utility...")
    
    try:
        from utils import chunk_and_retain_file, get_file_size_mb
        import tempfile
        import os
        
        # Create a test file larger than 3MB
        test_content = "This is test content for chunking.\n" * 50000  # ~1.5MB
        test_content = test_content * 3  # ~4.5MB
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
            f.write(test_content)
            test_file = f.name
        
        try:
            file_size = get_file_size_mb(test_file)
            print(f"📊 Test file size: {file_size:.2f}MB")
            
            # Test chunking
            chunk_paths = chunk_and_retain_file(test_file, max_size_mb=3, max_chunks=3)
            
            print(f"✅ Chunking created {len(chunk_paths)} chunks")
            
            for i, chunk_path in enumerate(chunk_paths):
                chunk_size = get_file_size_mb(chunk_path)
                print(f"   Chunk {i}: {chunk_size:.2f}MB")
                os.unlink(chunk_path)  # Clean up
            
            return True
            
        finally:
            os.unlink(test_file)  # Clean up
            
    except Exception as e:
        print(f"❌ Chunking utility test failed: {e}")
        return False

def test_config_values():
    """Test that configuration values are correct."""
    print("\n⚙️ Testing configuration values...")
    
    try:
        from config import ScraperConfig
        
        config = ScraperConfig()
        
        print(f"📊 MAX_FILE_SIZE_MB: {config.MAX_FILE_SIZE_MB}")
        print(f"📊 MAX_CHUNKS_TO_RETAIN: {config.MAX_CHUNKS_TO_RETAIN}")
        print(f"📊 FILE_INCLUDES: {config.FILE_INCLUDES}")
        
        # Verify expected values
        if config.MAX_FILE_SIZE_MB == 3:
            print("✅ MAX_FILE_SIZE_MB is correct (3MB)")
        else:
            print(f"❌ MAX_FILE_SIZE_MB should be 3, got {config.MAX_FILE_SIZE_MB}")
        
        if config.MAX_CHUNKS_TO_RETAIN == 3:
            print("✅ MAX_CHUNKS_TO_RETAIN is correct (3)")
        else:
            print(f"❌ MAX_CHUNKS_TO_RETAIN should be 3, got {config.MAX_CHUNKS_TO_RETAIN}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run all critical fix tests."""
    print("🚀 TESTING CRITICAL FIXES IMPLEMENTATION")
    print("=" * 60)
    
    results = []
    
    # Test 1: Search sorting
    results.append(test_search_sorting())
    
    # Test 2: Chunking utility
    results.append(test_chunking_utility())
    
    # Test 3: Configuration
    results.append(test_config_values())
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    
    test_names = ["Search Sorting", "Chunking Utility", "Configuration"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {i+1}. {name}: {status}")
    
    all_passed = all(results)
    
    if all_passed:
        print("\n🎉 ALL CRITICAL FIXES IMPLEMENTED SUCCESSFULLY!")
        print("✅ Ready for full system test")
    else:
        print("\n⚠️ Some tests failed - review implementation")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
