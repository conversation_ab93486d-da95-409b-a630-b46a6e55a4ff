#!/usr/bin/env python3
"""
PROPER TEST - Exactly as specified by user:
- Keywords: "proxy list", "free proxy", "proxy scrape"  
- Min stars: 30
- Max repos per keyword: 60
- Expected total: ~180 repositories
- Sorted by last updated
- Full unified architecture with Redis + Supabase
- Proper logging files
- Cloud storage integration
"""

import os
import sys
import uuid
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Run the EXACT test as specified by the user."""
    
    print("🚀 PROPER REPOSITORY RESEARCH TEST - EXACT SPECIFICATIONS")
    print("=" * 70)
    print("PARAMETERS (as originally requested):")
    print("   Keywords: 'proxy list', 'free proxy', 'proxy scrape'")
    print("   Min stars: 30")
    print("   Max repos per keyword: 60")
    print("   Expected total: ~180 repositories")
    print("   Sorting: By last updated (NOT stars)")
    print("   Architecture: Full unified with Redis + Supabase")
    print("   Storage: Cloud storage with proper logging")
    print()
    
    # Check if Redis is available
    print("🔧 Checking Redis availability...")
    try:
        import redis
        # Try to connect to Redis
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
        client = redis.from_url(redis_url)
        client.ping()
        print(f"✅ Redis connected: {redis_url}")
    except Exception as e:
        print(f"❌ Redis not available: {e}")
        print()
        print("🛠️ REDIS SETUP REQUIRED:")
        print("Option 1 - Cloud Redis (Recommended):")
        print("   1. Go to https://upstash.com/")
        print("   2. Create free account")
        print("   3. Create Redis database")
        print("   4. Copy Redis URL")
        print("   5. Update .env: REDIS_URL=redis://...")
        print()
        print("Option 2 - Local Docker:")
        print("   docker run -d --name redis -p 6379:6379 redis:7-alpine")
        print()
        return False
    
    # Run the actual unified architecture test
    print("\n🎯 RUNNING UNIFIED ARCHITECTURE TEST...")
    
    try:
        # Import and run the main pipeline
        from main import main as run_main
        
        # Set up arguments for the exact test
        import sys
        original_argv = sys.argv.copy()
        
        # Set the exact parameters as requested
        sys.argv = [
            'main.py',
            '--keywords', 'proxy list,free proxy,proxy scrape',
            '--min-stars', '30',
            '--max-repos', '60',  # This should be per keyword
            '--debug'  # Enable debug logging
        ]
        
        print("📋 Running with parameters:")
        print(f"   Command: {' '.join(sys.argv)}")
        print()
        
        # Run the main pipeline
        result = run_main()
        
        # Restore original argv
        sys.argv = original_argv
        
        if result:
            print("\n🎉 PROPER TEST COMPLETED SUCCESSFULLY!")
            print("✅ All components tested:")
            print("   - GitHub search (sorted by last updated)")
            print("   - Redis queuing system")
            print("   - Supabase database and storage")
            print("   - Repomix processing")
            print("   - LLM analysis")
            print("   - Proper logging files")
            print("   - Unified architecture validation")
        else:
            print("\n❌ Test failed - check logs for details")
            
        return result
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ Test execution error: {e}")
        return False

if __name__ == '__main__':
    success = main()
    
    if success:
        print("\n📊 VERIFICATION CHECKLIST:")
        print("□ 180 repositories found (60 per keyword)")
        print("□ Sorted by last updated (not stars)")
        print("□ Redis queues populated")
        print("□ Supabase records created")
        print("□ Workers processing repositories")
        print("□ Repomix files generated")
        print("□ LLM analysis completed")
        print("□ Logging files created")
        print("□ Final merged analysis file")
        print()
        print("🔍 Check output/ directory for results")
        print("🔍 Check Supabase dashboard for database records")
        print("🔍 Check logs/ directory for detailed logs")
    
    sys.exit(0 if success else 1)
