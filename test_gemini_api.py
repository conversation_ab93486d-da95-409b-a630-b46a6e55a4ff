#!/usr/bin/env python3
"""
Test script for Gemini API connectivity and functionality
"""

import requests
import json
import time

# API Configuration
API_KEY = "AIzaSyDmNPg_95eUXq8beQuGcGm2iKPwxYuQv20"
BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models"

def test_gemini_models():
    """Test listing available Gemini models"""
    print("🔍 Testing Gemini API - Listing Models...")
    
    url = f"{BASE_URL}?key={API_KEY}"
    
    try:
        response = requests.get(url, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"✅ Found {len(models)} models:")
            for model in models[:5]:  # Show first 5 models
                print(f"  - {model.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_gemini_generation():
    """Test Gemini text generation"""
    print("\n🤖 Testing Gemini API - Text Generation...")
    
    model = "gemini-2.5-flash"
    url = f"{BASE_URL}/{model}:generateContent?key={API_KEY}"
    
    payload = {
        "contents": [{
            "parts": [{
                "text": "Hello! Please respond with a simple greeting and confirm you're working correctly."
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 100
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            candidates = data.get('candidates', [])
            if candidates:
                content = candidates[0].get('content', {})
                parts = content.get('parts', [])
                if parts:
                    text = parts[0].get('text', '')
                    print(f"✅ Response: {text}")
                    return True
            print("❌ No valid response content found")
            return False
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_gemini_rate_limits():
    """Test Gemini API rate limits"""
    print("\n⚡ Testing Gemini API - Rate Limits...")
    
    model = "gemini-2.5-flash"
    url = f"{BASE_URL}/{model}:generateContent?key={API_KEY}"
    
    payload = {
        "contents": [{
            "parts": [{
                "text": "Count from 1 to 5."
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 50
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    success_count = 0
    total_requests = 5
    
    for i in range(total_requests):
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                success_count += 1
                print(f"  Request {i+1}: ✅ Success ({end_time - start_time:.2f}s)")
            else:
                print(f"  Request {i+1}: ❌ Failed ({response.status_code})")
                
            # Small delay between requests
            time.sleep(0.5)
            
        except Exception as e:
            print(f"  Request {i+1}: ❌ Exception: {e}")
    
    print(f"\n📊 Rate Limit Test Results: {success_count}/{total_requests} successful")
    return success_count == total_requests

def main():
    """Run all Gemini API tests"""
    print("🚀 Starting Gemini API Tests\n")
    
    # Test 1: List models
    models_ok = test_gemini_models()
    
    # Test 2: Generate content
    generation_ok = test_gemini_generation()
    
    # Test 3: Rate limits
    rate_limits_ok = test_gemini_rate_limits()
    
    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY:")
    print(f"  Models API: {'✅ PASS' if models_ok else '❌ FAIL'}")
    print(f"  Generation: {'✅ PASS' if generation_ok else '❌ FAIL'}")
    print(f"  Rate Limits: {'✅ PASS' if rate_limits_ok else '❌ FAIL'}")
    
    overall_success = models_ok and generation_ok and rate_limits_ok
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    main()
