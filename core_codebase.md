This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/pipeline.py, src/cloud_pipeline.py, src/config.py, main.py, service.py, worker.py
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
main.py
service.py
src/cloud_pipeline.py
src/config.py
src/pipeline.py
worker.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="worker.py">
#!/usr/bin/env python3
"""
Worker service for Repository Research Tool.
Runs distributed workers that process repositories and LLM analysis tasks.
"""

import os
import sys
import time
import signal
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from config import ScraperConfig
from cloud_pipeline import CloudRepositoryPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorkerService:
    """
    Worker service that runs distributed processing workers.
    Processes repositories and LLM analysis tasks from Redis queues.
    """
    
    def __init__(self):
        """Initialize the worker service."""
        self.config = ScraperConfig()
        self.pipeline = None
        self.running = False
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Worker service initialized")
        logger.info(f"Configuration: {self.config.REPOMIX_WORKERS} repomix workers, {self.config.LLM_WORKERS} LLM workers")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    def start(self):
        """Start the worker service."""
        logger.info("🚀 Starting Repository Research Tool Worker Service")
        logger.info(f"   Deployment mode: {self.config.DEPLOYMENT_MODE}")
        logger.info(f"   Redis URL: {self.config.REDIS_URL}")
        logger.info(f"   Workers: {self.config.REPOMIX_WORKERS} repomix + {self.config.LLM_WORKERS} LLM")
        
        try:
            # Initialize cloud pipeline for worker operations
            self.pipeline = CloudRepositoryPipeline(config=self.config)
            
            # Start worker processes
            self.pipeline.start_workers()
            
            self.running = True
            logger.info("✅ Worker service started successfully")
            
            # Keep the service running
            self._run_worker_loop()
            
        except Exception as e:
            logger.error(f"❌ Failed to start worker service: {e}")
            raise
        finally:
            self._shutdown()
    
    def _run_worker_loop(self):
        """Main worker loop that keeps the service running."""
        logger.info("Worker service running... Press Ctrl+C to stop")
        
        try:
            while self.running:
                # Monitor worker health and restart if needed
                self._check_worker_health()
                
                # Sleep for a short interval
                time.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
            self.running = False
    
    def _check_worker_health(self):
        """Check worker process health and restart if needed."""
        if not self.pipeline:
            return
        
        try:
            # Check repomix workers
            dead_repomix = []
            for i, process in enumerate(self.pipeline.repomix_processes):
                if not process.is_alive():
                    dead_repomix.append(i)
            
            # Check LLM workers
            dead_llm = []
            for i, process in enumerate(self.pipeline.llm_processes):
                if not process.is_alive():
                    dead_llm.append(i)
            
            # Log any dead workers
            if dead_repomix:
                logger.warning(f"Dead repomix workers detected: {dead_repomix}")
            if dead_llm:
                logger.warning(f"Dead LLM workers detected: {dead_llm}")
            
            # In a production system, you might restart dead workers here
            # For now, we just log the issue
            
        except Exception as e:
            logger.error(f"Error checking worker health: {e}")
    
    def _shutdown(self):
        """Shutdown the worker service gracefully."""
        logger.info("🛑 Shutting down worker service...")
        
        try:
            if self.pipeline:
                self.pipeline.stop_workers()
                logger.info("✅ All workers stopped")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        logger.info("Worker service shutdown complete")

def main():
    """Main entry point for the worker service."""
    try:
        # Check if we're in cloud mode
        config = ScraperConfig()
        if not config.USE_CLOUD_SERVICES:
            logger.error("❌ Worker service requires cloud services to be enabled")
            logger.error("   Set DEPLOYMENT_MODE=cloud or USE_CLOUD_SERVICES=true")
            return 1
        
        # Start the worker service
        worker_service = WorkerService()
        worker_service.start()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Worker service interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"❌ Worker service failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="main.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Main Entry Point

Usage:
    python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
    python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze this code for AI patterns"
"""

import argparse
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_custom_prompt_template():
    """Create a template for custom prompts with placeholders"""
    return """CUSTOM REPOSITORY ANALYSIS

Repository: {repo_name}
File size: {file_size_mb:.2f}MB

[Your custom analysis instructions here]

Code content:
{content}
"""

def main():
    """Main entry point for the repository research tool"""
    parser = argparse.ArgumentParser(
        description="Repository Research Tool - Analyze GitHub repositories with AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
  python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze for AI patterns"
  python main.py --keywords "web frameworks" --min-stars 100 --max-repos 5 --debug

Custom Prompt Template:
  Use {repo_name}, {file_size_mb}, and {content} as placeholders in your custom prompt.
        """
    )
    
    # Required arguments
    parser.add_argument(
        '--keywords',
        required=True,
        help='Comma-separated list of keywords to search for (e.g., "cursor rules,code prompts")'
    )
    
    parser.add_argument(
        '--min-stars',
        type=int,
        default=30,
        help='Minimum number of stars for repositories (default: 30)'
    )
    
    parser.add_argument(
        '--max-repos',
        type=int,
        default=15,
        help='Maximum number of repositories per keyword (default: 15)'
    )
    
    # Optional arguments
    parser.add_argument(
        '--custom-prompt',
        help='Custom LLM analysis prompt. Use {repo_name}, {file_size_mb}, and {content} as placeholders.'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode with verbose logging'
    )
    
    parser.add_argument(
        '--output-dir',
        help='Custom output directory name (default: auto-generated timestamp)'
    )
    
    args = parser.parse_args()
    
    # Parse keywords
    keywords = [k.strip() for k in args.keywords.split(',')]
    
    print("REPOSITORY RESEARCH TOOL")
    print("=" * 60)
    print(f"Configuration:")
    print(f"   Keywords: {keywords}")
    print(f"   Min stars: {args.min_stars}")
    print(f"   Max repos per keyword: {args.max_repos}")
    print(f"   Expected total repos: ~{len(keywords) * args.max_repos}")
    print(f"   Custom prompt: {'Yes' if args.custom_prompt else 'No (using default)'}")
    print(f"   Debug mode: {'Yes' if args.debug else 'No'}")
    
    try:
        # Import and configure
        from config import ScraperConfig
        from pipeline import RepositoryPipeline
        from github_search import GitHubSearcher
        from sentry_config import init_sentry

        # Initialize Sentry monitoring
        init_sentry()

        # Load configuration
        config = ScraperConfig()
        
        # Set custom prompt if provided
        if args.custom_prompt:
            config.CUSTOM_LLM_PROMPT = args.custom_prompt
            print(f"Custom prompt configured")
        
        # Validate API key
        if not config.LLM_API_KEY:
            print("Error: LLM_API_KEY not configured in .env file")
            print("   Please add your Gemini API key to .env file")
            return 1

        print(f"Configuration loaded:")
        print(f"   API key: Configured")
        print(f"   Repomix workers: {config.REPOMIX_WORKERS}")
        print(f"   LLM workers: {config.LLM_WORKERS}")
        
        # Search for repositories
        print(f"\nSearching for repositories (sorted by last updated)...")
        searcher = GitHubSearcher(config)
        
        all_repositories = []
        for keyword in keywords:
            print(f"   Searching for: '{keyword}'")
            repos = searcher.search_repositories(
                keyword, 
                min_stars=args.min_stars, 
                max_repos=args.max_repos
            )
            print(f"   Found {len(repos)} repositories for '{keyword}'")
            
            # Show first few repos
            for i, repo in enumerate(repos[:3]):
                updated_at = repo.get('updated_at', 'unknown')
                print(f"      {i+1}. {repo.get('full_name', 'unknown')} ({repo.get('stargazers_count', 0)} stars, updated: {updated_at[:10]})")
            
            if len(repos) > 3:
                print(f"      ... and {len(repos) - 3} more repositories")
            
            all_repositories.extend(repos)
        
        print(f"\nTotal repositories to process: {len(all_repositories)}")

        if len(all_repositories) == 0:
            print("No repositories found - check keywords and min-stars filter")
            return 1

        # Initialize pipeline
        print(f"\nInitializing Pipeline...")
        first_keyword = keywords[0].replace(' ', '_').replace('-', '_')
        if args.output_dir:
            # Custom output directory logic would go here
            pipeline = RepositoryPipeline(config=config, first_keyword=first_keyword)
        else:
            pipeline = RepositoryPipeline(config=config, first_keyword=first_keyword)
        
        print(f"Pipeline initialized")
        print(f"   Output directory: {pipeline.output_dir}")
        
        # Run the pipeline
        print(f"\nRunning Complete Pipeline...")
        print(f"   Processing {len(all_repositories)} repositories")
        print(f"   Using {config.REPOMIX_WORKERS} repomix workers")
        print(f"   Using {config.LLM_WORKERS} LLM workers")
        print(f"   Expected time: ~{len(all_repositories) * 1.5} minutes")
        print("=" * 60)

        # Run the pipeline
        pipeline.run(all_repositories)

        print("=" * 60)
        print(f"ANALYSIS COMPLETED!")
        
        # Show results
        analysis_file = pipeline.output_dir / "analysis.json"
        if analysis_file.exists():
            file_size = analysis_file.stat().st_size
            print(f"Results saved to: {pipeline.output_dir}")
            print(f"   Main analysis: analysis.json ({file_size:,} bytes)")
            print(f"   Repomix files: repomixes/ directory")
            print(f"   Error tracking: sentry_analysis.json")
        else:
            print(f"Analysis file not found - check for errors")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\nAnalysis interrupted by user")
        return 0

    except Exception as e:
        print(f"\nAnalysis failed: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="src/cloud_pipeline.py">
"""
Cloud-integrated pipeline for Repository Research Tool.
Uses Redis queues, Supabase database, and cloud storage.
"""

import os
import uuid
import time
import json
import logging
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any

from config import ScraperConfig
from redis_queue import QueueManager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord, AnalysisRecord
from storage_manager import create_storage_manager
from monitoring import RepositoryMonitor
from llm_rate_limiter import LLMRateLimiter

# Import worker functions from original pipeline
from pipeline import repomix_worker, llm_worker

logger = logging.getLogger(__name__)

class CloudRepositoryPipeline:
    """
    Cloud-native pipeline for repository processing.
    Uses Redis for distributed queues, Supabase for persistence, and cloud storage.
    """
    
    def __init__(self, config=None, job_id=None):
        """
        Initialize the cloud pipeline.
        
        Args:
            config: Configuration object (defaults to ScraperConfig)
            job_id: Unique job identifier (generated if not provided)
        """
        self.config = config or ScraperConfig()
        self.job_id = job_id or str(uuid.uuid4())
        
        # Initialize cloud services
        self._initialize_cloud_services()
        
        # Initialize local components
        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None
        
        # Create local output directory for temporary files
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
        self.output_dir = Path(self.config.OUTPUT_BASE) / f"cloud_{timestamp_str}"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
        
        logger.info(f"Initialized cloud pipeline for job {self.job_id}")
    
    def _initialize_cloud_services(self):
        """Initialize cloud service connections."""
        try:
            # Initialize queue manager
            self.queue_manager = QueueManager(self.config.REDIS_URL)
            self.repo_queue = self.queue_manager.get_repository_queue()
            self.file_queue = self.queue_manager.get_file_queue()

            # Initialize distributed rate limiter with Redis client
            self.rate_limiter = LLMRateLimiter(
                redis_client=self.queue_manager.redis_client,
                rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN,
                rate_limit_requests_per_min=getattr(self.config, 'LLM_RATE_LIMIT_REQUESTS_PER_MIN', 1000)
            )

            # Initialize Supabase client
            self.supabase_client = create_supabase_client()

            # Initialize storage manager
            self.storage_manager = create_storage_manager()
            
            logger.info("Cloud services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cloud services: {e}")
            raise
    
    def create_job(self, keywords: str, min_stars: int, max_repos: int,
                   custom_prompt: Optional[str] = None, debug: bool = False) -> JobRecord:
        """
        Create a new analysis job in the database.
        
        Args:
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            custom_prompt: Optional custom LLM prompt
            debug: Debug mode flag
            
        Returns:
            Created job record
        """
        try:
            job = JobRecord(
                id=self.job_id,
                keywords=keywords,
                min_stars=min_stars,
                max_repos=max_repos,
                status="pending",
                created_at=datetime.now().isoformat(),
                custom_prompt=custom_prompt,
                debug=debug
            )
            
            created_job = self.supabase_client.create_job(job)
            logger.info(f"Created job {self.job_id} in database")
            return created_job
            
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def add_repositories(self, repositories: List[Dict[str, Any]]) -> int:
        """
        Add repositories to the processing queue and database.
        
        Args:
            repositories: List of repository information dictionaries
            
        Returns:
            Number of repositories added
        """
        try:
            added_count = 0
            
            for repo_info in repositories:
                # Create repository record in database
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=self.job_id,
                    name=repo_info.get('full_name', 'unknown'),
                    url=repo_info.get('html_url', ''),
                    stars=repo_info.get('stargazers_count', 0),
                    file_size_mb=0.0  # Will be updated after repomix processing
                )
                
                self.supabase_client.create_repository(repo_record)
                
                # Add to Redis queue
                queue_item = RepositoryQueueItem(
                    repository_url=repo_info.get('html_url', ''),
                    repository_name=repo_info.get('full_name', 'unknown'),
                    job_id=self.job_id
                )
                
                self.repo_queue.put(queue_item)
                added_count += 1
            
            # Update job with total repositories
            self.supabase_client.update_job_status(
                self.job_id, 
                "running", 
                total_repositories=added_count
            )
            
            logger.info(f"Added {added_count} repositories to queue and database")
            return added_count
            
        except Exception as e:
            logger.error(f"Failed to add repositories: {e}")
            raise
    
    def start_workers(self):
        """Start repomix and LLM worker processes."""
        try:
            # Start repomix workers
            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=self._cloud_repomix_worker,
                    args=(i,)
                )
                p.start()
                self.repomix_processes.append(p)
            
            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=self._cloud_llm_worker,
                    args=(i,)
                )
                p.start()
                self.llm_processes.append(p)
            
            logger.info(f"Started {len(self.repomix_processes)} repomix and {len(self.llm_processes)} LLM workers")
            
        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            raise
    
    def _cloud_repomix_worker(self, worker_id: int):
        """
        Cloud-integrated repomix worker.
        
        Args:
            worker_id: Worker identifier
        """
        logger.info(f"Starting cloud repomix worker {worker_id}")
        
        while True:
            try:
                # Get repository from queue
                repo_item = self.repo_queue.get(timeout=30)
                if repo_item is None:
                    break
                
                logger.info(f"Worker {worker_id} processing {repo_item.repository_name}")
                
                # Process repository with repomix (reuse existing logic)
                # This would call the existing repomix processing logic
                # but upload results to cloud storage instead of local files
                
                # Simulate repomix processing for now
                repomix_content = f"# Repomix output for {repo_item.repository_name}\n\nProcessed by worker {worker_id}"
                
                # Upload to cloud storage
                repomix_url = self.storage_manager.upload_repomix_file(
                    job_id=self.job_id,
                    repository_name=repo_item.repository_name.replace('/', '_'),
                    content=repomix_content
                )
                
                # Update repository record
                # Find repository by name and job_id
                repositories = self.supabase_client.get_repositories_by_job(self.job_id)
                repo_record = next((r for r in repositories if r.name == repo_item.repository_name), None)
                
                if repo_record:
                    self.supabase_client.update_repository_status(
                        repo_record.id,
                        "completed",
                        repomix_file_url=repomix_url
                    )
                
                # Add to LLM queue
                file_item = FileQueueItem(
                    file_path=repomix_url,
                    repository_name=repo_item.repository_name,
                    job_id=self.job_id
                )
                self.file_queue.put(file_item)
                
                # Mark task as done
                self.repo_queue.task_done(repo_item)
                
                logger.info(f"Worker {worker_id} completed {repo_item.repository_name}")
                
            except Exception as e:
                logger.error(f"Repomix worker {worker_id} error: {e}")
                if 'repo_item' in locals():
                    self.repo_queue.task_failed(repo_item, str(e))
                break
    
    def _cloud_llm_worker(self, worker_id: int):
        """
        Cloud-integrated LLM worker.
        
        Args:
            worker_id: Worker identifier
        """
        logger.info(f"Starting cloud LLM worker {worker_id}")
        
        while True:
            try:
                # Get file from queue
                file_item = self.file_queue.get(timeout=30)
                if file_item is None:
                    break
                
                logger.info(f"LLM worker {worker_id} processing {file_item.repository_name}")
                
                # Download file content
                repomix_content = self.storage_manager.download_repomix_file(file_item.file_path)

                # Calculate file size for rate limiting
                file_size_mb = len(repomix_content.encode('utf-8')) / (1024 * 1024)

                # Wait for rate limit slot
                if not self.rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300):
                    logger.warning(f"LLM worker {worker_id} timed out waiting for rate limit slot")
                    continue

                logger.info(f"LLM worker {worker_id} acquired rate limit slot for {file_item.repository_name} ({file_size_mb:.2f}MB)")

                # Process with LLM (reuse existing logic)
                # This would call the existing LLM processing logic

                # Simulate LLM analysis for now
                analysis_content = f"Analysis of {file_item.repository_name} by LLM worker {worker_id}"
                
                # Create analysis record
                analysis_record = AnalysisRecord(
                    id=str(uuid.uuid4()),
                    repository_id="",  # Would need to look up repository ID
                    job_id=self.job_id,
                    worker_id=str(worker_id),
                    processed_at=datetime.now().isoformat(),
                    analysis_content=analysis_content,
                    analysis_length=len(analysis_content)
                )
                
                self.supabase_client.create_analysis(analysis_record)
                
                # Mark task as done
                self.file_queue.task_done(file_item)
                
                logger.info(f"LLM worker {worker_id} completed {file_item.repository_name}")
                
            except Exception as e:
                logger.error(f"LLM worker {worker_id} error: {e}")
                if 'file_item' in locals():
                    self.file_queue.task_failed(file_item, str(e))
                break
    
    def wait_for_completion(self, timeout: Optional[int] = None):
        """
        Wait for all processing to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
        """
        try:
            start_time = time.time()
            
            while True:
                # Check queue statistics
                stats = self.queue_manager.get_all_stats()
                repo_pending = stats.get('repositories', {}).get('pending', 0)
                file_pending = stats.get('files', {}).get('pending', 0)
                
                if repo_pending == 0 and file_pending == 0:
                    logger.info("All processing completed")
                    break
                
                if timeout and (time.time() - start_time) > timeout:
                    logger.warning(f"Timeout reached after {timeout} seconds")
                    break
                
                logger.info(f"Waiting... Repos: {repo_pending}, Files: {file_pending}")
                time.sleep(10)
            
        except Exception as e:
            logger.error(f"Error waiting for completion: {e}")
            raise
    
    def finalize_job(self) -> str:
        """
        Finalize the job by creating the final analysis file.
        
        Returns:
            URL of the final analysis file
        """
        try:
            # Get all analyses for this job
            analyses = self.supabase_client.get_analyses_by_job(self.job_id)
            repositories = self.supabase_client.get_repositories_by_job(self.job_id)
            
            # Create final analysis data
            analysis_data = {
                "metadata": {
                    "job_id": self.job_id,
                    "generated": datetime.now().isoformat(),
                    "total_repositories": len(repositories),
                    "total_analyses": len(analyses),
                    "processing_configuration": {
                        "repomix_workers": self.config.REPOMIX_WORKERS,
                        "llm_workers": self.config.LLM_WORKERS,
                        "deployment_mode": self.config.DEPLOYMENT_MODE
                    }
                },
                "analyses": []
            }
            
            # Add analyses to final data
            for analysis in analyses:
                repo = next((r for r in repositories if r.id == analysis.repository_id), None)
                if repo:
                    analysis_data["analyses"].append({
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url
                        },
                        "processing": {
                            "worker_id": analysis.worker_id,
                            "processed_at": analysis.processed_at,
                            "analysis_length": analysis.analysis_length
                        },
                        "analysis": {
                            "content": analysis.analysis_content
                        }
                    })
            
            # Upload final analysis file
            analysis_url = self.storage_manager.upload_analysis_file(
                job_id=self.job_id,
                analysis_data=analysis_data,
                filename="final_analysis.json"
            )
            
            # Update job status
            self.supabase_client.update_job_status(
                self.job_id,
                "completed",
                analysis_file_url=analysis_url
            )
            
            logger.info(f"Job {self.job_id} finalized with analysis file: {analysis_url}")
            return analysis_url
            
        except Exception as e:
            logger.error(f"Failed to finalize job: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
    
    def stop_workers(self):
        """Stop all worker processes."""
        try:
            # Send poison pills to stop workers
            for _ in range(len(self.repomix_processes)):
                self.repo_queue.put(None)
            
            for _ in range(len(self.llm_processes)):
                self.file_queue.put(None)
            
            # Wait for processes to finish
            for p in self.repomix_processes + self.llm_processes:
                p.join(timeout=10)
                if p.is_alive():
                    p.terminate()
            
            logger.info("All workers stopped")
            
        except Exception as e:
            logger.error(f"Error stopping workers: {e}")
    
    def run(self, repositories: List[Dict[str, Any]], keywords: str, 
            min_stars: int, max_repos: int, **kwargs) -> str:
        """
        Run the complete cloud pipeline.
        
        Args:
            repositories: List of repository information
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            **kwargs: Additional job parameters
            
        Returns:
            URL of the final analysis file
        """
        try:
            # Create job
            self.create_job(keywords, min_stars, max_repos, **kwargs)
            
            # Add repositories
            self.add_repositories(repositories)
            
            # Start workers
            self.start_workers()
            
            # Wait for completion
            self.wait_for_completion(timeout=3600)  # 1 hour timeout
            
            # Finalize job
            analysis_url = self.finalize_job()
            
            return analysis_url
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
        finally:
            # Always stop workers
            self.stop_workers()
</file>

<file path="src/config.py">
import os
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    # Look for .env file in project root
    env_path = Path(__file__).parent.parent / '.env'
    load_dotenv(env_path)
    print(f"Loaded environment variables from {env_path}")
except ImportError:
    print("python-dotenv not installed, using system environment variables")
except Exception as e:
    print(f"Could not load .env file: {e}")

class ScraperConfig:
    # GitHub Configuration
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
    MAX_REPOS_PER_KEYWORD = int(os.getenv('MAX_REPOS_PER_KEYWORD', '20'))
    MIN_STARS = int(os.getenv('MIN_STARS', '30'))

    # Repomix Configuration
    REPOMIX_WORKERS = int(os.getenv('REPOMIX_WORKERS', '15'))
    FILE_INCLUDES = "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"

    # Chunking Configuration
    MAX_FILE_SIZE_MB = 3
    MAX_CHUNKS_TO_RETAIN = 3

    # LLM Configuration
    LLM_API_KEY = os.getenv('LLM_API_KEY')
    LLM_BASE_URL = os.getenv('LLM_BASE_URL', 'https://generativelanguage.googleapis.com/v1beta')
    LLM_MODEL = os.getenv('LLM_MODEL', 'gemini-2.0-flash')
    LLM_WORKERS = int(os.getenv('LLM_WORKERS', '4'))
    LLM_RATE_LIMIT_MB_PER_MIN = 12

    # Output Configuration
    OUTPUT_BASE = os.getenv('OUTPUT_BASE', 'output')

    # Cloud Configuration
    DEPLOYMENT_MODE = os.getenv('DEPLOYMENT_MODE', 'local')  # local, cloud
    USE_CLOUD_SERVICES = DEPLOYMENT_MODE == 'cloud'

    # Redis Configuration
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    REDIS_QUEUE_TIMEOUT = int(os.getenv('REDIS_QUEUE_TIMEOUT', '300'))

    # Supabase Configuration
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY')  # Service role key
    SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')  # Anonymous key

    # Storage Configuration
    STORAGE_BUCKET_REPOMIXES = os.getenv('STORAGE_BUCKET_REPOMIXES', 'repomixes')
    STORAGE_BUCKET_ANALYSES = os.getenv('STORAGE_BUCKET_ANALYSES', 'analyses')
    STORAGE_BUCKET_LOGS = os.getenv('STORAGE_BUCKET_LOGS', 'logs')

    # Enhanced LLM Configuration
    LLM_TIMEOUT = int(os.getenv('LLM_TIMEOUT', '90'))
    LLM_MAX_OUTPUT_TOKENS = int(os.getenv('LLM_MAX_OUTPUT_TOKENS', '8192'))

    # Custom prompt override
    CUSTOM_LLM_PROMPT = os.getenv('CUSTOM_LLM_PROMPT')

    # Default comprehensive analysis prompt
    DEFAULT_LLM_PROMPT = """Analyze this repository comprehensively and provide a detailed summary covering:

1. **Main Purpose and Functionality**: What does this repository do? What problem does it solve?

2. **Key Technologies and Frameworks Used**: List and briefly describe the main technologies, frameworks, libraries, and tools used.

3. **Architecture Overview**: Describe the overall architecture, design patterns, and structure of the codebase.

4. **Notable Features or Patterns**: Highlight interesting implementation details, design patterns, or unique approaches used.

5. **Potential Use Cases**: Describe scenarios where this repository could be useful or applied.

Please be comprehensive and specific, focusing on the actual implementation details and functionality rather than generic descriptions."""
</file>

<file path="src/pipeline.py">
import multiprocessing
import subprocess
import os
import tempfile
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import json
import time
import logging
import shutil
import random
from pathlib import Path
from datetime import datetime

# Sentry integration
from sentry_config import trace_api_call, log_worker_start, log_rate_limit_event
from monitoring import RepositoryMonitor
import sentry_sdk

from config import ScraperConfig
from utils import get_file_size_mb, chunk_and_retain_file
from llm_rate_limiter import LLMRateLimiter


def repomix_worker(repo_queue, file_queue, config, output_dir, monitor=None):
    """
    Worker function to process repositories using repomix.

    Args:
        repo_queue: Queue containing repository information to process
        file_queue: Queue to add processed files to
        config: Configuration object with repomix settings
        output_dir: Timestamped output directory path
        monitor: RepositoryMonitor instance for logging
    """
    # Initialize Sentry context for this worker
    worker_id = os.getpid()
    log_worker_start("repomix", worker_id)

    while True:
        try:
            # Get repository from queue
            repo_info = repo_queue.get()
            if repo_info is None:  # Poison pill to stop worker
                repo_queue.task_done()
                break

            repo_url = repo_info.get('html_url', repo_info.get('clone_url', ''))
            repo_name = repo_info.get('full_name', 'unknown')

            start_time = time.time()
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] REPOMIX Worker {worker_id}: Starting {repo_name}")

            # Log to monitor
            if monitor:
                monitor.log_repository_start(repo_name, worker_id, "repomix")

            # Use the timestamped repomixes directory
            repomixes_dir = Path(output_dir) / 'repomixes'
            repomixes_dir.mkdir(parents=True, exist_ok=True)

            safe_repo_name = repo_name.replace('/', '_')
            output_file = repomixes_dir / f"{safe_repo_name}.md"

            # Find npx executable
            npx_path = shutil.which('npx')
            if not npx_path:
                print(f"❌ npx not found in PATH")
                continue

            # Run repomix command
            cmd = [
                npx_path, 'repomix', '--remote', repo_url,
                '--include', config.FILE_INCLUDES,
                '--output', str(output_file)
            ]

            try:
                    repomix_start = time.time()
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] REPOMIX Worker {worker_id}: Running repomix for {repo_name}")

                    # Ensure PATH is available for subprocess with proper encoding
                    env = os.environ.copy()
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=600, env=env, encoding='utf-8', errors='replace')

                    repomix_end = time.time()
                    repomix_duration = repomix_end - repomix_start
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]

                    if result.returncode == 0 and os.path.exists(output_file):
                        # Check file size and chunk if necessary
                        file_size_mb = get_file_size_mb(output_file)
                        
                        if file_size_mb > config.MAX_FILE_SIZE_MB:
                            # Chunk the file
                            chunk_files = chunk_and_retain_file(
                                output_file, 
                                config.MAX_FILE_SIZE_MB, 
                                config.MAX_CHUNKS_TO_RETAIN
                            )
                            
                            # Add each chunk to the file queue
                            for chunk_file in chunk_files:
                                file_queue.put({
                                    'file_path': chunk_file,
                                    'repo_name': repo_name,
                                    'is_chunk': True,
                                    'original_size_mb': file_size_mb
                                })
                        else:
                            # Add the original file to the queue (keep in repomixes directory)
                            file_queue.put({
                                'file_path': str(output_file),
                                'repo_name': repo_name,
                                'is_chunk': False,
                                'original_size_mb': file_size_mb
                            })
                        
                        total_duration = time.time() - start_time
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] REPOMIX Worker {worker_id}: ✅ Completed {repo_name} ({file_size_mb:.2f}MB) - Repomix: {repomix_duration:.1f}s, Total: {total_duration:.1f}s")

                        # Log success to monitor
                        if monitor:
                            monitor.log_repository_success(repo_name, worker_id, "repomix", total_duration, file_size_mb)
                    else:
                        total_duration = time.time() - start_time
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        error_msg = result.stderr[:100] if result.stderr else "Unknown error"
                        print(f"[{timestamp}] REPOMIX Worker {worker_id}: ❌ Failed {repo_name} - {error_msg} - Duration: {total_duration:.1f}s")

                        # Log failure to monitor
                        if monitor:
                            monitor.log_repository_failure(repo_name, worker_id, "repomix", error_msg, total_duration)

            except subprocess.TimeoutExpired:
                print(f"Timeout processing {repo_name}")
            except Exception as e:
                print(f"Error processing {repo_name}: {e}")
            
            repo_queue.task_done()
            
        except Exception as e:
            print(f"Repomix worker error: {e}")
            repo_queue.task_done()


def create_robust_session():
    """Create a requests session with connection pooling and retry logic."""
    session = requests.Session()

    # Configure retry strategy for connection issues
    retry_strategy = Retry(
        total=5,  # Total number of retries
        backoff_factor=2,  # Exponential backoff: 2, 4, 8, 16, 32 seconds
        status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry
        allowed_methods=["POST"],  # Only retry POST requests
        raise_on_status=False  # Don't raise exception on retry exhaustion
    )

    # Configure HTTP adapter with connection pooling
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,  # Number of connection pools
        pool_maxsize=20,  # Max connections per pool
        pool_block=False  # Don't block when pool is full
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session

def llm_worker(file_queue, rate_limiter, config, analysis_file, monitor=None):
    """
    Worker function to analyze files using LLM API.

    Args:
        file_queue: Queue containing files to analyze
        rate_limiter: Rate limiter for LLM API calls
        config: Configuration object with LLM settings
        analysis_file: Path to the single analysis file to append to
        monitor: RepositoryMonitor instance for logging
    """
    # Initialize Sentry context for this worker
    worker_id = os.getpid()
    log_worker_start("llm", worker_id)

    # Create robust session for this worker
    session = create_robust_session()

    while True:
        try:
            # Get file from queue
            file_info = file_queue.get()
            if file_info is None:  # Poison pill to stop worker
                file_queue.task_done()
                break

            file_path = file_info['file_path']
            repo_name = file_info['repo_name']
            file_size_mb = get_file_size_mb(file_path)

            start_time = time.time()
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] LLM Worker {worker_id}: Starting analysis of {repo_name} ({file_size_mb:.2f}MB)")

            # Acquire rate limiting slot
            rate_limit_start = time.time()
            try:
                # Use wait_for_slot for blocking behavior (maintains backward compatibility)
                rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300)
                rate_limit_duration = time.time() - rate_limit_start

                if rate_limit_duration > 1.0:
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] LLM Worker {worker_id}: Rate limit wait: {rate_limit_duration:.1f}s for {repo_name}")
            except TimeoutError:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                print(f"[{timestamp}] LLM Worker {worker_id}: ❌ Rate limit timeout for {repo_name}, skipping")
                continue
            
            try:
                # Read file content
                logger.info(f"📖 LLM FLOW: Reading file content: {os.path.basename(file_path)}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                content_size = len(content)
                logger.info(f"📏 LLM FLOW: File content size: {content_size:,} characters")

                # Prepare LLM API request
                logger.info(f"🤖 LLM FLOW: Preparing Gemini API request for {repo_name}")
                prompt = f"""Analyze this repository code and provide a comprehensive summary:

Repository: {repo_name}
File size: {file_size_mb:.2f}MB

Please provide:
1. Main purpose and functionality
2. Key technologies and frameworks used
3. Architecture overview
4. Notable features or patterns
5. Potential use cases

Code content:
{content[:50000]}  # Limit content to avoid token limits
"""
                
                # Make LLM API call
                api_start = time.time()
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                print(f"[{timestamp}] LLM Worker {worker_id}: Making API call for {repo_name}")

                headers = {
                    'Content-Type': 'application/json'
                }

                # Gemini API format
                data = {
                    'contents': [{
                        'parts': [{'text': prompt}]
                    }],
                    'generationConfig': {
                        'maxOutputTokens': 4000,
                        'temperature': 0.3
                    }
                }

                # Enhanced retry mechanism: UNLIMITED retries for all errors
                response = None
                rate_limit_retries = 0
                timeout_retries = 0
                network_error_retries = 0
                connection_error_retries = 0
                other_error_retries = 0

                logger.info(f"🌐 LLM FLOW: Starting Gemini API call for {repo_name}")

                with trace_api_call("gemini_analysis", repo_name) as tracer:
                    tracer.add_breadcrumb(f"Starting API call for {repo_name}")

                    # UNLIMITED retries - keep trying until success
                    logger.info(f"🔄 LLM FLOW: Entering retry loop for API call")
                    while True:
                        try:
                            # DEBUG: Log the exact request details
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Making request to: {config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - API Key: {config.LLM_API_KEY[:20]}...")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Prompt size: {len(prompt):,} chars")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Timeout: 30s")

                            response = requests.post(
                                f"{config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent?key={config.LLM_API_KEY}",
                                headers=headers,
                                json=data,
                                timeout=30  # Simple 30-second timeout like the working test
                            )

                            # DEBUG: Log response received
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Response received: {response.status_code}")

                            if response.status_code == 200:
                                tracer.set_status(200)
                                tracer.add_breadcrumb("API call successful")
                                break  # Success

                            elif response.status_code == 429:  # Rate limit - stall and retry
                                rate_limit_retries += 1
                                retry_delay = 5  # Always 5 seconds for rate limits
                                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                                print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Rate limited, stalling for {retry_delay}s (stall #{rate_limit_retries})")

                                # Log to Sentry
                                log_rate_limit_event(worker_id, repo_name, retry_delay, rate_limit_retries)
                                tracer.add_breadcrumb(f"Rate limited - stall #{rate_limit_retries}")

                                time.sleep(retry_delay)
                                continue  # Retry immediately

                            else:  # Other API error - stall and retry
                                other_error_retries += 1
                                error_msg = f"Status {response.status_code}"
                                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                                print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ API error {response.status_code}, retrying in 10s (error #{other_error_retries})")

                                tracer.set_status(response.status_code, error_msg)
                                tracer.add_breadcrumb(f"API error {response.status_code} - error #{other_error_retries}")

                                time.sleep(10)  # 10 second delay for API errors
                                continue  # Retry immediately

                        except requests.exceptions.Timeout:
                            timeout_retries += 1
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: ⏰ Timeout, retrying in 15s (timeout #{timeout_retries})")

                            tracer.add_breadcrumb(f"Timeout - retry #{timeout_retries}")

                            time.sleep(15)  # 15 second delay for timeouts
                            continue  # Retry immediately

                        except requests.exceptions.ConnectionError as e:
                            # Handle connection reset errors specifically
                            connection_error_retries += 1
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]

                            if "10054" in str(e) or "forcibly closed" in str(e).lower():
                                print(f"[{timestamp}] LLM Worker {worker_id}: 🔌 Connection reset by remote host, retrying in 30s (reset #{connection_error_retries})")
                                tracer.add_breadcrumb(f"Connection reset - retry #{connection_error_retries}")
                                time.sleep(30)  # Longer delay for connection resets
                            else:
                                print(f"[{timestamp}] LLM Worker {worker_id}: 🌐 Connection error: {str(e)[:100]}, retrying in 25s (conn #{connection_error_retries})")
                                tracer.add_breadcrumb(f"Connection error - retry #{connection_error_retries}: {str(e)[:100]}")
                                time.sleep(25)  # 25 second delay for connection errors

                            continue  # Retry immediately

                        except requests.exceptions.RequestException as e:
                            network_error_retries += 1
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🌐 Network error: {str(e)[:100]}, retrying in 20s (network #{network_error_retries})")

                            tracer.add_breadcrumb(f"Network error - retry #{network_error_retries}: {str(e)[:100]}")

                            time.sleep(20)  # 20 second delay for network errors
                            continue  # Retry immediately

                    # If we get here, we have a successful response
                    # Continue to process the response (don't break!)

                api_duration = time.time() - api_start

                if response and response.status_code == 200:
                    try:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Processing successful response...")

                        result = response.json()
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - JSON parsed successfully")

                        # Gemini API response format
                        candidates = result.get('candidates', [])
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Found {len(candidates)} candidates")

                        if candidates and 'content' in candidates[0]:
                            analysis = candidates[0]['content']['parts'][0]['text']
                            analysis_length = len(analysis)
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Analysis extracted: {analysis_length} chars")
                            logger.info(f"✅ LLM FLOW: Analysis completed for {repo_name}: {analysis_length} characters")
                            logger.info(f"📊 LLM FLOW: Analysis quality: {'Good' if analysis_length > 500 else 'Short'} length")
                        else:
                            analysis = 'No analysis content available in response'
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Empty response for {repo_name}")
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Response structure: {json.dumps(result, indent=2)[:500]}...")
                    except (KeyError, IndexError, TypeError) as e:
                        analysis = f'Error parsing response: {str(e)}'
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] LLM Worker {worker_id}: ❌ Parse error for {repo_name}: {str(e)}")
                        print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Raw response: {response.text[:500]}...")
                    
                    # Append analysis to JSON file with retry
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Preparing analysis JSON...")

                    # Create analysis object
                    analysis_object = {
                        "repository": {
                            "name": repo_name,
                            "url": f"https://github.com/{repo_name}",
                            "file_size_mb": round(file_size_mb, 2),
                            "repomix_file": f"repomixes/{repo_name.replace('/', '_')}.md"
                        },
                        "processing": {
                            "processed_at": time.strftime('%Y-%m-%d %H:%M:%S'),
                            "worker_id": worker_id,
                            "analysis_length": len(analysis)
                        },
                        "analysis": {
                            "content": analysis.strip(),
                            "format": "structured_text"
                        }
                    }

                    print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Analysis JSON prepared: {len(str(analysis_object))} chars")

                    # Retry mechanism for JSON append
                    max_append_retries = 3
                    append_delay = 2

                    for append_attempt in range(max_append_retries):
                        try:
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Writing to JSON file: {analysis_file}")

                            # Read existing JSON, append new analysis, write back with file locking
                            import json
                            try:
                                import fcntl  # Unix-only, not available on Windows
                            except ImportError:
                                fcntl = None  # Windows compatibility
                            import tempfile
                            # import os  # REMOVED: Use module-level import to avoid scoping conflict

                            # Use atomic write with temporary file to prevent corruption
                            temp_file = None
                            try:
                                with open(analysis_file, 'r', encoding='utf-8') as f:
                                    data = json.load(f)

                                data["analyses"].append(analysis_object)

                                # Write to temporary file first, then atomic rename
                                temp_file = analysis_file.with_suffix('.tmp')
                                with open(temp_file, 'w', encoding='utf-8') as f:
                                    json.dump(data, f, indent=2, ensure_ascii=False)

                                # Atomic rename (Windows compatible)
                                if os.name == 'nt':  # Windows
                                    if analysis_file.exists():
                                        analysis_file.unlink()
                                    temp_file.rename(analysis_file)
                                else:  # Unix/Linux
                                    temp_file.rename(analysis_file)

                            except Exception as e:
                                # Clean up temp file if it exists
                                if temp_file and temp_file.exists():
                                    temp_file.unlink()
                                raise e

                            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - JSON write successful!")
                            break  # Success, exit retry loop
                        except (IOError, OSError, json.JSONDecodeError) as e:
                            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            if append_attempt < max_append_retries - 1:
                                print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ JSON write failed for {repo_name}, retrying in {append_delay}s (attempt {append_attempt + 1}/{max_append_retries})")
                                time.sleep(append_delay)
                            else:
                                print(f"[{timestamp}] LLM Worker {worker_id}: ❌ JSON write failed permanently for {repo_name}: {e}")
                    
                    total_duration = time.time() - start_time
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Analysis complete, marking task done...")
                    print(f"[{timestamp}] LLM Worker {worker_id}: ✅ Completed {repo_name} - API: {api_duration:.1f}s, Total: {total_duration:.1f}s")
                else:
                    total_duration = time.time() - start_time
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    error_msg = f"Status: {response.status_code if response else 'No response'}"
                    if response:
                        try:
                            error_detail = response.json().get('error', {}).get('message', 'Unknown error')
                            error_msg += f", Error: {error_detail[:100]}"
                        except:
                            error_msg += f", Response: {response.text[:100]}"
                    print(f"[{timestamp}] LLM Worker {worker_id}: ❌ API Error {repo_name}: {error_msg} - Duration: {total_duration:.1f}s")

                    # Log failed analysis to JSON file with retry
                    try:
                        failed_analysis_object = {
                            "repository": {
                                "name": repo_name,
                                "url": f"https://github.com/{repo_name}",
                                "file_size_mb": round(file_size_mb, 2),
                                "repomix_file": f"repomixes/{repo_name.replace('/', '_')}.md"
                            },
                            "processing": {
                                "failed_at": time.strftime('%Y-%m-%d %H:%M:%S'),
                                "worker_id": worker_id,
                                "error_message": error_msg,
                                "duration": round(total_duration, 1)
                            },
                            "analysis": {
                                "content": "ANALYSIS FAILED",
                                "format": "error",
                                "status": "failed"
                            }
                        }

                        # Retry mechanism for failed analysis logging
                        for append_attempt in range(3):
                            try:
                                import json
                                import tempfile
                                # import os  # REMOVED: Use module-level import to avoid scoping conflict

                                # Use atomic write with temporary file
                                temp_file = None
                                try:
                                    with open(analysis_file, 'r', encoding='utf-8') as f:
                                        data = json.load(f)

                                    data["analyses"].append(failed_analysis_object)

                                    # Write to temporary file first, then atomic rename
                                    temp_file = analysis_file.with_suffix('.tmp')
                                    with open(temp_file, 'w', encoding='utf-8') as f:
                                        json.dump(data, f, indent=2, ensure_ascii=False)

                                    # Atomic rename (Windows compatible)
                                    if os.name == 'nt':  # Windows
                                        if analysis_file.exists():
                                            analysis_file.unlink()
                                        temp_file.rename(analysis_file)
                                    else:  # Unix/Linux
                                        temp_file.rename(analysis_file)

                                except Exception as e:
                                    # Clean up temp file if it exists
                                    if temp_file and temp_file.exists():
                                        temp_file.unlink()
                                    raise e
                                break
                            except (IOError, OSError, json.JSONDecodeError) as e:
                                if append_attempt < 2:
                                    time.sleep(2)
                                else:
                                    print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Failed to log error for {repo_name}: {e}")
                    except Exception as log_error:
                        print(f"[{timestamp}] LLM Worker {worker_id}: ⚠️ Failed to log error for {repo_name}: {log_error}")
            
            except Exception as e:
                print(f"Error analyzing {repo_name}: {e}")
            
            finally:
                # Keep repomix files - don't delete them!
                # They are saved in repomixes/ directory for research purposes
                pass
            
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Calling task_done()...")
            file_queue.task_done()
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - task_done() completed successfully")

        except Exception as e:
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"[{timestamp}] LLM Worker {worker_id}: ❌ LLM worker error: {e}")
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - Calling task_done() after error...")
            file_queue.task_done()
            print(f"[{timestamp}] LLM Worker {worker_id}: 🔍 DEBUG - task_done() after error completed")


class RepositoryPipeline:
    """
    Main pipeline class to orchestrate repository processing.
    """
    
    def __init__(self, config=None, first_keyword=None):
        """
        Initialize the pipeline.

        Args:
            config: Configuration object (defaults to ScraperConfig)
            first_keyword: First keyword for directory naming (optional)
        """
        self.config = config or ScraperConfig()
        self.repo_queue = multiprocessing.JoinableQueue()
        self.file_queue = multiprocessing.JoinableQueue()
        # Initialize rate limiter with Redis if available
        redis_client = None
        if hasattr(self.config, 'REDIS_URL') and self.config.REDIS_URL:
            try:
                import redis
                redis_client = redis.from_url(self.config.REDIS_URL, decode_responses=True)
                redis_client.ping()  # Test connection
                print(f"✅ Connected to Redis for distributed rate limiting: {self.config.REDIS_URL}")
            except Exception as e:
                print(f"⚠️ Redis connection failed, using local rate limiting: {e}")
                redis_client = None

        self.rate_limiter = LLMRateLimiter(
            redis_client=redis_client,
            rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN
        )

        # Worker configuration
        self.repomix_workers = self.config.REPOMIX_WORKERS
        self.llm_workers = self.config.LLM_WORKERS
        self.rate_limit_mb_per_min = self.config.LLM_RATE_LIMIT_MB_PER_MIN

        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None

        # Create unique timestamped output directory with first keyword
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # Include microseconds for uniqueness

        if first_keyword:
            # Sanitize keyword for directory name
            safe_keyword = first_keyword.replace(' ', '-').replace('/', '-').replace('\\', '-')
            # Remove special characters and limit length
            safe_keyword = ''.join(c for c in safe_keyword if c.isalnum() or c in '-_')[:20]
            dir_name = f"{timestamp_str}_{safe_keyword}"
        else:
            dir_name = timestamp_str

        self.output_dir = Path(self.config.OUTPUT_BASE) / dir_name

        # Ensure directory doesn't exist (handle rare collision case)
        counter = 1
        original_output_dir = self.output_dir
        while self.output_dir.exists():
            self.output_dir = original_output_dir.parent / f"{dir_name}_{counter}"
            counter += 1

        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Create analysis file
        self.analysis_file = self.output_dir / "analysis.json"

        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
    
    def add_repositories(self, repositories):
        """
        Add repositories to the processing queue.
        
        Args:
            repositories: List of repository information dictionaries
        """
        for repo in repositories:
            self.repo_queue.put(repo)
    
    def start_workers(self):
        """Start all worker processes."""
        print(f"Starting {self.config.REPOMIX_WORKERS} repomix workers...")
        
        # Start repomix workers
        for i in range(self.config.REPOMIX_WORKERS):
            p = multiprocessing.Process(
                target=repomix_worker,
                args=(self.repo_queue, self.file_queue, self.config, self.output_dir, self.monitor)
            )
            p.start()
            self.repomix_processes.append(p)
        
        print(f"Starting {self.config.LLM_WORKERS} LLM workers...")
        
        # Start LLM workers
        for i in range(self.config.LLM_WORKERS):
            p = multiprocessing.Process(
                target=llm_worker,
                args=(self.file_queue, self.rate_limiter, self.config, self.analysis_file, self.monitor)
            )
            p.start()
            self.llm_processes.append(p)
    
    def stop_workers(self):
        """Stop all worker processes gracefully."""
        print("Stopping workers...")

        try:
            # Send poison pills to stop repomix workers
            for _ in self.repomix_processes:
                try:
                    self.repo_queue.put(None, timeout=1)
                except:
                    pass  # Queue might be full or closed

            # Wait for repomix workers to finish gracefully
            for p in self.repomix_processes:
                if p.is_alive():
                    p.join(timeout=5)
                    if p.is_alive():
                        print(f"Terminating repomix worker {p.pid}")
                        p.terminate()
                        p.join(timeout=2)

            # Send poison pills to stop LLM workers
            for _ in self.llm_processes:
                try:
                    self.file_queue.put(None, timeout=1)
                except:
                    pass  # Queue might be full or closed

            # Wait for LLM workers to finish gracefully
            for p in self.llm_processes:
                if p.is_alive():
                    p.join(timeout=5)
                    if p.is_alive():
                        print(f"Terminating LLM worker {p.pid}")
                        p.terminate()
                        p.join(timeout=2)

        except Exception as e:
            print(f"Error during worker shutdown: {e}")
            # Force terminate all workers
            for p in self.repomix_processes + self.llm_processes:
                if p.is_alive():
                    try:
                        p.terminate()
                        p.join(timeout=1)
                    except:
                        pass

        print("All workers stopped.")

    def _initialize_analysis_file(self, repositories):
        """Initialize the single analysis file as JSON"""
        import json

        # Create initial JSON structure
        initial_data = {
            "metadata": {
                "generated": time.strftime('%Y-%m-%d %H:%M:%S'),
                "output_directory": self.output_dir.name,
                "total_repositories": len(repositories),
                "processing_configuration": {
                    "repomix_workers": self.config.REPOMIX_WORKERS,
                    "llm_workers": self.config.LLM_WORKERS,
                    "rate_limit_mb_per_min": self.config.LLM_RATE_LIMIT_MB_PER_MIN
                }
            },
            "analyses": []
        }

        # Write initial JSON structure
        with open(self.analysis_file, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2, ensure_ascii=False)

        print(f"📄 Analysis file initialized: {self.analysis_file}")
        print(f"📁 Repomixes directory: {self.output_dir / 'repomixes'}")

        return self.analysis_file

    def add_duplicate_info_to_metadata(self, duplicate_info):
        """Add duplicate detection information to the JSON metadata."""
        import json

        try:
            # Read existing JSON
            with open(self.analysis_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Add duplicate information to metadata
            data["metadata"]["duplicate_detection"] = duplicate_info

            # Write back to file
            with open(self.analysis_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print(f"📊 Added duplicate detection info to metadata: {duplicate_info['duplicates_removed']} duplicates removed")

        except Exception as e:
            print(f"⚠️ Failed to add duplicate info to metadata: {e}")

    def run(self, repositories):
        """
        Run the complete pipeline.

        Args:
            repositories: List of repository information dictionaries
        """
        import logging
        import time

        # Set up logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        logger = logging.getLogger(__name__)

        logger.info("🎯 REPOSITORY PIPELINE START")
        logger.info(f"📊 Processing {len(repositories)} repositories")

        # Log repository details
        logger.info("📋 Repository list:")
        for i, repo in enumerate(repositories, 1):
            logger.info(f"   {i:2d}. {repo['name']} ({repo.get('stars', 0)} stars)")
            logger.info(f"       URL: {repo['url']}")
            if 'search_keyword' in repo:
                logger.info(f"       Found via: '{repo['search_keyword']}'")

        # Log configuration
        logger.info("⚙️ Pipeline configuration:")
        logger.info(f"   Repomix workers: {self.repomix_workers}")
        logger.info(f"   LLM workers: {self.llm_workers}")
        logger.info(f"   Rate limit: {self.rate_limit_mb_per_min} MB/min")

        start_time = time.time()

        try:
            # Initialize single analysis file
            logger.info("📁 Initializing analysis file...")
            self._initialize_analysis_file(repositories)
            logger.info(f"✅ Analysis file initialized: {self.analysis_file}")

            # Add repositories to queue
            logger.info("📦 Adding repositories to processing queue...")
            self.add_repositories(repositories)
            logger.info(f"✅ {len(repositories)} repositories added to queue")

            # Start workers
            logger.info("🚀 Starting worker processes...")
            logger.info(f"   Starting {self.repomix_workers} repomix workers")
            logger.info(f"   Starting {self.llm_workers} LLM workers")
            self.start_workers()
            logger.info("✅ All workers started successfully")

            # Wait for all repositories to be processed
            logger.info("⏳ Waiting for repository processing to complete...")
            print("Waiting for repository processing to complete...")
            self.repo_queue.join()
            logger.info("✅ Repository processing completed")

            # Wait for all files to be analyzed
            logger.info("⏳ Waiting for file analysis to complete...")
            print("Waiting for file analysis to complete...")
            self.file_queue.join()
            logger.info("✅ File analysis completed")

            total_time = time.time() - start_time
            logger.info(f"🎉 PIPELINE COMPLETED SUCCESSFULLY!")
            logger.info(f"⏱️ Total processing time: {total_time:.1f} seconds")
            logger.info(f"📄 Analysis file: {self.analysis_file}")

        except Exception as e:
            logger.error(f"❌ PIPELINE FAILED: {e}")
            logger.error(f"📍 Error occurred in pipeline execution")
            raise

        finally:
            # Stop workers
            logger.info("🛑 Stopping worker processes...")
            self.stop_workers()
            logger.info("✅ All workers stopped")

            # Clean up rate limiter
            if hasattr(self.rate_limiter, 'cleanup'):
                logger.info("🧹 Cleaning up rate limiter...")
                self.rate_limiter.cleanup()
                logger.info("✅ Rate limiter cleaned up")

        return self.analysis_file
</file>

<file path="service.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Web Service
A Flask-based web service for running repository analysis jobs.
"""

import os
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_file
from werkzeug.exceptions import BadRequest
import subprocess
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import cloud components if available
try:
    from cloud_pipeline import CloudRepositoryPipeline
    from supabase_client import create_supabase_client
    from storage_manager import create_storage_manager
    from config import ScraperConfig
    CLOUD_AVAILABLE = ScraperConfig.USE_CLOUD_SERVICES
    print(f"Cloud services available: {CLOUD_AVAILABLE}")
except ImportError as e:
    print(f"Cloud components not available: {e}")
    CLOUD_AVAILABLE = False

app = Flask(__name__)

# Global job storage (fallback for local mode)
jobs = {}
job_lock = threading.Lock()

# Initialize cloud services if available
supabase_client = None
storage_manager = None

if CLOUD_AVAILABLE:
    try:
        supabase_client = create_supabase_client()
        storage_manager = create_storage_manager()
        print("Cloud services initialized successfully")
    except Exception as e:
        print(f"Failed to initialize cloud services: {e}")
        CLOUD_AVAILABLE = False

class JobStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

def run_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt=None, debug=False):
    """Run the repository analysis in a separate thread."""
    import logging

    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    logger.info(f"🚀 SERVICE FLOW: STARTING ANALYSIS JOB: {job_id}")
    logger.info(f"📋 SERVICE FLOW: Job parameters:")
    logger.info(f"   Keywords: {keywords}")
    logger.info(f"   Min stars: {min_stars}")
    logger.info(f"   Max repos: {max_repos} PER KEYWORD")
    logger.info(f"   Custom prompt: {custom_prompt is not None}")
    logger.info(f"   Debug mode: {debug}")

    # Calculate expected maximum repositories
    keyword_count = len([k.strip() for k in keywords.split(',')])
    expected_max = keyword_count * max_repos
    logger.info(f"🎯 SERVICE FLOW: Expected maximum repositories: {keyword_count} keywords × {max_repos} = {expected_max}")

    with job_lock:
        jobs[job_id]["status"] = JobStatus.RUNNING
        jobs[job_id]["started_at"] = datetime.now().isoformat()
        logger.info(f"✅ Job status updated to 'running'")

    try:
        # Import and run pipeline directly
        logger.info("📦 Importing pipeline components...")
        from pipeline import RepositoryPipeline
        from github_search import search_repositories

        # Search for repositories
        logger.info("🔍 SERVICE FLOW: Starting repository search...")
        logger.info(f"🌐 SERVICE FLOW: Searching GitHub with per-keyword logic...")
        repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos
        )

        logger.info(f"📊 SERVICE FLOW: Search completed: {len(repositories)} repositories found")
        logger.info(f"✅ SERVICE FLOW: Per-keyword logic working: {len(repositories)} ≤ {expected_max} (expected max)")

        if not repositories:
            logger.error("❌ No repositories found matching the criteria")
            raise Exception("No repositories found matching the criteria")

        # Update job with repository count
        with job_lock:
            jobs[job_id]["total_repositories"] = len(repositories)
            logger.info(f"✅ Job updated with repository count: {len(repositories)}")

        # Initialize and run local pipeline
        logger.info("🏭 SERVICE FLOW: Initializing repository pipeline...")
        logger.info(f"📦 SERVICE FLOW: Pipeline will process {len(repositories)} repositories")
        logger.info(f"🔧 SERVICE FLOW: Chunking enabled: 3MB limit with max 3 chunks per repo")
        pipeline = RepositoryPipeline()

        logger.info("🚀 SERVICE FLOW: Starting pipeline execution...")
        logger.info(f"⚡ SERVICE FLOW: Processing repositories with repomix + LLM analysis...")
        result_file = pipeline.run(repositories)

        logger.info(f"✅ SERVICE FLOW: Pipeline execution completed")
        logger.info(f"📄 SERVICE FLOW: Result file: {result_file}")
        
        with job_lock:
            jobs[job_id]["completed_at"] = datetime.now().isoformat()

            if result_file:
                jobs[job_id]["status"] = JobStatus.COMPLETED
                jobs[job_id]["analysis_file"] = str(result_file)

                # Get file size and directory info
                result_path = Path(result_file)
                if result_path.exists():
                    jobs[job_id]["analysis_size"] = result_path.stat().st_size
                    jobs[job_id]["output_directory"] = str(result_path.parent)

                print(f"✅ Local analysis job {job_id} completed: {result_file}")
            else:
                jobs[job_id]["status"] = JobStatus.FAILED
                jobs[job_id]["error"] = "Pipeline returned no result file"
                
    except Exception as e:
        with job_lock:
            jobs[job_id]["status"] = JobStatus.FAILED
            jobs[job_id]["error"] = str(e)
            jobs[job_id]["completed_at"] = datetime.now().isoformat()

def start_cloud_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt=None, debug=False):
    """Start cloud analysis by adding repositories to the processing queue."""
    try:
        # Import required modules
        from github_search import search_repositories
        from redis_queue import QueueManager, RepositoryQueueItem
        import uuid

        # Search for repositories
        repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos
        )

        if not repositories:
            raise Exception("No repositories found matching the criteria")

        # Initialize queue manager
        queue_manager = QueueManager(config.REDIS_URL)
        repo_queue = queue_manager.get_repository_queue()

        # Add repositories to the processing queue
        for repo in repositories:
            repo_item = RepositoryQueueItem(
                repository_url=repo['url'],
                repository_name=repo['name'],
                job_id=job_id
            )
            repo_queue.put(repo_item)

        # Update job status to running (workers will process the repositories)
        if supabase_client:
            supabase_client.update_job_status(job_id, JobStatus.RUNNING)

            # Create repository records in database
            from supabase_client import RepositoryRecord
            for repo in repositories:
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=job_id,
                    name=repo['name'],
                    url=repo['url'],
                    stars=repo.get('stars', 0),
                    status='pending'
                )
                supabase_client.create_repository(repo_record)
        else:
            with job_lock:
                jobs[job_id]["status"] = JobStatus.RUNNING
                jobs[job_id]["started_at"] = datetime.now().isoformat()
                jobs[job_id]["total_repositories"] = len(repositories)

        print(f"✅ Added {len(repositories)} repositories to processing queue for job {job_id}")

    except Exception as e:
        print(f"Failed to start cloud analysis job {job_id}: {e}")

        # Update job status to failed
        if supabase_client:
            supabase_client.update_job_status(job_id, JobStatus.FAILED, error_message=str(e))
        else:
            with job_lock:
                jobs[job_id]["status"] = JobStatus.FAILED
                jobs[job_id]["error"] = str(e)
                jobs[job_id]["completed_at"] = datetime.now().isoformat()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "Repository Research Tool",
        "version": "1.0",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/start', methods=['POST'])
def start_analysis():
    """Start a new repository analysis job."""
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("JSON data required")
        
        # Validate required parameters
        keywords = data.get('keywords')
        if not keywords:
            raise BadRequest("keywords parameter is required")
        
        min_stars = data.get('min_stars', 30)
        max_repos = data.get('max_repos', 20)
        custom_prompt = data.get('custom_prompt')
        debug = data.get('debug', False)
        
        # Create job
        job_id = str(uuid.uuid4())

        if CLOUD_AVAILABLE and supabase_client:
            # Use cloud services
            try:
                from supabase_client import JobRecord
                job_record = JobRecord(
                    id=job_id,
                    keywords=keywords,
                    min_stars=min_stars,
                    max_repos=max_repos,
                    status=JobStatus.PENDING,
                    created_at=datetime.now().isoformat(),
                    custom_prompt=custom_prompt,
                    debug=debug
                )
                supabase_client.create_job(job_record)
                print(f"Created cloud job {job_id}")
            except Exception as e:
                print(f"Failed to create cloud job, falling back to local: {e}")
                # Fallback to local storage
                with job_lock:
                    jobs[job_id] = {
                        "id": job_id,
                        "status": JobStatus.PENDING,
                        "created_at": datetime.now().isoformat(),
                        "parameters": {
                            "keywords": keywords,
                            "min_stars": min_stars,
                            "max_repos": max_repos,
                            "custom_prompt": custom_prompt,
                            "debug": debug
                        }
                    }
        else:
            # Use local storage
            with job_lock:
                jobs[job_id] = {
                    "id": job_id,
                    "status": JobStatus.PENDING,
                    "created_at": datetime.now().isoformat(),
                    "parameters": {
                        "keywords": keywords,
                        "min_stars": min_stars,
                        "max_repos": max_repos,
                        "custom_prompt": custom_prompt,
                        "debug": debug
                    }
                }

        # Start analysis based on deployment mode
        if CLOUD_AVAILABLE:
            # In cloud mode, add job to queue for worker processing
            start_cloud_analysis_job(job_id, keywords, min_stars, max_repos, custom_prompt, debug)
        else:
            # In local mode, run analysis in background thread
            thread = threading.Thread(
                target=run_analysis_job,
                args=(job_id, keywords, min_stars, max_repos, custom_prompt, debug)
            )
            thread.daemon = True
            thread.start()
        
        return jsonify({
            "job_id": job_id,
            "status": JobStatus.PENDING,
            "message": "Analysis job started"
        }), 202
        
    except BadRequest as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a specific job."""
    if CLOUD_AVAILABLE and supabase_client:
        try:
            # Get job from cloud database
            job_record = supabase_client.get_job(job_id)
            if job_record:
                job_data = {
                    "id": job_record.id,
                    "status": job_record.status,
                    "created_at": job_record.created_at,
                    "started_at": job_record.started_at,
                    "completed_at": job_record.completed_at,
                    "parameters": {
                        "keywords": job_record.keywords,
                        "min_stars": job_record.min_stars,
                        "max_repos": job_record.max_repos,
                        "custom_prompt": job_record.custom_prompt,
                        "debug": job_record.debug
                    },
                    "total_repositories": job_record.total_repositories,
                    "analysis_file_url": job_record.analysis_file_url,
                    "error_message": job_record.error_message
                }
                return jsonify(job_data)
        except Exception as e:
            print(f"Error getting job from cloud: {e}")
            # Fall back to local storage

    # Use local storage
    with job_lock:
        job = jobs.get(job_id)

    if not job:
        return jsonify({"error": "Job not found"}), 404

    return jsonify(job)

@app.route('/results/<job_id>', methods=['GET'])
def get_job_results(job_id):
    """Get the results of a completed job."""
    with job_lock:
        job = jobs.get(job_id)
    
    if not job:
        return jsonify({"error": "Job not found"}), 404
    
    if job["status"] != JobStatus.COMPLETED:
        return jsonify({"error": "Job not completed"}), 400
    
    analysis_file = job.get("analysis_file")
    if not analysis_file or not Path(analysis_file).exists():
        return jsonify({"error": "Analysis file not found"}), 404
    
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis_data = json.load(f)
        return jsonify(analysis_data)
    except Exception as e:
        return jsonify({"error": f"Error reading analysis file: {str(e)}"}), 500

@app.route('/download/<job_id>', methods=['GET'])
def download_analysis(job_id):
    """Download the analysis file for a completed job."""
    with job_lock:
        job = jobs.get(job_id)
    
    if not job:
        return jsonify({"error": "Job not found"}), 404
    
    if job["status"] != JobStatus.COMPLETED:
        return jsonify({"error": "Job not completed"}), 400
    
    analysis_file = job.get("analysis_file")
    if not analysis_file or not Path(analysis_file).exists():
        return jsonify({"error": "Analysis file not found"}), 404
    
    return send_file(
        analysis_file,
        as_attachment=True,
        download_name=f"repository_analysis_{job_id}.json"
    )

@app.route('/jobs', methods=['GET'])
def list_jobs():
    """List all jobs."""
    with job_lock:
        job_list = list(jobs.values())
    
    return jsonify({
        "jobs": job_list,
        "total_jobs": len(job_list)
    })

@app.route('/config', methods=['GET'])
def get_config():
    """Get current service configuration."""
    try:
        from config import ScraperConfig
        config = ScraperConfig()
        
        return jsonify({
            "repomix_workers": config.REPOMIX_WORKERS,
            "llm_workers": config.LLM_WORKERS,
            "rate_limit_mb_per_min": config.LLM_RATE_LIMIT_MB_PER_MIN,
            "max_file_size_mb": config.MAX_FILE_SIZE_MB,
            "llm_model": config.LLM_MODEL,
            "api_configured": bool(config.LLM_API_KEY)
        })
    except Exception as e:
        return jsonify({"error": f"Error getting config: {str(e)}"}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    
    print(f"🚀 Starting Repository Research Tool Service")
    print(f"   Port: {port}")
    print(f"   Debug: {debug}")
    print(f"   Health check: http://localhost:{port}/health")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
</file>

</files>
