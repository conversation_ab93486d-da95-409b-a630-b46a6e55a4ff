#!/usr/bin/env python3
"""
Test ONLY the Supabase component to ensure it:
1. Connects to database
2. Creates job records
3. Creates repository records
4. Uploads files to storage
5. Downloads files from storage
6. Creates analysis records
"""

import sys
import os
import tempfile
import uuid
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_supabase_connection():
    """Test Supabase database connection."""
    print("🧪 TESTING SUPABASE CONNECTION")
    print("=" * 50)
    
    try:
        from supabase_client import create_supabase_client
        
        supabase_client = create_supabase_client()
        print("✅ Supabase client created successfully")
        
        # Test connection with a simple query
        result = supabase_client.client.table('jobs').select('id').limit(1).execute()
        print("✅ Database connection successful")
        
        return supabase_client
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return None

def test_job_operations(supabase_client):
    """Test job creation and management."""
    print("\n🧪 TESTING JOB OPERATIONS")
    print("=" * 50)
    
    try:
        from supabase_client import JobRecord
        
        # Create a test job
        test_job_id = str(uuid.uuid4())
        print(f"📝 Creating test job: {test_job_id}")

        job_record = JobRecord(
            id=test_job_id,
            keywords="test keywords",
            min_stars=100,
            max_repos=5,
            status="pending",
            created_at=datetime.now().isoformat()
        )

        created_job = supabase_client.create_job(job_record)
        
        print(f"✅ Job created: {created_job.id}")
        
        # Get the job back
        retrieved_job = supabase_client.get_job(test_job_id)
        if retrieved_job and retrieved_job.id == test_job_id:
            print("✅ Job retrieval successful")
        else:
            print("❌ Job retrieval failed")
            return False
        
        # Update job status
        success = supabase_client.update_job(test_job_id, {"status": "completed"})
        if success:
            print("✅ Job update successful")
        else:
            print("❌ Job update failed")
            return False
        
        return test_job_id
        
    except Exception as e:
        print(f"❌ Job operations failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_repository_operations(supabase_client, job_id):
    """Test repository record creation."""
    print("\n🧪 TESTING REPOSITORY OPERATIONS")
    print("=" * 50)
    
    try:
        from supabase_client import RepositoryRecord

        # Create a test repository
        repo_record = RepositoryRecord(
            id=str(uuid.uuid4()),
            name="test/repository",
            url="https://github.com/test/repository",
            job_id=job_id,
            stars=1000,
            file_size_mb=0.0,
            analysis_status="pending",
            created_at=datetime.now().isoformat()
        )

        created_repo = supabase_client.create_repository(repo_record)
        
        print(f"✅ Repository created: {created_repo.name}")

        return created_repo.id
        
    except Exception as e:
        print(f"❌ Repository operations failed: {e}")
        return False

def test_file_storage(supabase_client):
    """Test file upload and download."""
    print("\n🧪 TESTING FILE STORAGE")
    print("=" * 50)
    
    try:
        # Create a test file
        test_content = "# Test Repository Analysis\n\nThis is a test file for storage testing.\n\n" + ("Test content. " * 100)
        test_file_path = f"test_files/test_{uuid.uuid4().hex[:8]}.md"
        
        print(f"📁 Uploading test file: {test_file_path}")
        
        # Upload file
        file_url = supabase_client.upload_file(
            bucket="repomixes",
            file_path=test_file_path,
            file_content=test_content.encode('utf-8'),
            content_type="text/markdown"
        )
        
        print(f"✅ File uploaded: {file_url}")
        
        # Download file content
        print("📥 Downloading file content...")
        downloaded_content = supabase_client.download_file_content(file_url)
        
        if test_content in downloaded_content:
            print("✅ File download successful - content matches")
        else:
            print("❌ File download failed - content mismatch")
            return False
        
        return file_url
        
    except Exception as e:
        print(f"❌ File storage operations failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_operations(supabase_client, job_id, repo_id):
    """Test analysis record creation."""
    print("\n🧪 TESTING ANALYSIS OPERATIONS")
    print("=" * 50)
    
    try:
        from supabase_client import AnalysisRecord
        
        # Create a test analysis
        analysis_content = "# Test Analysis\n\nThis repository contains test functionality for validation."

        analysis_record = AnalysisRecord(
            id=str(uuid.uuid4()),
            repository_id=repo_id,
            job_id=job_id,
            worker_id="test_worker",
            processed_at=datetime.now().isoformat(),
            analysis_content=analysis_content,
            analysis_length=len(analysis_content)
        )

        created_analysis = supabase_client.create_analysis(analysis_record)
        
        print(f"✅ Analysis created: {created_analysis.id}")
        
        # Get analyses by job ID
        analyses = supabase_client.get_analyses_by_job_id(job_id)
        if len(analyses) > 0 and analyses[0].job_id == job_id:
            print("✅ Analysis retrieval successful")
        else:
            print("❌ Analysis retrieval failed")
            return False
        
        return created_analysis.id
        
    except Exception as e:
        print(f"❌ Analysis operations failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test Supabase component."""
    print("🔧 TESTING SUPABASE COMPONENT")
    print("=" * 60)
    print("TESTING:")
    print("1. Database connection")
    print("2. Job operations (create, get, update)")
    print("3. Repository operations")
    print("4. File storage (upload, download)")
    print("5. Analysis operations")
    print()
    
    # Test connection
    supabase_client = test_supabase_connection()
    if not supabase_client:
        print("\n❌ SUPABASE TESTS FAILED!")
        print("❌ Cannot connect to Supabase")
        return False
    
    # Test job operations
    job_id = test_job_operations(supabase_client)
    if not job_id:
        print("\n❌ SUPABASE TESTS FAILED!")
        print("❌ Job operations failed")
        return False
    
    # Test repository operations
    repo_id = test_repository_operations(supabase_client, job_id)
    if not repo_id:
        print("\n❌ SUPABASE TESTS FAILED!")
        print("❌ Repository operations failed")
        return False
    
    # Test file storage
    file_url = test_file_storage(supabase_client)
    if not file_url:
        print("\n❌ SUPABASE TESTS FAILED!")
        print("❌ File storage failed")
        return False
    
    # Test analysis operations
    analysis_id = test_analysis_operations(supabase_client, job_id, repo_id)
    if not analysis_id:
        print("\n❌ SUPABASE TESTS FAILED!")
        print("❌ Analysis operations failed")
        return False
    
    print("\n🎉 ALL SUPABASE TESTS PASSED!")
    print("✅ Component is working correctly")
    print("✅ Database connection working")
    print("✅ Job operations working")
    print("✅ Repository operations working")
    print("✅ File storage working")
    print("✅ Analysis operations working")
    print()
    print(f"📊 Test Results:")
    print(f"   Job ID: {job_id}")
    print(f"   Repository ID: {repo_id}")
    print(f"   File URL: {file_url}")
    print(f"   Analysis ID: {analysis_id}")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
