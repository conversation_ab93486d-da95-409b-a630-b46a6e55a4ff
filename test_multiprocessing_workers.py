#!/usr/bin/env python3
"""
Test the Repository Research Tool with simplified multiprocessing workers.
This implements the DeepView solution using direct multiprocessing queues.
"""

import sys
import os
import uuid
import json
import logging
import multiprocessing
import time
import subprocess
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from typing import Optional

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config import ScraperConfig
from github_search import search_repositories
from queue_manager import RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord, AnalysisRecord
import requests

def simple_repomix_worker(worker_id: str, config_dict: dict, job_id: str, repo_queue_mp, file_queue_mp):
    """Simplified repomix worker using multiprocessing queues."""
    import logging

    # Initialize logger
    logger = logging.getLogger(f'simple_repomix_{worker_id}')
    logger.info(f"Simple Repomix Worker {worker_id} started for job {job_id}")

    # Initialize Supabase client
    supabase_client = create_supabase_client()

    # Resolve npx path once at the start of the worker
    npx_path = shutil.which('npx')
    if not npx_path:
        logger.error(f"Simple Repomix Worker {worker_id}: 'npx' command not found in system PATH. Please ensure Node.js and npm/npx are installed and accessible.")
        return  # Exit worker if npx is not available

    logger.info(f"Simple Repomix Worker {worker_id}: Using npx at {npx_path}")
    processed_count = 0
    
    while True:
        try:
            # Get repository from multiprocessing queue (blocking, no timeout)
            repo_item: Optional[RepositoryQueueItem] = repo_queue_mp.get()
            if repo_item is None:
                logger.info(f"Simple Repomix Worker {worker_id}: Received termination sentinel. Passing sentinel to file queue and exiting.")
                file_queue_mp.put(None)  # Propagate sentinel for LLM worker
                break
            
            repo_name = repo_item.repository_name
            repo_url = repo_item.repository_url
            
            logger.info(f"Simple Repomix Worker {worker_id}: Processing {repo_name}")
            
            # Run repomix command
            with tempfile.TemporaryDirectory() as temp_dir:
                output_file = Path(temp_dir) / f"{repo_name.replace('/', '_')}.md"
                
                # Use the resolved npx_path
                cmd = [
                    npx_path, 'repomix', '--remote', repo_url,
                    '--include', config_dict['FILE_INCLUDES'],
                    '--output', str(output_file)
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300, encoding='utf-8', errors='replace')
                
                if result.returncode == 0 and output_file.exists():
                    # Upload to Supabase storage
                    with open(output_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    file_path = f"repomixes/{job_id}/{repo_name.replace('/', '_')}.md"
                    file_url = supabase_client.upload_file("repomixes", file_path, content.encode('utf-8'), "text/markdown")
                    
                    # Update repository status - need to get repo_id first
                    # For now, let's skip the status update and just queue for LLM
                    # TODO: Implement proper repository status update with repo_id
                    
                    # Queue for LLM analysis
                    file_item = FileQueueItem(
                        file_url=file_url,
                        repository_name=repo_name,
                        job_id=job_id
                    )
                    file_queue_mp.put(file_item)
                    
                    processed_count += 1
                    logger.info(f"Simple Repomix Worker {worker_id}: Completed {repo_name}")
                else:
                    logger.error(f"Simple Repomix Worker {worker_id}: Failed to process {repo_name}: {result.stderr}")
            
        except Exception as e:
            logger.error(f"Simple Repomix Worker {worker_id}: Unhandled error processing {repo_name if 'repo_name' in locals() else 'unknown'}: {e}", exc_info=True)
            continue
    
    logger.info(f"Simple Repomix Worker {worker_id}: Finished processing {processed_count} repositories")

def simple_llm_worker(worker_id: str, config_dict: dict, job_id: str, file_queue_mp):
    """Simplified LLM worker using multiprocessing queues."""
    import logging
    
    # Initialize logger
    logger = logging.getLogger(f'simple_llm_{worker_id}')
    logger.info(f"Simple LLM Worker {worker_id} started for job {job_id}")
    
    # Initialize services
    supabase_client = create_supabase_client()
    
    processed_count = 0
    
    while True:
        try:
            # Get file from multiprocessing queue (blocking, no timeout)
            file_item: Optional[FileQueueItem] = file_queue_mp.get()
            if file_item is None:
                logger.info(f"Simple LLM Worker {worker_id}: Received termination sentinel, exiting.")
                break
            
            repo_name = file_item.repository_name  # Access from dataclass property
            file_url = file_item.file_url          # Access from dataclass property
            
            logger.info(f"Simple LLM Worker {worker_id}: Analyzing {repo_name}")
            
            # Download file content
            file_content = supabase_client.download_file_content(file_url)
            
            # Generate analysis using direct HTTP request
            prompt = f"""Analyze this repository and provide a comprehensive summary of its features and functionality:

Repository: {repo_name}

{file_content}

Please provide:
1. Main purpose and functionality
2. Key features and capabilities
3. Technology stack used
4. Architecture overview
5. Notable implementation details"""

            # Make LLM API request
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "maxOutputTokens": config_dict['LLM_MAX_OUTPUT_TOKENS'],
                    "temperature": 0.1
                }
            }

            # Correct Gemini API endpoint format
            api_url = f"{config_dict['LLM_BASE_URL']}/models/{config_dict['LLM_MODEL']}:generateContent"

            response = requests.post(
                api_url,
                headers={
                    "Content-Type": "application/json",
                    "x-goog-api-key": config_dict['LLM_API_KEY']
                },
                json=data,
                timeout=config_dict['LLM_TIMEOUT']
            )

            logger.info(f"Simple LLM Worker {worker_id}: API request to {api_url} - Status: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"Simple LLM Worker {worker_id}: Failed analysis for {repo_name} - API Status: {response.status_code}")
                logger.error(f"Simple LLM Worker {worker_id}: API Response: {response.text}")
                continue

            result = response.json()
            analysis = result['candidates'][0]['content']['parts'][0]['text']
            
            # Save analysis
            analysis_record = AnalysisRecord(
                id=str(uuid.uuid4()),
                job_id=job_id,
                repository_name=repo_name,
                analysis_content=analysis,
                chunk_id=None,
                created_at=datetime.now().isoformat()
            )
            supabase_client.create_analysis(analysis_record)
            
            # Update repository status - skip for now
            # TODO: Implement proper repository status update with repo_id
            
            processed_count += 1
            logger.info(f"Simple LLM Worker {worker_id}: Completed analysis for {repo_name}")
            
        except Exception as e:
            logger.error(f"Simple LLM Worker {worker_id}: Unhandled error processing {repo_name if 'repo_name' in locals() else 'unknown'}: {e}", exc_info=True)
            continue
    
    logger.info(f"Simple LLM Worker {worker_id}: Finished processing {processed_count} files")

def main():
    """Test the complete workflow with multiprocessing workers."""
    print("🚀 MULTIPROCESSING WORKERS TEST")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = ScraperConfig()
        logger.info("✅ Configuration loaded")
        
        # Search for repositories
        logger.info("🔍 Searching GitHub repositories...")
        repositories = search_repositories(
            keywords="python test",
            min_stars=1000,
            max_repos=2  # Small test
        )
        
        if not repositories:
            logger.error("❌ No repositories found")
            return False
        
        logger.info(f"✅ Found {len(repositories)} repositories")
        
        # Create multiprocessing queues
        manager = multiprocessing.Manager()
        repo_queue_mp = manager.Queue()
        file_queue_mp = manager.Queue()
        logger.info("✅ Multiprocessing queues initialized")
        
        # Create job
        job_id = str(uuid.uuid4())
        logger.info(f"📝 Creating job: {job_id}")
        
        supabase_client = create_supabase_client()
        job_record = JobRecord(
            id=job_id,
            keywords="python test",
            min_stars=1000,
            max_repos=2,
            status="running",
            created_at=datetime.now().isoformat(),
            custom_prompt=None,
            debug=True
        )
        supabase_client.create_job(job_record)
        
        # Queue repositories
        for repo in repositories:
            # Create repository record
            repo_record = RepositoryRecord(
                id=str(uuid.uuid4()),
                job_id=job_id,
                name=repo['full_name'],
                url=repo['html_url'],
                stars=repo['stars'],
                file_size_mb=0.0,
                analysis_status="pending"
            )
            supabase_client.create_repository(repo_record)
            
            # Queue for processing
            queue_item = RepositoryQueueItem(
                repository_name=repo['full_name'],
                repository_url=repo['html_url'],
                job_id=job_id
            )
            repo_queue_mp.put(queue_item)
        
        logger.info(f"✅ Queued {len(repositories)} repositories")

        # Add termination sentinel for the repomix worker
        repo_queue_mp.put(None)
        logger.info("✅ Sent termination sentinel to repomix queue")
        
        # Convert config to dict
        config_dict = {
            'LLM_API_KEY': config.LLM_API_KEY,
            'LLM_BASE_URL': config.LLM_BASE_URL,
            'LLM_MODEL': config.LLM_MODEL,
            'LLM_MAX_OUTPUT_TOKENS': config.LLM_MAX_OUTPUT_TOKENS,
            'LLM_TIMEOUT': config.LLM_TIMEOUT,
            'FILE_INCLUDES': config.FILE_INCLUDES,
            'MAX_FILE_SIZE_MB': config.MAX_FILE_SIZE_MB,
            'MAX_CHUNKS_TO_RETAIN': config.MAX_CHUNKS_TO_RETAIN
        }
        
        # Start workers
        workers = []
        
        # Start 1 repomix worker
        p_repomix = multiprocessing.Process(
            target=simple_repomix_worker,
            args=("repomix_1", config_dict, job_id, repo_queue_mp, file_queue_mp),
            name="RepomixWorker-1"
        )
        p_repomix.start()
        workers.append(p_repomix)
        logger.info("📦 Started repomix worker")

        # Start 1 LLM worker
        p_llm = multiprocessing.Process(
            target=simple_llm_worker,
            args=("llm_1", config_dict, job_id, file_queue_mp),
            name="LLMWorker-1"
        )
        p_llm.start()
        workers.append(p_llm)
        logger.info("🤖 Started LLM worker")
        
        # Monitor progress and wait for workers to exit gracefully
        start_time = time.time()
        timeout = 600  # 10 minute timeout

        while any(p.is_alive() for p in workers) and (time.time() - start_time < timeout):
            repo_size = repo_queue_mp.qsize()
            file_size = file_queue_mp.qsize()

            alive_worker_names = [p.name for p in workers if p.is_alive()]
            logger.info(f"📊 Queue status: repo_queue={repo_size}, file_queue={file_size}. Active workers: {', '.join(alive_worker_names) if alive_worker_names else 'None'}")

            time.sleep(10)

        # Ensure all workers are properly terminated
        for p in workers:
            p.join(timeout=30)  # Give workers time to finish after sentinels
            if p.is_alive():
                logger.warning(f"Worker {p.name} (PID: {p.pid}) still alive, terminating")
                p.terminate()
                p.join()  # Ensure termination is complete

        logger.info("✅ All workers completed (or terminated after timeout)")

        # Check results in database
        time.sleep(2)  # Give database time to update

        # Consolidate analysis file
        logger.info(f"✨ Consolidating analysis for job {job_id}...")
        try:
            analyses = supabase_client.get_analyses_by_job_id(job_id)
            if analyses:
                full_analysis_content = "# Comprehensive Repository Analysis\n\n"
                full_analysis_content += f"**Job ID:** {job_id}\n"
                full_analysis_content += f"**Generated:** {datetime.now().isoformat()}\n"
                full_analysis_content += f"**Total Repositories Analyzed:** {len(analyses)}\n\n"
                full_analysis_content += "---\n\n"

                for analysis_record in analyses:
                    full_analysis_content += f"## Repository: {analysis_record.repository_name}\n\n"
                    full_analysis_content += analysis_record.analysis_content + "\n\n"
                    full_analysis_content += "---\n\n"

                analysis_file_path = f"analysis_reports/{job_id}.md"
                file_url = supabase_client.upload_file("analyses", analysis_file_path, full_analysis_content.encode('utf-8'), "text/markdown")

                supabase_client.update_job(job_id, {"analysis_file_url": file_url, "status": "completed"})
                logger.info(f"🎉 FINAL ANALYSIS FILE CREATED: {file_url}")
                return True
            else:
                logger.warning(f"⚠️ No analysis records found for job {job_id} to consolidate")
                supabase_client.update_job(job_id, {"status": "completed"})
                return True
        except Exception as e:
            logger.error(f"❌ Error consolidating analysis file for job {job_id}: {e}", exc_info=True)
            supabase_client.update_job(job_id, {"status": "failed"})
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
