"""
Comprehensive monitoring and logging system for repository research tool.
Integrates with <PERSON><PERSON> and creates detailed logs for analysis.
"""
import os
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration


class RepositoryMonitor:
    """Comprehensive monitoring for repository processing with Sentry integration."""
    
    def __init__(self, output_dir: Path):
        self.output_dir = Path(output_dir)
        self.logs_dir = self.output_dir / "logs"
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize log files
        self.run_log_file = self.logs_dir / "run_analysis.json"
        self.errors_log_file = self.logs_dir / "errors.json"
        self.metrics_log_file = self.logs_dir / "metrics.json"
        self.sentry_log_file = self.logs_dir / "sentry_events.json"
        
        # Initialize data structures
        self.run_data = {
            "start_time": datetime.now().isoformat(),
            "configuration": {},
            "search_results": {},
            "processing_stats": {
                "repositories_found": 0,
                "repositories_attempted": 0,
                "repositories_successful": 0,
                "repositories_failed": 0,
                "repomix_failures": [],
                "llm_failures": [],
                "rate_limit_events": []
            },
            "performance_metrics": {},
            "end_time": None,
            "status": "running"
        }
        
        self.errors = []
        self.metrics = []
        self.sentry_events = []
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup comprehensive logging with Sentry integration."""
        # Configure Python logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.logs_dir / "detailed.log"),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("repository_monitor")
        
        # Add custom Sentry breadcrumb handler
        def custom_breadcrumb_processor(crumb, hint):
            """Process and log all Sentry breadcrumbs."""
            self.sentry_events.append({
                "timestamp": datetime.now().isoformat(),
                "type": "breadcrumb",
                "data": crumb,
                "hint": str(hint) if hint else None
            })
            return crumb
            
        # Configure Sentry with enhanced logging
        sentry_dsn = os.getenv('SENTRY_DSN')
        if sentry_dsn and sentry_dsn != 'your_sentry_dsn_here':
            sentry_sdk.init(
                dsn=os.getenv('SENTRY_DSN'),
                traces_sample_rate=1.0,
                profiles_sample_rate=1.0,
                integrations=[
                    LoggingIntegration(
                        level=logging.INFO,
                        event_level=logging.ERROR,
                        sentry_logs_level=logging.INFO
                    )
                ],
                before_breadcrumb=custom_breadcrumb_processor,
                _experiments={"enable_logs": True},
                environment=os.getenv('ENVIRONMENT', 'development'),
                release=os.getenv('RELEASE', 'repository-research-tool@1.0.0'),
            )
            
    def log_configuration(self, config: Dict[str, Any]):
        """Log the run configuration."""
        self.run_data["configuration"] = config
        self.logger.info(f"Configuration logged: {config}")
        sentry_sdk.add_breadcrumb(
            category="configuration",
            message="Run configuration set",
            level="info",
            data=config
        )
        
    def log_search_results(self, keyword: str, found_count: int, requested_count: int):
        """Log GitHub search results."""
        self.run_data["search_results"][keyword] = {
            "found": found_count,
            "requested": requested_count,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"Search results - {keyword}: {found_count}/{requested_count}")
        sentry_sdk.add_breadcrumb(
            category="search",
            message=f"GitHub search completed for '{keyword}'",
            level="info",
            data={"keyword": keyword, "found": found_count, "requested": requested_count}
        )

    def log_duplicate_detection(self, duplicate_info: Dict[str, Any]):
        """Log duplicate detection results."""
        self.run_data["duplicate_detection"] = duplicate_info

        self.logger.info(f"Duplicate detection: {duplicate_info['duplicates_removed']} duplicates removed "
                        f"({duplicate_info['duplicate_rate']:.1f}% duplicate rate)")

        sentry_sdk.add_breadcrumb(
            category="deduplication",
            message=f"Removed {duplicate_info['duplicates_removed']} duplicates "
                   f"({duplicate_info['duplicate_rate']:.1f}% rate)",
            level="info",
            data=duplicate_info
        )

        # Log multi-keyword repositories if any
        if duplicate_info.get('multi_keyword_repos'):
            multi_keyword_count = len(duplicate_info['multi_keyword_repos'])
            self.logger.info(f"Found {multi_keyword_count} repositories matching multiple keywords")

            sentry_sdk.add_breadcrumb(
                category="deduplication",
                message=f"{multi_keyword_count} repositories found by multiple keywords",
                level="info",
                data={"multi_keyword_repos": duplicate_info['multi_keyword_repos']}
            )

    def log_repository_start(self, repo_name: str, worker_id: int, worker_type: str):
        """Log when repository processing starts."""
        self.run_data["processing_stats"]["repositories_attempted"] += 1
        
        self.logger.info(f"{worker_type} Worker {worker_id}: Starting {repo_name}")
        sentry_sdk.add_breadcrumb(
            category="processing",
            message=f"Repository processing started: {repo_name}",
            level="info",
            data={"repo_name": repo_name, "worker_id": worker_id, "worker_type": worker_type}
        )
        
    def log_repository_success(self, repo_name: str, worker_id: int, worker_type: str, 
                             duration: float, file_size_mb: float = None):
        """Log successful repository processing."""
        self.run_data["processing_stats"]["repositories_successful"] += 1
        
        log_data = {
            "repo_name": repo_name,
            "worker_id": worker_id,
            "worker_type": worker_type,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        if file_size_mb:
            log_data["file_size_mb"] = file_size_mb
            
        self.metrics.append(log_data)
        
        self.logger.info(f"{worker_type} Worker {worker_id}: ✅ Completed {repo_name} - Duration: {duration:.1f}s")
        sentry_sdk.add_breadcrumb(
            category="success",
            message=f"Repository processed successfully: {repo_name}",
            level="info",
            data=log_data
        )
        
    def log_repository_failure(self, repo_name: str, worker_id: int, worker_type: str,
                             error_message: str, duration: float):
        """Log repository processing failure."""
        self.run_data["processing_stats"]["repositories_failed"] += 1
        
        error_data = {
            "repo_name": repo_name,
            "worker_id": worker_id,
            "worker_type": worker_type,
            "error_message": error_message,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        if worker_type == "repomix":
            self.run_data["processing_stats"]["repomix_failures"].append(error_data)
        else:
            self.run_data["processing_stats"]["llm_failures"].append(error_data)
            
        self.errors.append(error_data)
        
        self.logger.error(f"{worker_type} Worker {worker_id}: ❌ Failed {repo_name} - {error_message}")
        sentry_sdk.add_breadcrumb(
            category="error",
            message=f"Repository processing failed: {repo_name}",
            level="error",
            data=error_data
        )
        
        # Send to Sentry as an event for tracking
        sentry_sdk.capture_message(
            f"Repository processing failed: {repo_name}",
            level="error",
            extras=error_data
        )
        
    def log_rate_limit_event(self, worker_id: int, repo_name: str, retry_delay: int, attempt: int):
        """Log rate limiting events."""
        rate_limit_data = {
            "worker_id": worker_id,
            "repo_name": repo_name,
            "retry_delay": retry_delay,
            "attempt": attempt,
            "timestamp": datetime.now().isoformat()
        }
        
        self.run_data["processing_stats"]["rate_limit_events"].append(rate_limit_data)
        
        self.logger.warning(f"Rate limited - Worker {worker_id}, Repo: {repo_name}, Delay: {retry_delay}s, Attempt: {attempt}")
        sentry_sdk.add_breadcrumb(
            category="rate_limit",
            message=f"Rate limit encountered",
            level="warning",
            data=rate_limit_data
        )
        
    def log_performance_metrics(self, phase: str, duration: float, count: int, success_count: int = None):
        """Log performance metrics for different phases."""
        metrics_data = {
            "phase": phase,
            "duration": duration,
            "total_count": count,
            "success_count": success_count or count,
            "success_rate": (success_count or count) / count if count > 0 else 0,
            "timestamp": datetime.now().isoformat()
        }
        
        self.run_data["performance_metrics"][phase] = metrics_data
        
        self.logger.info(f"Performance - {phase}: {duration:.2f}s, Count: {count}, Success: {success_count or count}")
        sentry_sdk.add_breadcrumb(
            category="performance",
            message=f"Phase completed: {phase}",
            level="info",
            data=metrics_data
        )
        
    def finalize_run(self, status: str = "completed"):
        """Finalize the run and save all logs."""
        self.run_data["end_time"] = datetime.now().isoformat()
        self.run_data["status"] = status

        # Calculate final statistics
        total_found = sum(result["found"] for result in self.run_data["search_results"].values())
        self.run_data["processing_stats"]["repositories_found"] = total_found

        # Save all log files
        self._save_logs()

        # Send final summary to Sentry
        summary = {
            "total_repositories_found": total_found,
            "repositories_successful": self.run_data["processing_stats"]["repositories_successful"],
            "repositories_failed": self.run_data["processing_stats"]["repositories_failed"],
            "success_rate": self.run_data["processing_stats"]["repositories_successful"] / max(1, self.run_data["processing_stats"]["repositories_attempted"]),
            "total_duration": self._calculate_total_duration()
        }

        self.logger.info(f"Run completed: {summary}")
        sentry_sdk.capture_message(
            f"Repository research run completed",
            level="info",
            extras=summary
        )

        # Auto-fetch and analyze Sentry data
        self._auto_analyze_sentry_data()
        
    def _calculate_total_duration(self) -> float:
        """Calculate total run duration."""
        if self.run_data["end_time"]:
            start = datetime.fromisoformat(self.run_data["start_time"])
            end = datetime.fromisoformat(self.run_data["end_time"])
            return (end - start).total_seconds()
        return 0
        
    def _save_logs(self):
        """Save all logs to files."""
        # Save run analysis
        with open(self.run_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.run_data, f, indent=2, ensure_ascii=False, default=str)

        # Save errors
        with open(self.errors_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.errors, f, indent=2, ensure_ascii=False, default=str)

        # Save metrics
        with open(self.metrics_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False, default=str)

        # Save Sentry events
        with open(self.sentry_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.sentry_events, f, indent=2, ensure_ascii=False, default=str)
            
        print(f"\n📊 LOGS SAVED TO: {self.logs_dir}")
        print(f"  - Run analysis: {self.run_log_file}")
        print(f"  - Errors: {self.errors_log_file}")
        print(f"  - Metrics: {self.metrics_log_file}")
        print(f"  - Sentry events: {self.sentry_log_file}")

    def _auto_analyze_sentry_data(self):
        """Auto-fetch Sentry data and perform comprehensive analysis."""
        try:
            print(f"\n🔍 AUTO-ANALYZING SENTRY DATA...")

            # Fetch recent Sentry issues
            sentry_issues = self._fetch_recent_sentry_issues()

            # Comprehensive analysis
            analysis_data = {
                "timestamp": datetime.now().isoformat(),
                "run_summary": self.run_data,
                "sentry_analysis": sentry_issues,
                "error_analysis": self._analyze_local_errors(),
                "performance_analysis": self._analyze_performance(),
                "critical_issues": self._identify_critical_issues(sentry_issues),
                "recommendations": self._generate_enhanced_recommendations(sentry_issues)
            }

            # Save comprehensive analysis
            sentry_analysis_file = self.logs_dir / "auto_analysis.json"
            with open(sentry_analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)

            # Generate human-readable analysis report
            analysis_report_file = self.logs_dir / "auto_analysis_report.md"
            with open(analysis_report_file, 'w', encoding='utf-8') as f:
                f.write(self._generate_enhanced_analysis_report(analysis_data))

            # Generate Sentry-specific insights
            sentry_insights_file = self.logs_dir / "sentry_insights.md"
            with open(sentry_insights_file, 'w', encoding='utf-8') as f:
                f.write(self._generate_sentry_insights_report(sentry_issues))

            print(f"✅ Enhanced auto-analysis completed:")
            print(f"  - Analysis data: {sentry_analysis_file}")
            print(f"  - Analysis report: {analysis_report_file}")
            print(f"  - Sentry insights: {sentry_insights_file}")

        except Exception as e:
            print(f"⚠️ Auto-analysis failed: {e}")
            # Save the error for debugging
            error_file = self.logs_dir / "auto_analysis_error.txt"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"Auto-analysis error at {datetime.now().isoformat()}:\n")
                f.write(f"{str(e)}\n")
                f.write(f"\nTraceback:\n")
                import traceback
                f.write(traceback.format_exc())

    def _analyze_local_errors(self) -> Dict[str, Any]:
        """Analyze errors from local logs."""
        error_analysis = {
            "total_errors": len(self.errors),
            "repomix_failures": len(self.run_data["processing_stats"]["repomix_failures"]),
            "llm_failures": len(self.run_data["processing_stats"]["llm_failures"]),
            "rate_limit_events": len(self.run_data["processing_stats"]["rate_limit_events"]),
            "error_patterns": {},
            "failure_reasons": {}
        }

        # Analyze repomix failures
        for failure in self.run_data["processing_stats"]["repomix_failures"]:
            error_msg = failure.get("error_message", "unknown")
            if "clone repository" in error_msg:
                error_analysis["failure_reasons"]["git_clone_failures"] = error_analysis["failure_reasons"].get("git_clone_failures", 0) + 1
            elif "Invalid remote repository" in error_msg:
                error_analysis["failure_reasons"]["invalid_repo_urls"] = error_analysis["failure_reasons"].get("invalid_repo_urls", 0) + 1
            else:
                error_analysis["failure_reasons"]["other_repomix"] = error_analysis["failure_reasons"].get("other_repomix", 0) + 1

        return error_analysis

    def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze performance metrics."""
        stats = self.run_data["processing_stats"]
        total_attempted = stats["repositories_attempted"]
        total_successful = stats["repositories_successful"]
        total_found = stats["repositories_found"]

        # INTEGRITY FIX: Handle multiprocessing synchronization issues
        # If worker process stats weren't synchronized, use performance metrics as fallback
        if total_attempted == 0 and "total_processing" in self.run_data.get("performance_metrics", {}):
            perf_metrics = self.run_data["performance_metrics"]["total_processing"]
            total_attempted = perf_metrics.get("total_count", total_found)
            total_successful = perf_metrics.get("success_count", 0)

            # Log the synchronization issue
            self.logger.warning(f"INTEGRITY WARNING: Worker process stats not synchronized. Using fallback metrics.")
            self.logger.warning(f"  - repositories_attempted: {stats['repositories_attempted']} -> {total_attempted}")
            self.logger.warning(f"  - repositories_successful: {stats['repositories_successful']} -> {total_successful}")

        performance = {
            "success_rate": (total_successful / max(1, total_attempted)) * 100,
            "total_repositories": total_found,
            "processing_efficiency": (total_successful / max(1, total_found)) * 100,
            "rate_limit_frequency": len(stats["rate_limit_events"]) / max(1, total_attempted),
            "average_processing_time": self._calculate_average_processing_time(),
            "integrity_warning": total_attempted == 0 and total_found > 0
        }

        return performance

    def _calculate_average_processing_time(self) -> float:
        """Calculate average processing time from metrics."""
        if not self.metrics:
            return 0.0

        total_time = sum(metric.get("duration", 0) for metric in self.metrics)
        return total_time / len(self.metrics)

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on run data."""
        recommendations = []
        stats = self.run_data["processing_stats"]

        # INTEGRITY FIX: Use performance analysis for accurate success rate
        performance = self._analyze_performance()
        success_rate = performance["success_rate"]

        if performance.get("integrity_warning", False):
            recommendations.append("⚠️ MONITORING INTEGRITY WARNING: Worker process synchronization issues detected. Metrics may be inaccurate.")

        if success_rate < 50:
            recommendations.append("🚨 LOW SUCCESS RATE: Less than 50% of repositories processed successfully. Investigate repomix failures and API issues.")
        elif success_rate < 80:
            recommendations.append("⚠️ MODERATE SUCCESS RATE: Consider optimizing error handling and retry logic.")
        else:
            recommendations.append("✅ GOOD SUCCESS RATE: System performing well with high success rate.")

        # Rate limiting analysis
        rate_limit_count = len(stats["rate_limit_events"])
        if rate_limit_count > 100:
            recommendations.append("🚨 EXCESSIVE RATE LIMITING: Consider reducing concurrent workers or increasing delays.")
        elif rate_limit_count > 20:
            recommendations.append("⚠️ MODERATE RATE LIMITING: Monitor API usage patterns.")

        # Repository failures analysis
        repomix_failures = len(stats["repomix_failures"])
        if repomix_failures > 20:
            recommendations.append("🔧 HIGH REPOMIX FAILURES: Many repositories failed to process. Check repository accessibility and network connectivity.")

        return recommendations

    def _generate_analysis_report(self, analysis_data: Dict[str, Any]) -> str:
        """Generate human-readable analysis report."""
        report = []
        report.append("# Repository Research Tool - Auto Analysis Report")
        report.append(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Run Summary
        run_data = analysis_data["run_summary"]
        stats = run_data["processing_stats"]

        report.append("## Run Summary")
        report.append(f"- **Status**: {run_data['status']}")
        report.append(f"- **Duration**: {self._format_duration(self._calculate_total_duration())}")
        report.append(f"- **Repositories Found**: {stats['repositories_found']}")
        report.append(f"- **Repositories Attempted**: {stats['repositories_attempted']}")
        report.append(f"- **Repositories Successful**: {stats['repositories_successful']}")
        report.append(f"- **Success Rate**: {analysis_data['performance_analysis']['success_rate']:.1f}%")
        report.append("")

        # Error Analysis
        error_analysis = analysis_data["error_analysis"]
        report.append("## Error Analysis")
        report.append(f"- **Total Errors**: {error_analysis['total_errors']}")
        report.append(f"- **Repomix Failures**: {error_analysis['repomix_failures']}")
        report.append(f"- **LLM Failures**: {error_analysis['llm_failures']}")
        report.append(f"- **Rate Limit Events**: {error_analysis['rate_limit_events']}")

        if error_analysis["failure_reasons"]:
            report.append("\n### Failure Breakdown")
            for reason, count in error_analysis["failure_reasons"].items():
                report.append(f"- **{reason.replace('_', ' ').title()}**: {count}")
        report.append("")

        # Performance Analysis
        perf = analysis_data["performance_analysis"]
        report.append("## Performance Analysis")
        report.append(f"- **Processing Efficiency**: {perf['processing_efficiency']:.1f}%")
        report.append(f"- **Average Processing Time**: {perf['average_processing_time']:.1f}s")
        report.append(f"- **Rate Limit Frequency**: {perf['rate_limit_frequency']:.2f} per repository")
        report.append("")

        # Recommendations
        recommendations = analysis_data["recommendations"]
        if recommendations:
            report.append("## Recommendations")
            for rec in recommendations:
                report.append(f"- {rec}")
            report.append("")

        # Configuration
        config = run_data.get("configuration", {})
        if config:
            report.append("## Configuration Used")
            for key, value in config.items():
                report.append(f"- **{key}**: {value}")

        return "\n".join(report)

    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"

    def _fetch_recent_sentry_issues(self) -> Dict[str, Any]:
        """Fetch recent Sentry issues for analysis."""
        try:
            # This would use Sentry MCP tools if available
            # For now, return mock data structure
            return {
                "issues_fetched": 0,
                "connection_errors": 0,
                "timeout_errors": 0,
                "rate_limit_errors": 0,
                "api_errors": 0,
                "critical_issues": [],
                "fetch_error": "Sentry MCP tools not available in current context"
            }
        except Exception as e:
            return {
                "issues_fetched": 0,
                "fetch_error": str(e),
                "critical_issues": []
            }

    def _identify_critical_issues(self, sentry_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify critical issues from Sentry data and local logs."""
        critical_issues = []

        # Analyze local error patterns
        error_patterns = {}
        for error in self.errors:
            error_type = error.get("error_type", "unknown")
            error_msg = error.get("error_message", "")

            # Categorize errors
            if "connection" in error_msg.lower() or "reset" in error_msg.lower():
                pattern = "connection_reset"
            elif "timeout" in error_msg.lower():
                pattern = "timeout"
            elif "rate limit" in error_msg.lower() or "429" in error_msg:
                pattern = "rate_limit"
            elif "api" in error_msg.lower() and ("key" in error_msg.lower() or "auth" in error_msg.lower()):
                pattern = "api_auth"
            else:
                pattern = "other"

            if pattern not in error_patterns:
                error_patterns[pattern] = []
            error_patterns[pattern].append(error)

        # Generate critical issue summaries
        for pattern, errors in error_patterns.items():
            if len(errors) > 3:  # More than 3 occurrences = critical
                critical_issues.append({
                    "pattern": pattern,
                    "count": len(errors),
                    "severity": "critical" if len(errors) > 10 else "high",
                    "description": self._get_pattern_description(pattern),
                    "examples": errors[:3],  # First 3 examples
                    "recommendation": self._get_pattern_recommendation(pattern)
                })

        return critical_issues

    def _get_pattern_description(self, pattern: str) -> str:
        """Get description for error pattern."""
        descriptions = {
            "connection_reset": "Network connections being forcibly closed by remote host",
            "timeout": "Operations timing out, likely due to large file processing or network issues",
            "rate_limit": "API rate limiting preventing requests from completing",
            "api_auth": "API authentication or authorization failures",
            "other": "Miscellaneous errors requiring investigation"
        }
        return descriptions.get(pattern, "Unknown error pattern")

    def _get_pattern_recommendation(self, pattern: str) -> str:
        """Get recommendation for error pattern."""
        recommendations = {
            "connection_reset": "Implement connection pooling, retry logic, and reduce request payload sizes",
            "timeout": "Increase timeout values, implement chunking for large files, add progress monitoring",
            "rate_limit": "Implement exponential backoff, reduce concurrent requests, check API tier limits",
            "api_auth": "Verify API key validity, check authentication headers, ensure proper permissions",
            "other": "Review error logs for specific failure patterns and implement targeted fixes"
        }
        return recommendations.get(pattern, "Investigate error details and implement appropriate fixes")

    def _generate_enhanced_recommendations(self, sentry_data: Dict[str, Any]) -> List[str]:
        """Generate enhanced recommendations based on comprehensive analysis."""
        recommendations = self._generate_recommendations()  # Get base recommendations

        # Add Sentry-specific recommendations
        if sentry_data.get("connection_errors", 0) > 5:
            recommendations.append("🌐 HIGH CONNECTION ERRORS: Implement connection pooling and retry logic for network stability")

        if sentry_data.get("timeout_errors", 0) > 3:
            recommendations.append("⏰ TIMEOUT ISSUES: Increase timeout values and implement chunking for large file processing")

        # Add recommendations based on critical issues
        critical_issues = sentry_data.get("critical_issues", [])
        for issue in critical_issues:
            recommendations.append(f"🚨 {issue['pattern'].upper()}: {issue['recommendation']}")

        return recommendations

    def _generate_enhanced_analysis_report(self, analysis_data: Dict[str, Any]) -> str:
        """Generate enhanced human-readable analysis report."""
        base_report = self._generate_analysis_report(analysis_data)

        # Add Sentry analysis section
        sentry_section = []
        sentry_section.append("\n## Sentry Analysis")

        sentry_data = analysis_data.get("sentry_analysis", {})
        if sentry_data.get("fetch_error"):
            sentry_section.append(f"- **Fetch Status**: ⚠️ {sentry_data['fetch_error']}")
        else:
            sentry_section.append(f"- **Issues Fetched**: {sentry_data.get('issues_fetched', 0)}")
            sentry_section.append(f"- **Connection Errors**: {sentry_data.get('connection_errors', 0)}")
            sentry_section.append(f"- **Timeout Errors**: {sentry_data.get('timeout_errors', 0)}")
            sentry_section.append(f"- **Rate Limit Errors**: {sentry_data.get('rate_limit_errors', 0)}")

        # Add critical issues section
        critical_issues = analysis_data.get("critical_issues", [])
        if critical_issues:
            sentry_section.append("\n### Critical Issues Identified")
            for issue in critical_issues:
                sentry_section.append(f"- **{issue['pattern'].replace('_', ' ').title()}** ({issue['severity']}): {issue['count']} occurrences")
                sentry_section.append(f"  - {issue['description']}")
                sentry_section.append(f"  - Recommendation: {issue['recommendation']}")

        return base_report + "\n".join(sentry_section)

    def _generate_sentry_insights_report(self, sentry_data: Dict[str, Any]) -> str:
        """Generate detailed Sentry insights report."""
        report = []
        report.append("# Sentry Insights Report")
        report.append(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Connection status
        if sentry_data.get("fetch_error"):
            report.append("## ⚠️ Sentry Connection Status")
            report.append(f"**Status**: Failed to fetch data")
            report.append(f"**Error**: {sentry_data['fetch_error']}")
            report.append("")
            report.append("### Manual Sentry Check Recommended")
            report.append("1. Visit https://imimi.de.sentry.io/issues/")
            report.append("2. Filter by project: repomixed-scraper")
            report.append("3. Check for recent issues in the last 2 hours")
            report.append("4. Look for patterns in:")
            report.append("   - Connection errors (ConnectionResetError)")
            report.append("   - Timeout errors (TimeoutExpired)")
            report.append("   - Rate limiting (429 errors)")
            report.append("   - API authentication issues")
        else:
            report.append("## ✅ Sentry Data Summary")
            report.append(f"- Issues fetched: {sentry_data.get('issues_fetched', 0)}")
            report.append(f"- Connection errors: {sentry_data.get('connection_errors', 0)}")
            report.append(f"- Timeout errors: {sentry_data.get('timeout_errors', 0)}")
            report.append(f"- Rate limit errors: {sentry_data.get('rate_limit_errors', 0)}")

        report.append("")
        report.append("## 🔍 Key Issues to Monitor")
        report.append("Based on recent Sentry data, watch for:")
        report.append("")
        report.append("### 1. Connection Reset Errors")
        report.append("- **Pattern**: `ConnectionResetError: [WinError 10054]`")
        report.append("- **Cause**: Remote host forcibly closing connections")
        report.append("- **Impact**: LLM API calls failing mid-request")
        report.append("- **Solution**: Implement connection pooling and retry logic")
        report.append("")
        report.append("### 2. Timeout Issues")
        report.append("- **Pattern**: `TimeoutExpired: Command timed out after 300 seconds`")
        report.append("- **Cause**: Large repositories taking too long to process")
        report.append("- **Impact**: Repomix operations failing on large codebases")
        report.append("- **Solution**: Increase timeouts and implement chunking")
        report.append("")
        report.append("### 3. API Rate Limiting")
        report.append("- **Pattern**: HTTP 429 responses")
        report.append("- **Cause**: Exceeding API rate limits")
        report.append("- **Impact**: LLM analysis requests being rejected")
        report.append("- **Solution**: Implement exponential backoff and reduce concurrency")

        return "\n".join(report)
