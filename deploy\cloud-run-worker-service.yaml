apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: repository-research-worker
  annotations:
    run.googleapis.com/ingress: none
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration for Worker service
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "5"
        
        # Resource allocation - high for processing
        run.googleapis.com/cpu: "2"
        run.googleapis.com/memory: "4Gi"
        
        # Long timeout for processing
        run.googleapis.com/timeout: "3600s"
        
        # VPC configuration for Redis access
        run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/redis-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only
        
        # Execution environment
        run.googleapis.com/execution-environment: gen2
        
    spec:
      containerConcurrency: 1
      timeoutSeconds: 3600
      serviceAccountName: repository-research-sa@PROJECT_ID.iam.gserviceaccount.com
      
      containers:
      - name: repository-research-worker
        image: gcr.io/PROJECT_ID/repository-research-tool:latest
        
        # Run Worker service
        args: ["worker"]
        
        env:
        # Deployment configuration
        - name: DEPLOYMENT_MODE
          value: "cloud"
          
        # Redis configuration
        - name: REDIS_URL
          value: "redis://REDIS_PRIVATE_IP:6379"
          
        # Supabase configuration
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: url
        - name: SUPABASE_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: service-key
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: anon-key
              
        # API keys
        - name: LLM_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: gemini-api-key
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: github-token
              
        # Worker configuration
        - name: REPOMIX_WORKERS
          value: "15"
        - name: LLM_WORKERS
          value: "4"
        - name: LLM_TIMEOUT
          value: "90"
        - name: LLM_MAX_OUTPUT_TOKENS
          value: "8192"
          
        # Storage configuration
        - name: STORAGE_BUCKET_REPOMIXES
          value: "repomixes"
        - name: STORAGE_BUCKET_ANALYSES
          value: "analyses"
        - name: STORAGE_BUCKET_LOGS
          value: "logs"
          
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
            
        # Worker service doesn't need HTTP health checks
        # Use exec-based health check instead
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; sys.exit(0)"
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 10
          failureThreshold: 3
          
        readinessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; sys.exit(0)"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3

  traffic:
  - percent: 100
    latestRevision: true
