#!/usr/bin/env python3
"""
Repository Research Tool - Unified Service API

This service provides a REST API for the unified architecture that:
1. Creates job records in Supabase
2. Adds repository tasks to Redis queues
3. Returns job status from Supabase

Workers run separately and process the queued tasks.
"""

import os
import sys
import uuid
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from werkzeug.exceptions import BadRequest

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import unified components
from config import ScraperConfig
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord
from redis_queue import QueueManager, RepositoryQueueItem
from github_search import search_repositories

# Initialize Flask app
app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize unified services
config = ScraperConfig()
supabase_client = create_supabase_client()
queue_manager = QueueManager(config.REDIS_URL)

logger.info("✅ Unified pipeline service initialized successfully")

class JobStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "Repository Research Tool - Unified Architecture",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "architecture": "unified",
        "components": {
            "redis": config.REDIS_URL,
            "supabase": config.SUPABASE_URL
        }
    })

@app.route('/start', methods=['POST'])
def start_analysis():
    """Start a new repository analysis job using unified architecture."""
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("JSON data required")
        
        # Validate required parameters
        keywords = data.get('keywords')
        if not keywords:
            raise BadRequest("keywords parameter is required")
        
        min_stars = data.get('min_stars', 30)
        max_repos = data.get('max_repos', 20)
        custom_prompt = data.get('custom_prompt')
        debug = data.get('debug', False)
        
        # Create job ID
        job_id = str(uuid.uuid4())
        logger.info(f"🚀 Starting job {job_id} with keywords: {keywords}")
        
        # Search for repositories
        all_repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos_per_keyword=max_repos
        )
        
        if not all_repositories:
            return jsonify({"error": "No repositories found matching criteria"}), 404
        
        logger.info(f"📊 Found {len(all_repositories)} repositories for job {job_id}")
        
        # Create job record in Supabase
        job_record = JobRecord(
            id=job_id,
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos,
            status=JobStatus.RUNNING,
            created_at=datetime.now().isoformat(),
            custom_prompt=custom_prompt,
            debug=debug
        )
        supabase_client.create_job(job_record)
        logger.info(f"✅ Job record created in Supabase: {job_id}")
        
        # Create repository records and add to Redis queue
        repo_queue = queue_manager.get_repository_queue()
        
        for repo in all_repositories:
            # Create repository record in Supabase
            repo_record = RepositoryRecord(
                id=str(uuid.uuid4()),
                job_id=job_id,
                name=repo['name'],
                url=repo['url'],
                stars=repo['stars'],
                file_size_mb=0.0,  # Will be updated after repomix
                analysis_status="pending"
            )
            supabase_client.create_repository(repo_record)
            
            # Add to Redis queue for worker processing
            queue_item = RepositoryQueueItem(
                repository_name=repo['name'],
                repository_url=repo['url'],
                job_id=job_id
            )
            repo_queue.put(queue_item)
            logger.info(f"📤 Queued repository: {repo['name']}")
        
        logger.info(f"✅ Job {job_id} setup complete - {len(all_repositories)} repositories queued")
        
        return jsonify({
            "job_id": job_id,
            "status": JobStatus.RUNNING,
            "message": f"Analysis job started with {len(all_repositories)} repositories",
            "repositories_count": len(all_repositories),
            "parameters": {
                "keywords": keywords,
                "min_stars": min_stars,
                "max_repos": max_repos,
                "custom_prompt": custom_prompt,
                "debug": debug
            }
        }), 202
        
    except BadRequest as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error starting analysis: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a specific job."""
    try:
        # Get job from Supabase
        job_record = supabase_client.get_job(job_id)
        if not job_record:
            return jsonify({"error": "Job not found"}), 404
        
        # Get repositories for this job
        repositories = supabase_client.get_repositories_by_job(job_id)
        
        # Get analyses for this job
        analyses = supabase_client.get_analyses_by_job(job_id)
        
        # Calculate progress
        total_repos = len(repositories)
        completed_analyses = len(analyses)
        
        # Determine overall status
        if completed_analyses == total_repos and total_repos > 0:
            status = JobStatus.COMPLETED
        elif completed_analyses > 0:
            status = JobStatus.RUNNING
        else:
            status = job_record.status
        
        return jsonify({
            "job_id": job_id,
            "status": status,
            "created_at": job_record.created_at,
            "progress": {
                "total_repositories": total_repos,
                "completed_analyses": completed_analyses,
                "percentage": round((completed_analyses / total_repos * 100) if total_repos > 0 else 0, 1)
            },
            "parameters": {
                "keywords": job_record.keywords,
                "min_stars": job_record.min_stars,
                "max_repos": job_record.max_repos,
                "custom_prompt": job_record.custom_prompt
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/results/<job_id>', methods=['GET'])
def get_job_results(job_id):
    """Get the results of a completed job."""
    try:
        # Get job from Supabase
        job_record = supabase_client.get_job(job_id)
        if not job_record:
            return jsonify({"error": "Job not found"}), 404
        
        # Get analyses for this job
        analyses = supabase_client.get_analyses_by_job(job_id)
        
        if not analyses:
            return jsonify({"error": "No results available yet"}), 404
        
        # Format results
        results = []
        for analysis in analyses:
            # Get repository info
            repo = supabase_client.get_repository(analysis.repository_id)
            if repo:
                results.append({
                    "repository": {
                        "name": repo.name,
                        "url": repo.url,
                        "stars": repo.stars
                    },
                    "analysis": {
                        "content": analysis.analysis_content,
                        "processed_at": analysis.processed_at,
                        "worker_id": analysis.worker_id
                    }
                })
        
        return jsonify({
            "job_id": job_id,
            "status": job_record.status,
            "total_results": len(results),
            "results": results
        })
        
    except Exception as e:
        logger.error(f"Error getting job results: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    
    logger.info(f"🚀 Starting unified service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
