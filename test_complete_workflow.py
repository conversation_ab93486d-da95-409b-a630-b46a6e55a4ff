#!/usr/bin/env python3
"""
Test the complete workflow with just 3 repositories to verify everything works
and get the analysis file quickly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Run a small complete workflow test."""
    print("🚀 COMPLETE WORKFLOW TEST - 3 REPOSITORIES")
    print("=" * 60)
    
    # Test with very small parameters for quick completion
    test_params = {
        'keywords': 'python test',
        'min_stars': 1000,
        'max_repos': 3  # Very small for quick test
    }
    
    print(f"🔍 Test Parameters:")
    print(f"   Keywords: {test_params['keywords']}")
    print(f"   Min stars: {test_params['min_stars']}")
    print(f"   Max repos: {test_params['max_repos']}")
    print()
    
    try:
        # Import main function
        from main import main as run_main
        import sys
        
        # Set up arguments
        original_argv = sys.argv.copy()
        sys.argv = [
            'main.py',
            '--keywords', test_params['keywords'],
            '--min-stars', str(test_params['min_stars']),
            '--max-repos', str(test_params['max_repos']),
            '--debug'
        ]
        
        print("📋 Running main pipeline...")
        print(f"   Command: {' '.join(sys.argv)}")
        print()
        
        # Run the main pipeline
        result = run_main()
        
        # Restore original argv
        sys.argv = original_argv
        
        if result:
            print("\n🎉 COMPLETE WORKFLOW TEST SUCCESSFUL!")
            print("✅ Check output directory for analysis files")
            print("✅ Check Supabase for database records")
        else:
            print("\n❌ Workflow test failed")
            
        return result
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
