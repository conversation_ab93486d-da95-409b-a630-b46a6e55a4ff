This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/**/*.py, main.py, service_unified.py, worker_unified.py, run_comprehensive_test.py, .env
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
main.py
run_comprehensive_test.py
service_unified.py
src/__init__.py
src/config.py
src/data_fetcher.py
src/github_search.py
src/llm_rate_limiter.py
src/monitoring.py
src/pipeline.py
src/redis_queue.py
src/sentry_analyzer.py
src/sentry_config.py
src/storage_manager.py
src/supabase_client.py
src/utils.py
worker_unified.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="run_comprehensive_test.py">
#!/usr/bin/env python3
"""
Comprehensive test runner that bypasses Redis requirement
and runs the complete analysis with your specified parameters.
"""

import os
import sys
import uuid
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from github_search import search_repositories
from config import ScraperConfig

def main():
    """Run comprehensive test with specified parameters."""
    
    print("🚀 COMPREHENSIVE REPOSITORY RESEARCH TEST")
    print("=" * 60)
    print("Parameters:")
    print("   Keywords: 'proxy list', 'free proxy', 'proxy scrape'")
    print("   Min stars: 30")
    print("   Max repos per keyword: 60")
    print("   Expected total: ~180 repositories")
    print()
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = ScraperConfig()
        print("✅ Configuration loaded successfully")
        
        # Search for repositories
        print("\n🔍 Searching GitHub repositories...")
        keywords = "proxy list,free proxy,proxy scrape"
        
        repositories = search_repositories(
            keywords=keywords,
            min_stars=30,
            max_repos=60
        )
        
        print(f"✅ Found {len(repositories)} repositories total")
        
        if not repositories:
            print("❌ No repositories found. Check GitHub token and rate limits.")
            return False
        
        # Show top repositories by category
        print("\n📊 Top repositories found:")
        for i, repo in enumerate(repositories[:10]):
            print(f"   {i+1:2d}. {repo['name']} ({repo['stars']} stars)")
        
        if len(repositories) > 10:
            print(f"   ... and {len(repositories) - 10} more repositories")
        
        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        output_dir = f"output/comprehensive_test_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n📁 Output directory: {output_dir}")
        
        # Process repositories (simplified without Redis)
        print("\n🔄 Processing repositories...")
        
        # Create analysis data structure
        analysis_data = {
            "metadata": {
                "generated": datetime.now().isoformat(),
                "keywords": keywords,
                "total_repositories": len(repositories),
                "min_stars": 30,
                "max_repos_per_keyword": 60,
                "github_token_used": bool(config.GITHUB_TOKEN),
                "processing_mode": "comprehensive_test"
            },
            "repositories": [],
            "summary": {
                "total_found": len(repositories),
                "highest_stars": max(repo['stars'] for repo in repositories),
                "lowest_stars": min(repo['stars'] for repo in repositories),
                "average_stars": sum(repo['stars'] for repo in repositories) / len(repositories),
                "languages": {},
                "topics": {}
            }
        }
        
        # Process each repository
        for i, repo in enumerate(repositories):
            print(f"   Processing {i+1}/{len(repositories)}: {repo['name']}")
            
            # Add repository data
            repo_data = {
                "name": repo['name'],
                "full_name": repo['full_name'],
                "url": repo['html_url'],
                "stars": repo['stars'],
                "language": repo.get('language', 'Unknown'),
                "description": repo.get('description', ''),
                "updated_at": repo.get('updated_at', ''),
                "topics": repo.get('topics', [])
            }
            
            analysis_data["repositories"].append(repo_data)
            
            # Update summary statistics
            lang = repo.get('language', 'Unknown')
            analysis_data["summary"]["languages"][lang] = analysis_data["summary"]["languages"].get(lang, 0) + 1
            
            for topic in repo.get('topics', []):
                analysis_data["summary"]["topics"][topic] = analysis_data["summary"]["topics"].get(topic, 0) + 1
        
        # Save analysis file
        import json
        analysis_file = os.path.join(output_dir, "comprehensive_analysis.json")
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Analysis saved to: {analysis_file}")
        
        # Create summary report
        summary_file = os.path.join(output_dir, "summary_report.md")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"# Repository Research Test Results\n\n")
            f.write(f"**Generated:** {datetime.now().isoformat()}\n\n")
            f.write(f"## Search Parameters\n")
            f.write(f"- **Keywords:** {keywords}\n")
            f.write(f"- **Min stars:** 30\n")
            f.write(f"- **Max repos per keyword:** 60\n")
            f.write(f"- **Total found:** {len(repositories)} repositories\n\n")
            
            f.write(f"## Summary Statistics\n")
            f.write(f"- **Highest stars:** {analysis_data['summary']['highest_stars']:,}\n")
            f.write(f"- **Lowest stars:** {analysis_data['summary']['lowest_stars']:,}\n")
            f.write(f"- **Average stars:** {analysis_data['summary']['average_stars']:.1f}\n\n")
            
            f.write(f"## Top Languages\n")
            sorted_langs = sorted(analysis_data['summary']['languages'].items(), key=lambda x: x[1], reverse=True)
            for lang, count in sorted_langs[:10]:
                f.write(f"- **{lang}:** {count} repositories\n")
            
            f.write(f"\n## Top Repositories\n")
            for i, repo in enumerate(repositories[:20]):
                f.write(f"{i+1}. **{repo['name']}** ({repo['stars']:,} stars) - {repo.get('description', 'No description')[:100]}...\n")
        
        print(f"✅ Summary report saved to: {summary_file}")
        
        print("\n🎉 COMPREHENSIVE TEST COMPLETED SUCCESSFULLY!")
        print(f"   Total repositories processed: {len(repositories)}")
        print(f"   Analysis file: {analysis_file}")
        print(f"   Summary report: {summary_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Comprehensive test failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
</file>

<file path="service_unified.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Unified Service API

This service provides a REST API for the unified architecture that:
1. Creates job records in Supabase
2. Adds repository tasks to Redis queues
3. Returns job status from Supabase

Workers run separately and process the queued tasks.
"""

import os
import sys
import uuid
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from werkzeug.exceptions import BadRequest

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import unified components
from config import ScraperConfig
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord
from redis_queue import QueueManager, RepositoryQueueItem
from github_search import search_repositories

# Initialize Flask app
app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize unified services
config = ScraperConfig()
supabase_client = create_supabase_client()
queue_manager = QueueManager(config.REDIS_URL)

logger.info("✅ Unified pipeline service initialized successfully")

class JobStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "Repository Research Tool - Unified Architecture",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "architecture": "unified",
        "components": {
            "redis": config.REDIS_URL,
            "supabase": config.SUPABASE_URL
        }
    })

@app.route('/start', methods=['POST'])
def start_analysis():
    """Start a new repository analysis job using unified architecture."""
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("JSON data required")
        
        # Validate required parameters
        keywords = data.get('keywords')
        if not keywords:
            raise BadRequest("keywords parameter is required")
        
        min_stars = data.get('min_stars', 30)
        max_repos = data.get('max_repos', 20)
        custom_prompt = data.get('custom_prompt')
        debug = data.get('debug', False)
        
        # Create job ID
        job_id = str(uuid.uuid4())
        logger.info(f"🚀 Starting job {job_id} with keywords: {keywords}")
        
        # Search for repositories
        all_repositories = search_repositories(
            keywords=keywords,
            min_stars=min_stars,
            max_repos_per_keyword=max_repos
        )
        
        if not all_repositories:
            return jsonify({"error": "No repositories found matching criteria"}), 404
        
        logger.info(f"📊 Found {len(all_repositories)} repositories for job {job_id}")
        
        # Create job record in Supabase
        job_record = JobRecord(
            id=job_id,
            keywords=keywords,
            min_stars=min_stars,
            max_repos=max_repos,
            status=JobStatus.RUNNING,
            created_at=datetime.now().isoformat(),
            custom_prompt=custom_prompt,
            debug=debug
        )
        supabase_client.create_job(job_record)
        logger.info(f"✅ Job record created in Supabase: {job_id}")
        
        # Create repository records and add to Redis queue
        repo_queue = queue_manager.get_repository_queue()
        
        for repo in all_repositories:
            # Create repository record in Supabase
            repo_record = RepositoryRecord(
                id=str(uuid.uuid4()),
                job_id=job_id,
                name=repo['name'],
                url=repo['url'],
                stars=repo['stars'],
                file_size_mb=0.0,  # Will be updated after repomix
                analysis_status="pending"
            )
            supabase_client.create_repository(repo_record)
            
            # Add to Redis queue for worker processing
            queue_item = RepositoryQueueItem(
                repository_name=repo['name'],
                repository_url=repo['url'],
                job_id=job_id
            )
            repo_queue.put(queue_item)
            logger.info(f"📤 Queued repository: {repo['name']}")
        
        logger.info(f"✅ Job {job_id} setup complete - {len(all_repositories)} repositories queued")
        
        return jsonify({
            "job_id": job_id,
            "status": JobStatus.RUNNING,
            "message": f"Analysis job started with {len(all_repositories)} repositories",
            "repositories_count": len(all_repositories),
            "parameters": {
                "keywords": keywords,
                "min_stars": min_stars,
                "max_repos": max_repos,
                "custom_prompt": custom_prompt,
                "debug": debug
            }
        }), 202
        
    except BadRequest as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error starting analysis: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a specific job."""
    try:
        # Get job from Supabase
        job_record = supabase_client.get_job(job_id)
        if not job_record:
            return jsonify({"error": "Job not found"}), 404
        
        # Get repositories for this job
        repositories = supabase_client.get_repositories_by_job(job_id)
        
        # Get analyses for this job
        analyses = supabase_client.get_analyses_by_job(job_id)
        
        # Calculate progress
        total_repos = len(repositories)
        completed_analyses = len(analyses)
        
        # Determine overall status
        if completed_analyses == total_repos and total_repos > 0:
            status = JobStatus.COMPLETED
        elif completed_analyses > 0:
            status = JobStatus.RUNNING
        else:
            status = job_record.status
        
        return jsonify({
            "job_id": job_id,
            "status": status,
            "created_at": job_record.created_at,
            "progress": {
                "total_repositories": total_repos,
                "completed_analyses": completed_analyses,
                "percentage": round((completed_analyses / total_repos * 100) if total_repos > 0 else 0, 1)
            },
            "parameters": {
                "keywords": job_record.keywords,
                "min_stars": job_record.min_stars,
                "max_repos": job_record.max_repos,
                "custom_prompt": job_record.custom_prompt
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/results/<job_id>', methods=['GET'])
def get_job_results(job_id):
    """Get the results of a completed job."""
    try:
        # Get job from Supabase
        job_record = supabase_client.get_job(job_id)
        if not job_record:
            return jsonify({"error": "Job not found"}), 404
        
        # Get analyses for this job
        analyses = supabase_client.get_analyses_by_job(job_id)
        
        if not analyses:
            return jsonify({"error": "No results available yet"}), 404
        
        # Format results
        results = []
        for analysis in analyses:
            # Get repository info
            repo = supabase_client.get_repository(analysis.repository_id)
            if repo:
                results.append({
                    "repository": {
                        "name": repo.name,
                        "url": repo.url,
                        "stars": repo.stars
                    },
                    "analysis": {
                        "content": analysis.analysis_content,
                        "processed_at": analysis.processed_at,
                        "worker_id": analysis.worker_id
                    }
                })
        
        return jsonify({
            "job_id": job_id,
            "status": job_record.status,
            "total_results": len(results),
            "results": results
        })
        
    except Exception as e:
        logger.error(f"Error getting job results: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    
    logger.info(f"🚀 Starting unified service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
</file>

<file path="src/__init__.py">

</file>

<file path="src/data_fetcher.py">
#!/usr/bin/env python3
"""
Data fetcher for Repository Research Tool.
Auto-fetches analysis files from cloud storage to local output directory.
"""

import os
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class DataFetcher:
    """
    Fetches analysis files from cloud storage to local directory.
    Provides easy access to analysis results for review and processing.
    """
    
    def __init__(self, config=None, storage_manager=None, supabase_client=None):
        """
        Initialize data fetcher.
        
        Args:
            config: ScraperConfig instance
            storage_manager: StorageManager instance
            supabase_client: Supabase client instance
        """
        self.config = config
        self.storage_manager = storage_manager
        self.supabase_client = supabase_client
        
        # Local output directory
        self.local_output_dir = Path("output")
        self.local_output_dir.mkdir(exist_ok=True)
        
        # Downloaded files tracking
        self.downloads_dir = self.local_output_dir / "downloads"
        self.downloads_dir.mkdir(exist_ok=True)
        
        logger.info(f"Data fetcher initialized, output directory: {self.local_output_dir}")
    
    def fetch_job_analysis(self, job_id: str, force_refresh: bool = False) -> Optional[Path]:
        """
        Fetch analysis file for a specific job.
        
        Args:
            job_id: Job ID to fetch analysis for
            force_refresh: Force re-download even if file exists locally
            
        Returns:
            Path to local analysis file, or None if not found
        """
        try:
            # Create job-specific directory
            job_dir = self.downloads_dir / job_id
            job_dir.mkdir(exist_ok=True)
            
            # Check if already downloaded
            local_analysis_file = job_dir / "analysis.json"
            if local_analysis_file.exists() and not force_refresh:
                logger.info(f"Analysis file already exists locally: {local_analysis_file}")
                return local_analysis_file
            
            # Get job information from database
            if not self.supabase_client:
                logger.error("Supabase client not available for fetching job data")
                return None
            
            job_record = self.supabase_client.get_job(job_id)
            if not job_record:
                logger.error(f"Job {job_id} not found in database")
                return None
            
            # Check if job has analysis file URL
            analysis_file_url = getattr(job_record, 'analysis_file_url', None)
            if not analysis_file_url:
                logger.warning(f"Job {job_id} has no analysis file URL")
                return None
            
            # Download analysis file from storage
            if not self.storage_manager:
                logger.error("Storage manager not available for downloading files")
                return None
            
            logger.info(f"Downloading analysis file for job {job_id}...")
            analysis_content = self.storage_manager.download_analysis_file(analysis_file_url)
            
            if not analysis_content:
                logger.error(f"Failed to download analysis file for job {job_id}")
                return None
            
            # Save to local file
            with open(local_analysis_file, 'w', encoding='utf-8') as f:
                if isinstance(analysis_content, str):
                    f.write(analysis_content)
                else:
                    json.dump(analysis_content, f, indent=2)
            
            # Create metadata file
            metadata = {
                "job_id": job_id,
                "downloaded_at": datetime.now().isoformat(),
                "source_url": analysis_file_url,
                "file_size": local_analysis_file.stat().st_size,
                "job_status": getattr(job_record, 'status', 'unknown'),
                "created_at": getattr(job_record, 'created_at', None)
            }
            
            metadata_file = job_dir / "metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"✅ Analysis file downloaded: {local_analysis_file}")
            return local_analysis_file
            
        except Exception as e:
            logger.error(f"Error fetching analysis for job {job_id}: {e}")
            return None
    
    def fetch_recent_analyses(self, limit: int = 10, days: int = 7) -> List[Path]:
        """
        Fetch recent analysis files.
        
        Args:
            limit: Maximum number of analyses to fetch
            days: Number of days to look back
            
        Returns:
            List of paths to downloaded analysis files
        """
        try:
            if not self.supabase_client:
                logger.error("Supabase client not available")
                return []
            
            # Get recent completed jobs
            recent_jobs = self.supabase_client.get_recent_jobs(limit=limit, days=days)
            
            downloaded_files = []
            for job in recent_jobs:
                job_id = getattr(job, 'id', None)
                if job_id:
                    analysis_file = self.fetch_job_analysis(job_id)
                    if analysis_file:
                        downloaded_files.append(analysis_file)
            
            logger.info(f"✅ Downloaded {len(downloaded_files)} recent analysis files")
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Error fetching recent analyses: {e}")
            return []
    
    def fetch_all_analyses(self) -> List[Path]:
        """
        Fetch all available analysis files.
        
        Returns:
            List of paths to downloaded analysis files
        """
        try:
            if not self.supabase_client:
                logger.error("Supabase client not available")
                return []
            
            # Get all completed jobs
            all_jobs = self.supabase_client.get_all_completed_jobs()
            
            downloaded_files = []
            for job in all_jobs:
                job_id = getattr(job, 'id', None)
                if job_id:
                    analysis_file = self.fetch_job_analysis(job_id)
                    if analysis_file:
                        downloaded_files.append(analysis_file)
            
            logger.info(f"✅ Downloaded {len(downloaded_files)} analysis files")
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Error fetching all analyses: {e}")
            return []
    
    def list_local_analyses(self) -> List[Dict]:
        """
        List all locally downloaded analysis files.
        
        Returns:
            List of dictionaries with analysis file information
        """
        analyses = []
        
        try:
            for job_dir in self.downloads_dir.iterdir():
                if not job_dir.is_dir():
                    continue
                
                analysis_file = job_dir / "analysis.json"
                metadata_file = job_dir / "metadata.json"
                
                if analysis_file.exists():
                    # Load metadata if available
                    metadata = {}
                    if metadata_file.exists():
                        try:
                            with open(metadata_file) as f:
                                metadata = json.load(f)
                        except:
                            pass
                    
                    # Get file stats
                    stat = analysis_file.stat()
                    
                    analyses.append({
                        "job_id": job_dir.name,
                        "analysis_file": str(analysis_file),
                        "file_size": stat.st_size,
                        "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "metadata": metadata
                    })
            
            # Sort by modification time (newest first)
            analyses.sort(key=lambda x: x["modified_at"], reverse=True)
            
            return analyses
            
        except Exception as e:
            logger.error(f"Error listing local analyses: {e}")
            return []
    
    def cleanup_old_downloads(self, days: int = 30):
        """
        Clean up old downloaded files.
        
        Args:
            days: Remove files older than this many days
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
            removed_count = 0
            
            for job_dir in self.downloads_dir.iterdir():
                if not job_dir.is_dir():
                    continue
                
                # Check if directory is old
                if job_dir.stat().st_mtime < cutoff_time:
                    shutil.rmtree(job_dir)
                    removed_count += 1
                    logger.debug(f"Removed old download directory: {job_dir}")
            
            if removed_count > 0:
                logger.info(f"✅ Cleaned up {removed_count} old download directories")
            
        except Exception as e:
            logger.error(f"Error cleaning up old downloads: {e}")
    
    def create_analysis_index(self) -> Path:
        """
        Create an index file of all local analyses.
        
        Returns:
            Path to the index file
        """
        try:
            analyses = self.list_local_analyses()
            
            index = {
                "generated_at": datetime.now().isoformat(),
                "total_analyses": len(analyses),
                "analyses": analyses
            }
            
            index_file = self.downloads_dir / "index.json"
            with open(index_file, 'w') as f:
                json.dump(index, f, indent=2)
            
            logger.info(f"✅ Created analysis index: {index_file}")
            return index_file
            
        except Exception as e:
            logger.error(f"Error creating analysis index: {e}")
            return None
</file>

<file path="src/monitoring.py">
"""
Comprehensive monitoring and logging system for repository research tool.
Integrates with Sentry and creates detailed logs for analysis.
"""
import os
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration


class RepositoryMonitor:
    """Comprehensive monitoring for repository processing with Sentry integration."""
    
    def __init__(self, output_dir: Path):
        self.output_dir = Path(output_dir)
        self.logs_dir = self.output_dir / "logs"
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize log files
        self.run_log_file = self.logs_dir / "run_analysis.json"
        self.errors_log_file = self.logs_dir / "errors.json"
        self.metrics_log_file = self.logs_dir / "metrics.json"
        self.sentry_log_file = self.logs_dir / "sentry_events.json"
        
        # Initialize data structures
        self.run_data = {
            "start_time": datetime.now().isoformat(),
            "configuration": {},
            "search_results": {},
            "processing_stats": {
                "repositories_found": 0,
                "repositories_attempted": 0,
                "repositories_successful": 0,
                "repositories_failed": 0,
                "repomix_failures": [],
                "llm_failures": [],
                "rate_limit_events": []
            },
            "performance_metrics": {},
            "end_time": None,
            "status": "running"
        }
        
        self.errors = []
        self.metrics = []
        self.sentry_events = []
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup comprehensive logging with Sentry integration."""
        # Configure Python logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.logs_dir / "detailed.log"),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("repository_monitor")
        
        # Add custom Sentry breadcrumb handler
        def custom_breadcrumb_processor(crumb, hint):
            """Process and log all Sentry breadcrumbs."""
            self.sentry_events.append({
                "timestamp": datetime.now().isoformat(),
                "type": "breadcrumb",
                "data": crumb,
                "hint": str(hint) if hint else None
            })
            return crumb
            
        # Configure Sentry with enhanced logging
        sentry_dsn = os.getenv('SENTRY_DSN')
        if sentry_dsn and sentry_dsn != 'your_sentry_dsn_here':
            sentry_sdk.init(
                dsn=os.getenv('SENTRY_DSN'),
                traces_sample_rate=1.0,
                profiles_sample_rate=1.0,
                integrations=[
                    LoggingIntegration(
                        level=logging.INFO,
                        event_level=logging.ERROR,
                        sentry_logs_level=logging.INFO
                    )
                ],
                before_breadcrumb=custom_breadcrumb_processor,
                _experiments={"enable_logs": True},
                environment=os.getenv('ENVIRONMENT', 'development'),
                release=os.getenv('RELEASE', 'repository-research-tool@1.0.0'),
            )
            
    def log_configuration(self, config: Dict[str, Any]):
        """Log the run configuration."""
        self.run_data["configuration"] = config
        self.logger.info(f"Configuration logged: {config}")
        sentry_sdk.add_breadcrumb(
            category="configuration",
            message="Run configuration set",
            level="info",
            data=config
        )
        
    def log_search_results(self, keyword: str, found_count: int, requested_count: int):
        """Log GitHub search results."""
        self.run_data["search_results"][keyword] = {
            "found": found_count,
            "requested": requested_count,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"Search results - {keyword}: {found_count}/{requested_count}")
        sentry_sdk.add_breadcrumb(
            category="search",
            message=f"GitHub search completed for '{keyword}'",
            level="info",
            data={"keyword": keyword, "found": found_count, "requested": requested_count}
        )

    def log_duplicate_detection(self, duplicate_info: Dict[str, Any]):
        """Log duplicate detection results."""
        self.run_data["duplicate_detection"] = duplicate_info

        self.logger.info(f"Duplicate detection: {duplicate_info['duplicates_removed']} duplicates removed "
                        f"({duplicate_info['duplicate_rate']:.1f}% duplicate rate)")

        sentry_sdk.add_breadcrumb(
            category="deduplication",
            message=f"Removed {duplicate_info['duplicates_removed']} duplicates "
                   f"({duplicate_info['duplicate_rate']:.1f}% rate)",
            level="info",
            data=duplicate_info
        )

        # Log multi-keyword repositories if any
        if duplicate_info.get('multi_keyword_repos'):
            multi_keyword_count = len(duplicate_info['multi_keyword_repos'])
            self.logger.info(f"Found {multi_keyword_count} repositories matching multiple keywords")

            sentry_sdk.add_breadcrumb(
                category="deduplication",
                message=f"{multi_keyword_count} repositories found by multiple keywords",
                level="info",
                data={"multi_keyword_repos": duplicate_info['multi_keyword_repos']}
            )

    def log_repository_start(self, repo_name: str, worker_id: int, worker_type: str):
        """Log when repository processing starts."""
        self.run_data["processing_stats"]["repositories_attempted"] += 1
        
        self.logger.info(f"{worker_type} Worker {worker_id}: Starting {repo_name}")
        sentry_sdk.add_breadcrumb(
            category="processing",
            message=f"Repository processing started: {repo_name}",
            level="info",
            data={"repo_name": repo_name, "worker_id": worker_id, "worker_type": worker_type}
        )
        
    def log_repository_success(self, repo_name: str, worker_id: int, worker_type: str, 
                             duration: float, file_size_mb: float = None):
        """Log successful repository processing."""
        self.run_data["processing_stats"]["repositories_successful"] += 1
        
        log_data = {
            "repo_name": repo_name,
            "worker_id": worker_id,
            "worker_type": worker_type,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        if file_size_mb:
            log_data["file_size_mb"] = file_size_mb
            
        self.metrics.append(log_data)
        
        self.logger.info(f"{worker_type} Worker {worker_id}: ✅ Completed {repo_name} - Duration: {duration:.1f}s")
        sentry_sdk.add_breadcrumb(
            category="success",
            message=f"Repository processed successfully: {repo_name}",
            level="info",
            data=log_data
        )
        
    def log_repository_failure(self, repo_name: str, worker_id: int, worker_type: str,
                             error_message: str, duration: float):
        """Log repository processing failure."""
        self.run_data["processing_stats"]["repositories_failed"] += 1
        
        error_data = {
            "repo_name": repo_name,
            "worker_id": worker_id,
            "worker_type": worker_type,
            "error_message": error_message,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        if worker_type == "repomix":
            self.run_data["processing_stats"]["repomix_failures"].append(error_data)
        else:
            self.run_data["processing_stats"]["llm_failures"].append(error_data)
            
        self.errors.append(error_data)
        
        self.logger.error(f"{worker_type} Worker {worker_id}: ❌ Failed {repo_name} - {error_message}")
        sentry_sdk.add_breadcrumb(
            category="error",
            message=f"Repository processing failed: {repo_name}",
            level="error",
            data=error_data
        )
        
        # Send to Sentry as an event for tracking
        sentry_sdk.capture_message(
            f"Repository processing failed: {repo_name}",
            level="error",
            extras=error_data
        )
        
    def log_rate_limit_event(self, worker_id: int, repo_name: str, retry_delay: int, attempt: int):
        """Log rate limiting events."""
        rate_limit_data = {
            "worker_id": worker_id,
            "repo_name": repo_name,
            "retry_delay": retry_delay,
            "attempt": attempt,
            "timestamp": datetime.now().isoformat()
        }
        
        self.run_data["processing_stats"]["rate_limit_events"].append(rate_limit_data)
        
        self.logger.warning(f"Rate limited - Worker {worker_id}, Repo: {repo_name}, Delay: {retry_delay}s, Attempt: {attempt}")
        sentry_sdk.add_breadcrumb(
            category="rate_limit",
            message=f"Rate limit encountered",
            level="warning",
            data=rate_limit_data
        )
        
    def log_performance_metrics(self, phase: str, duration: float, count: int, success_count: int = None):
        """Log performance metrics for different phases."""
        metrics_data = {
            "phase": phase,
            "duration": duration,
            "total_count": count,
            "success_count": success_count or count,
            "success_rate": (success_count or count) / count if count > 0 else 0,
            "timestamp": datetime.now().isoformat()
        }
        
        self.run_data["performance_metrics"][phase] = metrics_data
        
        self.logger.info(f"Performance - {phase}: {duration:.2f}s, Count: {count}, Success: {success_count or count}")
        sentry_sdk.add_breadcrumb(
            category="performance",
            message=f"Phase completed: {phase}",
            level="info",
            data=metrics_data
        )
        
    def finalize_run(self, status: str = "completed"):
        """Finalize the run and save all logs."""
        self.run_data["end_time"] = datetime.now().isoformat()
        self.run_data["status"] = status

        # Calculate final statistics
        total_found = sum(result["found"] for result in self.run_data["search_results"].values())
        self.run_data["processing_stats"]["repositories_found"] = total_found

        # Save all log files
        self._save_logs()

        # Send final summary to Sentry
        summary = {
            "total_repositories_found": total_found,
            "repositories_successful": self.run_data["processing_stats"]["repositories_successful"],
            "repositories_failed": self.run_data["processing_stats"]["repositories_failed"],
            "success_rate": self.run_data["processing_stats"]["repositories_successful"] / max(1, self.run_data["processing_stats"]["repositories_attempted"]),
            "total_duration": self._calculate_total_duration()
        }

        self.logger.info(f"Run completed: {summary}")
        sentry_sdk.capture_message(
            f"Repository research run completed",
            level="info",
            extras=summary
        )

        # Auto-fetch and analyze Sentry data
        self._auto_analyze_sentry_data()
        
    def _calculate_total_duration(self) -> float:
        """Calculate total run duration."""
        if self.run_data["end_time"]:
            start = datetime.fromisoformat(self.run_data["start_time"])
            end = datetime.fromisoformat(self.run_data["end_time"])
            return (end - start).total_seconds()
        return 0
        
    def _save_logs(self):
        """Save all logs to files."""
        # Save run analysis
        with open(self.run_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.run_data, f, indent=2, ensure_ascii=False, default=str)

        # Save errors
        with open(self.errors_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.errors, f, indent=2, ensure_ascii=False, default=str)

        # Save metrics
        with open(self.metrics_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False, default=str)

        # Save Sentry events
        with open(self.sentry_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.sentry_events, f, indent=2, ensure_ascii=False, default=str)
            
        print(f"\n📊 LOGS SAVED TO: {self.logs_dir}")
        print(f"  - Run analysis: {self.run_log_file}")
        print(f"  - Errors: {self.errors_log_file}")
        print(f"  - Metrics: {self.metrics_log_file}")
        print(f"  - Sentry events: {self.sentry_log_file}")

    def _auto_analyze_sentry_data(self):
        """Auto-fetch Sentry data and perform comprehensive analysis."""
        try:
            print(f"\n🔍 AUTO-ANALYZING SENTRY DATA...")

            # Fetch recent Sentry issues
            sentry_issues = self._fetch_recent_sentry_issues()

            # Comprehensive analysis
            analysis_data = {
                "timestamp": datetime.now().isoformat(),
                "run_summary": self.run_data,
                "sentry_analysis": sentry_issues,
                "error_analysis": self._analyze_local_errors(),
                "performance_analysis": self._analyze_performance(),
                "critical_issues": self._identify_critical_issues(sentry_issues),
                "recommendations": self._generate_enhanced_recommendations(sentry_issues)
            }

            # Save comprehensive analysis
            sentry_analysis_file = self.logs_dir / "auto_analysis.json"
            with open(sentry_analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)

            # Generate human-readable analysis report
            analysis_report_file = self.logs_dir / "auto_analysis_report.md"
            with open(analysis_report_file, 'w', encoding='utf-8') as f:
                f.write(self._generate_enhanced_analysis_report(analysis_data))

            # Generate Sentry-specific insights
            sentry_insights_file = self.logs_dir / "sentry_insights.md"
            with open(sentry_insights_file, 'w', encoding='utf-8') as f:
                f.write(self._generate_sentry_insights_report(sentry_issues))

            print(f"✅ Enhanced auto-analysis completed:")
            print(f"  - Analysis data: {sentry_analysis_file}")
            print(f"  - Analysis report: {analysis_report_file}")
            print(f"  - Sentry insights: {sentry_insights_file}")

        except Exception as e:
            print(f"⚠️ Auto-analysis failed: {e}")
            # Save the error for debugging
            error_file = self.logs_dir / "auto_analysis_error.txt"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"Auto-analysis error at {datetime.now().isoformat()}:\n")
                f.write(f"{str(e)}\n")
                f.write(f"\nTraceback:\n")
                import traceback
                f.write(traceback.format_exc())

    def _analyze_local_errors(self) -> Dict[str, Any]:
        """Analyze errors from local logs."""
        error_analysis = {
            "total_errors": len(self.errors),
            "repomix_failures": len(self.run_data["processing_stats"]["repomix_failures"]),
            "llm_failures": len(self.run_data["processing_stats"]["llm_failures"]),
            "rate_limit_events": len(self.run_data["processing_stats"]["rate_limit_events"]),
            "error_patterns": {},
            "failure_reasons": {}
        }

        # Analyze repomix failures
        for failure in self.run_data["processing_stats"]["repomix_failures"]:
            error_msg = failure.get("error_message", "unknown")
            if "clone repository" in error_msg:
                error_analysis["failure_reasons"]["git_clone_failures"] = error_analysis["failure_reasons"].get("git_clone_failures", 0) + 1
            elif "Invalid remote repository" in error_msg:
                error_analysis["failure_reasons"]["invalid_repo_urls"] = error_analysis["failure_reasons"].get("invalid_repo_urls", 0) + 1
            else:
                error_analysis["failure_reasons"]["other_repomix"] = error_analysis["failure_reasons"].get("other_repomix", 0) + 1

        return error_analysis

    def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze performance metrics."""
        stats = self.run_data["processing_stats"]
        total_attempted = stats["repositories_attempted"]
        total_successful = stats["repositories_successful"]
        total_found = stats["repositories_found"]

        # INTEGRITY FIX: Handle multiprocessing synchronization issues
        # If worker process stats weren't synchronized, use performance metrics as fallback
        if total_attempted == 0 and "total_processing" in self.run_data.get("performance_metrics", {}):
            perf_metrics = self.run_data["performance_metrics"]["total_processing"]
            total_attempted = perf_metrics.get("total_count", total_found)
            total_successful = perf_metrics.get("success_count", 0)

            # Log the synchronization issue
            self.logger.warning(f"INTEGRITY WARNING: Worker process stats not synchronized. Using fallback metrics.")
            self.logger.warning(f"  - repositories_attempted: {stats['repositories_attempted']} -> {total_attempted}")
            self.logger.warning(f"  - repositories_successful: {stats['repositories_successful']} -> {total_successful}")

        performance = {
            "success_rate": (total_successful / max(1, total_attempted)) * 100,
            "total_repositories": total_found,
            "processing_efficiency": (total_successful / max(1, total_found)) * 100,
            "rate_limit_frequency": len(stats["rate_limit_events"]) / max(1, total_attempted),
            "average_processing_time": self._calculate_average_processing_time(),
            "integrity_warning": total_attempted == 0 and total_found > 0
        }

        return performance

    def _calculate_average_processing_time(self) -> float:
        """Calculate average processing time from metrics."""
        if not self.metrics:
            return 0.0

        total_time = sum(metric.get("duration", 0) for metric in self.metrics)
        return total_time / len(self.metrics)

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on run data."""
        recommendations = []
        stats = self.run_data["processing_stats"]

        # INTEGRITY FIX: Use performance analysis for accurate success rate
        performance = self._analyze_performance()
        success_rate = performance["success_rate"]

        if performance.get("integrity_warning", False):
            recommendations.append("⚠️ MONITORING INTEGRITY WARNING: Worker process synchronization issues detected. Metrics may be inaccurate.")

        if success_rate < 50:
            recommendations.append("🚨 LOW SUCCESS RATE: Less than 50% of repositories processed successfully. Investigate repomix failures and API issues.")
        elif success_rate < 80:
            recommendations.append("⚠️ MODERATE SUCCESS RATE: Consider optimizing error handling and retry logic.")
        else:
            recommendations.append("✅ GOOD SUCCESS RATE: System performing well with high success rate.")

        # Rate limiting analysis
        rate_limit_count = len(stats["rate_limit_events"])
        if rate_limit_count > 100:
            recommendations.append("🚨 EXCESSIVE RATE LIMITING: Consider reducing concurrent workers or increasing delays.")
        elif rate_limit_count > 20:
            recommendations.append("⚠️ MODERATE RATE LIMITING: Monitor API usage patterns.")

        # Repository failures analysis
        repomix_failures = len(stats["repomix_failures"])
        if repomix_failures > 20:
            recommendations.append("🔧 HIGH REPOMIX FAILURES: Many repositories failed to process. Check repository accessibility and network connectivity.")

        return recommendations

    def _generate_analysis_report(self, analysis_data: Dict[str, Any]) -> str:
        """Generate human-readable analysis report."""
        report = []
        report.append("# Repository Research Tool - Auto Analysis Report")
        report.append(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Run Summary
        run_data = analysis_data["run_summary"]
        stats = run_data["processing_stats"]

        report.append("## Run Summary")
        report.append(f"- **Status**: {run_data['status']}")
        report.append(f"- **Duration**: {self._format_duration(self._calculate_total_duration())}")
        report.append(f"- **Repositories Found**: {stats['repositories_found']}")
        report.append(f"- **Repositories Attempted**: {stats['repositories_attempted']}")
        report.append(f"- **Repositories Successful**: {stats['repositories_successful']}")
        report.append(f"- **Success Rate**: {analysis_data['performance_analysis']['success_rate']:.1f}%")
        report.append("")

        # Error Analysis
        error_analysis = analysis_data["error_analysis"]
        report.append("## Error Analysis")
        report.append(f"- **Total Errors**: {error_analysis['total_errors']}")
        report.append(f"- **Repomix Failures**: {error_analysis['repomix_failures']}")
        report.append(f"- **LLM Failures**: {error_analysis['llm_failures']}")
        report.append(f"- **Rate Limit Events**: {error_analysis['rate_limit_events']}")

        if error_analysis["failure_reasons"]:
            report.append("\n### Failure Breakdown")
            for reason, count in error_analysis["failure_reasons"].items():
                report.append(f"- **{reason.replace('_', ' ').title()}**: {count}")
        report.append("")

        # Performance Analysis
        perf = analysis_data["performance_analysis"]
        report.append("## Performance Analysis")
        report.append(f"- **Processing Efficiency**: {perf['processing_efficiency']:.1f}%")
        report.append(f"- **Average Processing Time**: {perf['average_processing_time']:.1f}s")
        report.append(f"- **Rate Limit Frequency**: {perf['rate_limit_frequency']:.2f} per repository")
        report.append("")

        # Recommendations
        recommendations = analysis_data["recommendations"]
        if recommendations:
            report.append("## Recommendations")
            for rec in recommendations:
                report.append(f"- {rec}")
            report.append("")

        # Configuration
        config = run_data.get("configuration", {})
        if config:
            report.append("## Configuration Used")
            for key, value in config.items():
                report.append(f"- **{key}**: {value}")

        return "\n".join(report)

    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"

    def _fetch_recent_sentry_issues(self) -> Dict[str, Any]:
        """Fetch recent Sentry issues for analysis."""
        try:
            # This would use Sentry MCP tools if available
            # For now, return mock data structure
            return {
                "issues_fetched": 0,
                "connection_errors": 0,
                "timeout_errors": 0,
                "rate_limit_errors": 0,
                "api_errors": 0,
                "critical_issues": [],
                "fetch_error": "Sentry MCP tools not available in current context"
            }
        except Exception as e:
            return {
                "issues_fetched": 0,
                "fetch_error": str(e),
                "critical_issues": []
            }

    def _identify_critical_issues(self, sentry_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify critical issues from Sentry data and local logs."""
        critical_issues = []

        # Analyze local error patterns
        error_patterns = {}
        for error in self.errors:
            error_type = error.get("error_type", "unknown")
            error_msg = error.get("error_message", "")

            # Categorize errors
            if "connection" in error_msg.lower() or "reset" in error_msg.lower():
                pattern = "connection_reset"
            elif "timeout" in error_msg.lower():
                pattern = "timeout"
            elif "rate limit" in error_msg.lower() or "429" in error_msg:
                pattern = "rate_limit"
            elif "api" in error_msg.lower() and ("key" in error_msg.lower() or "auth" in error_msg.lower()):
                pattern = "api_auth"
            else:
                pattern = "other"

            if pattern not in error_patterns:
                error_patterns[pattern] = []
            error_patterns[pattern].append(error)

        # Generate critical issue summaries
        for pattern, errors in error_patterns.items():
            if len(errors) > 3:  # More than 3 occurrences = critical
                critical_issues.append({
                    "pattern": pattern,
                    "count": len(errors),
                    "severity": "critical" if len(errors) > 10 else "high",
                    "description": self._get_pattern_description(pattern),
                    "examples": errors[:3],  # First 3 examples
                    "recommendation": self._get_pattern_recommendation(pattern)
                })

        return critical_issues

    def _get_pattern_description(self, pattern: str) -> str:
        """Get description for error pattern."""
        descriptions = {
            "connection_reset": "Network connections being forcibly closed by remote host",
            "timeout": "Operations timing out, likely due to large file processing or network issues",
            "rate_limit": "API rate limiting preventing requests from completing",
            "api_auth": "API authentication or authorization failures",
            "other": "Miscellaneous errors requiring investigation"
        }
        return descriptions.get(pattern, "Unknown error pattern")

    def _get_pattern_recommendation(self, pattern: str) -> str:
        """Get recommendation for error pattern."""
        recommendations = {
            "connection_reset": "Implement connection pooling, retry logic, and reduce request payload sizes",
            "timeout": "Increase timeout values, implement chunking for large files, add progress monitoring",
            "rate_limit": "Implement exponential backoff, reduce concurrent requests, check API tier limits",
            "api_auth": "Verify API key validity, check authentication headers, ensure proper permissions",
            "other": "Review error logs for specific failure patterns and implement targeted fixes"
        }
        return recommendations.get(pattern, "Investigate error details and implement appropriate fixes")

    def _generate_enhanced_recommendations(self, sentry_data: Dict[str, Any]) -> List[str]:
        """Generate enhanced recommendations based on comprehensive analysis."""
        recommendations = self._generate_recommendations()  # Get base recommendations

        # Add Sentry-specific recommendations
        if sentry_data.get("connection_errors", 0) > 5:
            recommendations.append("🌐 HIGH CONNECTION ERRORS: Implement connection pooling and retry logic for network stability")

        if sentry_data.get("timeout_errors", 0) > 3:
            recommendations.append("⏰ TIMEOUT ISSUES: Increase timeout values and implement chunking for large file processing")

        # Add recommendations based on critical issues
        critical_issues = sentry_data.get("critical_issues", [])
        for issue in critical_issues:
            recommendations.append(f"🚨 {issue['pattern'].upper()}: {issue['recommendation']}")

        return recommendations

    def _generate_enhanced_analysis_report(self, analysis_data: Dict[str, Any]) -> str:
        """Generate enhanced human-readable analysis report."""
        base_report = self._generate_analysis_report(analysis_data)

        # Add Sentry analysis section
        sentry_section = []
        sentry_section.append("\n## Sentry Analysis")

        sentry_data = analysis_data.get("sentry_analysis", {})
        if sentry_data.get("fetch_error"):
            sentry_section.append(f"- **Fetch Status**: ⚠️ {sentry_data['fetch_error']}")
        else:
            sentry_section.append(f"- **Issues Fetched**: {sentry_data.get('issues_fetched', 0)}")
            sentry_section.append(f"- **Connection Errors**: {sentry_data.get('connection_errors', 0)}")
            sentry_section.append(f"- **Timeout Errors**: {sentry_data.get('timeout_errors', 0)}")
            sentry_section.append(f"- **Rate Limit Errors**: {sentry_data.get('rate_limit_errors', 0)}")

        # Add critical issues section
        critical_issues = analysis_data.get("critical_issues", [])
        if critical_issues:
            sentry_section.append("\n### Critical Issues Identified")
            for issue in critical_issues:
                sentry_section.append(f"- **{issue['pattern'].replace('_', ' ').title()}** ({issue['severity']}): {issue['count']} occurrences")
                sentry_section.append(f"  - {issue['description']}")
                sentry_section.append(f"  - Recommendation: {issue['recommendation']}")

        return base_report + "\n".join(sentry_section)

    def _generate_sentry_insights_report(self, sentry_data: Dict[str, Any]) -> str:
        """Generate detailed Sentry insights report."""
        report = []
        report.append("# Sentry Insights Report")
        report.append(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Connection status
        if sentry_data.get("fetch_error"):
            report.append("## ⚠️ Sentry Connection Status")
            report.append(f"**Status**: Failed to fetch data")
            report.append(f"**Error**: {sentry_data['fetch_error']}")
            report.append("")
            report.append("### Manual Sentry Check Recommended")
            report.append("1. Visit https://imimi.de.sentry.io/issues/")
            report.append("2. Filter by project: repomixed-scraper")
            report.append("3. Check for recent issues in the last 2 hours")
            report.append("4. Look for patterns in:")
            report.append("   - Connection errors (ConnectionResetError)")
            report.append("   - Timeout errors (TimeoutExpired)")
            report.append("   - Rate limiting (429 errors)")
            report.append("   - API authentication issues")
        else:
            report.append("## ✅ Sentry Data Summary")
            report.append(f"- Issues fetched: {sentry_data.get('issues_fetched', 0)}")
            report.append(f"- Connection errors: {sentry_data.get('connection_errors', 0)}")
            report.append(f"- Timeout errors: {sentry_data.get('timeout_errors', 0)}")
            report.append(f"- Rate limit errors: {sentry_data.get('rate_limit_errors', 0)}")

        report.append("")
        report.append("## 🔍 Key Issues to Monitor")
        report.append("Based on recent Sentry data, watch for:")
        report.append("")
        report.append("### 1. Connection Reset Errors")
        report.append("- **Pattern**: `ConnectionResetError: [WinError 10054]`")
        report.append("- **Cause**: Remote host forcibly closing connections")
        report.append("- **Impact**: LLM API calls failing mid-request")
        report.append("- **Solution**: Implement connection pooling and retry logic")
        report.append("")
        report.append("### 2. Timeout Issues")
        report.append("- **Pattern**: `TimeoutExpired: Command timed out after 300 seconds`")
        report.append("- **Cause**: Large repositories taking too long to process")
        report.append("- **Impact**: Repomix operations failing on large codebases")
        report.append("- **Solution**: Increase timeouts and implement chunking")
        report.append("")
        report.append("### 3. API Rate Limiting")
        report.append("- **Pattern**: HTTP 429 responses")
        report.append("- **Cause**: Exceeding API rate limits")
        report.append("- **Impact**: LLM analysis requests being rejected")
        report.append("- **Solution**: Implement exponential backoff and reduce concurrency")

        return "\n".join(report)
</file>

<file path="src/sentry_analyzer.py">
"""
Sentry data analyzer for automatic log analysis and insights.
"""
import os
import requests
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional


class SentryAnalyzer:
    """Analyzer for Sentry data with automatic insights generation."""
    
    def __init__(self):
        self.base_url = "https://de.sentry.io/api/0"
        self.headers = {
            "Authorization": f"Bearer {os.getenv('SENTRY_AUTH_TOKEN', '')}",
            "Content-Type": "application/json"
        }
        
    def get_recent_issues(self, organization_slug: str, project_slug: str, hours_back: int = 2) -> List[Dict]:
        """Fetch recent issues from Sentry."""
        try:
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            url = f"{self.base_url}/projects/{organization_slug}/{project_slug}/issues/"
            params = {
                "statsPeriod": f"{hours_back}h",
                "query": f"timestamp:>{start_time.isoformat()}",
                "sort": "date",
                "limit": 50
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to fetch Sentry issues: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Error fetching Sentry issues: {e}")
            return []
    
    def get_recent_events(self, organization_slug: str, project_slug: str, hours_back: int = 2) -> List[Dict]:
        """Fetch recent events from Sentry."""
        try:
            url = f"{self.base_url}/projects/{organization_slug}/{project_slug}/events/"
            params = {
                "statsPeriod": f"{hours_back}h",
                "full": "true",
                "limit": 100
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to fetch Sentry events: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Error fetching Sentry events: {e}")
            return []
    
    def analyze_patterns(self, issues: List[Dict], events: List[Dict]) -> Dict[str, Any]:
        """Analyze patterns in Sentry data."""
        analysis = {
            "total_issues": len(issues),
            "total_events": len(events),
            "error_patterns": {},
            "rate_limit_events": 0,
            "api_errors": 0,
            "network_errors": 0,
            "most_common_errors": [],
            "error_frequency": {},
            "recommendations": []
        }
        
        # Analyze issues
        error_counts = {}
        for issue in issues:
            error_type = issue.get('type', 'unknown')
            title = issue.get('title', 'unknown')
            count = issue.get('count', 1)
            
            if error_type not in error_counts:
                error_counts[error_type] = 0
            error_counts[error_type] += count
            
            # Check for specific error patterns
            if 'rate limit' in title.lower() or '429' in title:
                analysis["rate_limit_events"] += count
            elif 'api' in title.lower() or 'http' in title.lower():
                analysis["api_errors"] += count
            elif 'network' in title.lower() or 'connection' in title.lower():
                analysis["network_errors"] += count
        
        # Sort errors by frequency
        analysis["most_common_errors"] = sorted(
            error_counts.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        analysis["error_frequency"] = error_counts
        
        # Analyze events for additional patterns
        event_patterns = {}
        for event in events:
            event_type = event.get('type', 'unknown')
            if event_type not in event_patterns:
                event_patterns[event_type] = 0
            event_patterns[event_type] += 1
        
        analysis["event_patterns"] = event_patterns
        
        return analysis
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Rate limiting recommendations
        if analysis["rate_limit_events"] > 10:
            recommendations.append(
                f"🚨 HIGH RATE LIMITING: {analysis['rate_limit_events']} rate limit events detected. "
                "Consider reducing concurrent workers or increasing delays between requests."
            )
        elif analysis["rate_limit_events"] > 0:
            recommendations.append(
                f"⚠️ Rate limiting detected: {analysis['rate_limit_events']} events. Monitor API usage patterns."
            )
        
        # API error recommendations
        if analysis["api_errors"] > 5:
            recommendations.append(
                f"🔧 API ERRORS: {analysis['api_errors']} API-related errors. "
                "Check API endpoint URLs, authentication, and request formats."
            )
        
        # Network error recommendations
        if analysis["network_errors"] > 5:
            recommendations.append(
                f"🌐 NETWORK ISSUES: {analysis['network_errors']} network-related errors. "
                "Check internet connectivity and consider implementing better retry logic."
            )
        
        # General error frequency recommendations
        if analysis["total_issues"] > 20:
            recommendations.append(
                f"📊 HIGH ERROR VOLUME: {analysis['total_issues']} total issues. "
                "Review error patterns and implement preventive measures."
            )
        
        # Most common error recommendations
        if analysis["most_common_errors"]:
            top_error = analysis["most_common_errors"][0]
            recommendations.append(
                f"🎯 TOP ERROR: '{top_error[0]}' occurred {top_error[1]} times. "
                "Focus on fixing this error type first for maximum impact."
            )
        
        # Success recommendations
        if analysis["total_issues"] == 0:
            recommendations.append("✅ NO ERRORS: Clean run with no Sentry issues detected!")
        
        if not recommendations:
            recommendations.append("📈 System appears stable with minimal error activity.")
        
        return recommendations
    
    def generate_summary_report(self, analysis: Dict[str, Any]) -> str:
        """Generate a human-readable summary report."""
        report = []
        report.append("# Sentry Analysis Summary")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Overview
        report.append("## Overview")
        report.append(f"- Total Issues: {analysis['total_issues']}")
        report.append(f"- Total Events: {analysis['total_events']}")
        report.append(f"- Rate Limit Events: {analysis['rate_limit_events']}")
        report.append(f"- API Errors: {analysis['api_errors']}")
        report.append(f"- Network Errors: {analysis['network_errors']}")
        report.append("")
        
        # Top errors
        if analysis["most_common_errors"]:
            report.append("## Most Common Errors")
            for error_type, count in analysis["most_common_errors"][:5]:
                report.append(f"- {error_type}: {count} occurrences")
            report.append("")
        
        # Recommendations
        recommendations = self.get_recommendations(analysis)
        if recommendations:
            report.append("## Recommendations")
            for rec in recommendations:
                report.append(f"- {rec}")
            report.append("")
        
        return "\n".join(report)
</file>

<file path="src/sentry_config.py">
"""
Sentry configuration and instrumentation for repository research tool.
"""
import os
import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
import logging


def init_sentry():
    """Initialize Sentry SDK with proper configuration."""
    sentry_dsn = os.getenv('SENTRY_DSN')

    if not sentry_dsn or sentry_dsn == 'your_sentry_dsn_here':
        print("⚠️ SENTRY_DSN not configured. Sentry monitoring disabled.")
        return False
    
    # Configure logging integration
    logging_integration = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )
    
    try:
        sentry_sdk.init(
            dsn=sentry_dsn,
            # Enable tracing for performance monitoring
            traces_sample_rate=1.0,  # 100% sampling for development/testing
            # Enable profiling
            profiles_sample_rate=1.0,
            # Add integrations
            integrations=[logging_integration],
            # Environment and release info
            environment=os.getenv('ENVIRONMENT', 'development'),
            release=os.getenv('RELEASE', 'repository-research-tool@1.0.0'),
            # Additional options
            send_default_pii=False,  # Don't send personally identifiable information
            debug=os.getenv('SENTRY_DEBUG', 'false').lower() == 'true',
            # Custom tags
            before_send=add_custom_tags,
        )
        
        print("✅ Sentry monitoring initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize Sentry: {e}")
        return False


def add_custom_tags(event, hint):
    """Add custom tags to Sentry events."""
    event.setdefault('tags', {}).update({
        'component': 'repository-research-tool',
        'worker_type': os.getenv('WORKER_TYPE', 'unknown'),
        'process_id': str(os.getpid()),
    })
    return event


def trace_function(operation_name):
    """Decorator to trace function execution with Sentry."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(name=operation_name, op="function"):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    sentry_sdk.capture_exception(e)
                    raise
        return wrapper
    return decorator


def trace_api_call(api_name, repo_name=None):
    """Context manager for tracing API calls."""
    class APITracer:
        def __init__(self, api_name, repo_name=None):
            self.api_name = api_name
            self.repo_name = repo_name
            self.transaction = None
            
        def __enter__(self):
            transaction_name = f"{self.api_name}"
            if self.repo_name:
                transaction_name += f" - {self.repo_name}"
                
            self.transaction = sentry_sdk.start_transaction(
                name=transaction_name,
                op="api_call"
            )
            self.transaction.__enter__()
            
            # Add context
            sentry_sdk.set_tag("api_name", self.api_name)
            if self.repo_name:
                sentry_sdk.set_tag("repository", self.repo_name)
                
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            if exc_type:
                sentry_sdk.capture_exception(exc_val)
            self.transaction.__exit__(exc_type, exc_val, exc_tb)
            
        def set_status(self, status_code, error_msg=None):
            """Set the status of the API call."""
            sentry_sdk.set_tag("status_code", str(status_code))
            if error_msg:
                sentry_sdk.set_context("error_details", {"message": error_msg})
                
        def add_breadcrumb(self, message, category="api", level="info"):
            """Add a breadcrumb to track API call progress."""
            sentry_sdk.add_breadcrumb(
                message=message,
                category=category,
                level=level
            )
    
    return APITracer(api_name, repo_name)


def log_worker_start(worker_type, worker_id):
    """Log worker startup with Sentry context."""
    sentry_sdk.set_tag("worker_type", worker_type)
    sentry_sdk.set_tag("worker_id", str(worker_id))
    sentry_sdk.add_breadcrumb(
        message=f"{worker_type} worker {worker_id} started",
        category="worker",
        level="info"
    )


def log_rate_limit_event(worker_id, repo_name, retry_delay, attempt):
    """Log rate limiting events for monitoring."""
    sentry_sdk.add_breadcrumb(
        message=f"Rate limited - Worker {worker_id}, Repo: {repo_name}, Delay: {retry_delay}s, Attempt: {attempt}",
        category="rate_limit",
        level="warning",
        data={
            "worker_id": worker_id,
            "repository": repo_name,
            "retry_delay": retry_delay,
            "attempt": attempt
        }
    )


def log_processing_metrics(phase, duration, count, success_count=None):
    """Log processing metrics for performance monitoring."""
    sentry_sdk.add_breadcrumb(
        message=f"{phase} completed - Duration: {duration:.2f}s, Count: {count}",
        category="metrics",
        level="info",
        data={
            "phase": phase,
            "duration": duration,
            "total_count": count,
            "success_count": success_count or count,
            "success_rate": (success_count or count) / count if count > 0 else 0
        }
    )
</file>

<file path="src/storage_manager.py">
"""
Storage manager for handling file operations with Supabase Storage.
Abstracts file storage operations for repomix files and analysis results.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path
from datetime import datetime
from src.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)

class StorageManager:
    """Manages file storage operations with Supabase Storage."""
    
    def __init__(self, supabase_client: SupabaseClient):
        """
        Initialize storage manager.
        
        Args:
            supabase_client: Configured Supabase client
        """
        self.client = supabase_client
        self.repomix_bucket = "repomixes"
        self.analysis_bucket = "analyses"
        self.logs_bucket = "logs"
    
    def upload_repomix_file(self, job_id: str, repository_name: str, 
                           content: str) -> str:
        """
        Upload repomix file to storage.
        
        Args:
            job_id: Job identifier
            repository_name: Repository name (sanitized)
            content: Repomix file content
            
        Returns:
            Public URL of uploaded file
        """
        try:
            # Create file path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"{job_id}/{repository_name}_{timestamp}.md"
            
            # Upload file
            url = self.client.upload_file(
                bucket=self.repomix_bucket,
                file_path=file_path,
                file_content=content.encode('utf-8'),
                content_type="text/markdown"
            )
            
            logger.info(f"Uploaded repomix file for {repository_name}: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload repomix file for {repository_name}: {e}")
            raise
    
    def upload_analysis_file(self, job_id: str, analysis_data: Dict, 
                           filename: Optional[str] = None) -> str:
        """
        Upload analysis results file to storage.
        
        Args:
            job_id: Job identifier
            analysis_data: Analysis data dictionary
            filename: Optional custom filename
            
        Returns:
            Public URL of uploaded file
        """
        try:
            # Create filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"analysis_{timestamp}.json"
            
            file_path = f"{job_id}/{filename}"
            
            # Convert to JSON
            json_content = json.dumps(analysis_data, indent=2, ensure_ascii=False)
            
            # Upload file
            url = self.client.upload_file(
                bucket=self.analysis_bucket,
                file_path=file_path,
                file_content=json_content.encode('utf-8'),
                content_type="application/json"
            )
            
            logger.info(f"Uploaded analysis file for job {job_id}: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload analysis file for job {job_id}: {e}")
            raise
    
    def upload_log_file(self, job_id: str, log_content: str, 
                       log_type: str = "general") -> str:
        """
        Upload log file to storage.
        
        Args:
            job_id: Job identifier
            log_content: Log file content
            log_type: Type of log (general, error, sentry, etc.)
            
        Returns:
            Public URL of uploaded file
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"{job_id}/{log_type}_{timestamp}.log"
            
            # Upload file
            url = self.client.upload_file(
                bucket=self.logs_bucket,
                file_path=file_path,
                file_content=log_content.encode('utf-8'),
                content_type="text/plain"
            )
            
            logger.debug(f"Uploaded log file for job {job_id}: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload log file for job {job_id}: {e}")
            raise
    
    def download_repomix_file(self, file_url: str) -> str:
        """
        Download repomix file content.
        
        Args:
            file_url: Public URL of the file
            
        Returns:
            File content as string
        """
        try:
            # Extract file path from URL
            file_path = self._extract_file_path_from_url(file_url, self.repomix_bucket)
            
            # Download file
            content_bytes = self.client.download_file(self.repomix_bucket, file_path)
            content = content_bytes.decode('utf-8')
            
            logger.debug(f"Downloaded repomix file: {file_path}")
            return content
            
        except Exception as e:
            logger.error(f"Failed to download repomix file {file_url}: {e}")
            raise
    
    def download_analysis_file(self, file_url: str) -> Dict:
        """
        Download and parse analysis file.
        
        Args:
            file_url: Public URL of the file
            
        Returns:
            Analysis data as dictionary
        """
        try:
            # Extract file path from URL
            file_path = self._extract_file_path_from_url(file_url, self.analysis_bucket)
            
            # Download file
            content_bytes = self.client.download_file(self.analysis_bucket, file_path)
            content = content_bytes.decode('utf-8')
            
            # Parse JSON
            analysis_data = json.loads(content)
            
            logger.debug(f"Downloaded analysis file: {file_path}")
            return analysis_data
            
        except Exception as e:
            logger.error(f"Failed to download analysis file {file_url}: {e}")
            raise
    
    def list_job_files(self, job_id: str, bucket: str) -> List[Dict]:
        """
        List all files for a specific job in a bucket.
        
        Args:
            job_id: Job identifier
            bucket: Bucket name
            
        Returns:
            List of file information dictionaries
        """
        try:
            files = self.client.list_files(bucket, job_id)
            logger.debug(f"Listed {len(files)} files for job {job_id} in bucket {bucket}")
            return files
        except Exception as e:
            logger.error(f"Failed to list files for job {job_id} in bucket {bucket}: {e}")
            raise
    
    def delete_job_files(self, job_id: str, bucket: str) -> int:
        """
        Delete all files for a specific job in a bucket.
        
        Args:
            job_id: Job identifier
            bucket: Bucket name
            
        Returns:
            Number of files deleted
        """
        try:
            files = self.list_job_files(job_id, bucket)
            deleted_count = 0
            
            for file_info in files:
                file_path = f"{job_id}/{file_info['name']}"
                self.client.delete_file(bucket, file_path)
                deleted_count += 1
            
            logger.info(f"Deleted {deleted_count} files for job {job_id} from bucket {bucket}")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to delete files for job {job_id} from bucket {bucket}: {e}")
            raise
    
    def get_storage_stats(self, job_id: Optional[str] = None) -> Dict[str, Dict]:
        """
        Get storage statistics.
        
        Args:
            job_id: Optional job ID to get stats for specific job
            
        Returns:
            Storage statistics by bucket
        """
        try:
            stats = {}
            buckets = [self.repomix_bucket, self.analysis_bucket, self.logs_bucket]
            
            for bucket in buckets:
                try:
                    if job_id:
                        files = self.list_job_files(job_id, bucket)
                    else:
                        files = self.client.list_files(bucket)
                    
                    total_size = sum(file_info.get('metadata', {}).get('size', 0) 
                                   for file_info in files)
                    
                    stats[bucket] = {
                        "file_count": len(files),
                        "total_size_bytes": total_size,
                        "total_size_mb": round(total_size / (1024 * 1024), 2)
                    }
                except Exception as e:
                    logger.warning(f"Failed to get stats for bucket {bucket}: {e}")
                    stats[bucket] = {"error": str(e)}
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            raise
    
    def _extract_file_path_from_url(self, file_url: str, bucket: str) -> str:
        """
        Extract file path from Supabase storage URL.
        
        Args:
            file_url: Full public URL
            bucket: Bucket name
            
        Returns:
            File path within the bucket
        """
        try:
            # Supabase storage URLs have format:
            # https://project.supabase.co/storage/v1/object/public/bucket/path
            url_parts = file_url.split(f"/storage/v1/object/public/{bucket}/")
            if len(url_parts) != 2:
                raise ValueError(f"Invalid storage URL format: {file_url}")
            
            return url_parts[1]
            
        except Exception as e:
            logger.error(f"Failed to extract file path from URL {file_url}: {e}")
            raise
    
    def create_local_backup(self, job_id: str, local_dir: str) -> Dict[str, str]:
        """
        Create local backup of all files for a job.
        
        Args:
            job_id: Job identifier
            local_dir: Local directory to save files
            
        Returns:
            Dictionary mapping bucket names to local file paths
        """
        try:
            local_paths = {}
            backup_dir = Path(local_dir) / f"backup_{job_id}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            buckets = [self.repomix_bucket, self.analysis_bucket, self.logs_bucket]
            
            for bucket in buckets:
                try:
                    files = self.list_job_files(job_id, bucket)
                    bucket_dir = backup_dir / bucket
                    bucket_dir.mkdir(exist_ok=True)
                    
                    for file_info in files:
                        file_path = f"{job_id}/{file_info['name']}"
                        content = self.client.download_file(bucket, file_path)
                        
                        local_file_path = bucket_dir / file_info['name']
                        local_file_path.write_bytes(content)
                        
                        if bucket not in local_paths:
                            local_paths[bucket] = []
                        local_paths[bucket].append(str(local_file_path))
                    
                except Exception as e:
                    logger.warning(f"Failed to backup files from bucket {bucket}: {e}")
            
            logger.info(f"Created local backup for job {job_id} in {backup_dir}")
            return local_paths
            
        except Exception as e:
            logger.error(f"Failed to create local backup for job {job_id}: {e}")
            raise

def create_storage_manager() -> StorageManager:
    """Create storage manager from environment variables."""
    from src.supabase_client import create_supabase_client
    
    supabase_client = create_supabase_client()
    return StorageManager(supabase_client)
</file>

<file path="worker_unified.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Unified Worker Process

This worker process starts and monitors the standalone repomix_worker and 
llm_worker functions from src/pipeline.py. It does not contain any pipeline 
logic itself - it simply manages the worker processes.
"""

import os
import sys
import time
import signal
import logging
import multiprocessing
from typing import List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import unified components
from config import ScraperConfig
from pipeline import repomix_worker, llm_worker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedWorkerManager:
    """Manages the standalone worker processes for the unified architecture."""
    
    def __init__(self):
        self.config = ScraperConfig()
        self.repomix_processes: List[multiprocessing.Process] = []
        self.llm_processes: List[multiprocessing.Process] = []
        self.running = False
        
        # Convert config to dict for passing to workers
        self.config_dict = {
            'REDIS_URL': self.config.REDIS_URL,
            'SUPABASE_URL': self.config.SUPABASE_URL,
            'SUPABASE_KEY': self.config.SUPABASE_KEY,
            'LLM_API_KEY': self.config.LLM_API_KEY,
            'LLM_BASE_URL': self.config.LLM_BASE_URL,
            'LLM_MODEL': self.config.LLM_MODEL,
            'FILE_INCLUDES': self.config.FILE_INCLUDES,
            'REPOMIX_WORKERS': self.config.REPOMIX_WORKERS,
            'LLM_WORKERS': self.config.LLM_WORKERS
        }
        
        logger.info("✅ Unified Worker Manager initialized")
        logger.info(f"   Repomix workers: {self.config.REPOMIX_WORKERS}")
        logger.info(f"   LLM workers: {self.config.LLM_WORKERS}")
        logger.info(f"   Redis URL: {self.config.REDIS_URL}")
        logger.info(f"   Supabase URL: {self.config.SUPABASE_URL}")
    
    def start_workers(self):
        """Start all worker processes."""
        try:
            logger.info("🚀 Starting unified worker processes...")
            
            # Start repomix workers
            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=repomix_worker,
                    args=(i, self.config_dict, "worker-pool"),  # Generic job_id for worker pool
                    name=f"repomix-worker-{i}"
                )
                p.start()
                self.repomix_processes.append(p)
                logger.info(f"✅ Started repomix worker {i} (PID: {p.pid})")
            
            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=llm_worker,
                    args=(i, self.config_dict, "worker-pool"),  # Generic job_id for worker pool
                    name=f"llm-worker-{i}"
                )
                p.start()
                self.llm_processes.append(p)
                logger.info(f"✅ Started LLM worker {i} (PID: {p.pid})")
            
            self.running = True
            logger.info(f"🎉 All workers started successfully!")
            logger.info(f"   Total processes: {len(self.repomix_processes) + len(self.llm_processes)}")
            
        except Exception as e:
            logger.error(f"❌ Failed to start workers: {e}")
            self.stop_workers()
            raise
    
    def monitor_workers(self):
        """Monitor worker processes and restart if they die."""
        logger.info("👀 Starting worker monitoring...")
        
        while self.running:
            try:
                # Check repomix workers
                for i, process in enumerate(self.repomix_processes):
                    if not process.is_alive():
                        logger.warning(f"⚠️ Repomix worker {i} died, restarting...")
                        process.terminate()
                        process.join(timeout=5)
                        
                        # Start new worker
                        new_process = multiprocessing.Process(
                            target=repomix_worker,
                            args=(i, self.config_dict, "worker-pool"),
                            name=f"repomix-worker-{i}"
                        )
                        new_process.start()
                        self.repomix_processes[i] = new_process
                        logger.info(f"✅ Restarted repomix worker {i} (PID: {new_process.pid})")
                
                # Check LLM workers
                for i, process in enumerate(self.llm_processes):
                    if not process.is_alive():
                        logger.warning(f"⚠️ LLM worker {i} died, restarting...")
                        process.terminate()
                        process.join(timeout=5)
                        
                        # Start new worker
                        new_process = multiprocessing.Process(
                            target=llm_worker,
                            args=(i, self.config_dict, "worker-pool"),
                            name=f"llm-worker-{i}"
                        )
                        new_process.start()
                        self.llm_processes[i] = new_process
                        logger.info(f"✅ Restarted LLM worker {i} (PID: {new_process.pid})")
                
                # Sleep before next check
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                logger.info("🛑 Received interrupt signal, stopping workers...")
                break
            except Exception as e:
                logger.error(f"❌ Error in worker monitoring: {e}")
                time.sleep(10)  # Wait before retrying
    
    def stop_workers(self):
        """Stop all worker processes."""
        logger.info("🛑 Stopping all worker processes...")
        self.running = False
        
        # Stop repomix workers
        for i, process in enumerate(self.repomix_processes):
            if process.is_alive():
                logger.info(f"Stopping repomix worker {i}...")
                process.terminate()
                process.join(timeout=10)
                if process.is_alive():
                    logger.warning(f"Force killing repomix worker {i}")
                    process.kill()
        
        # Stop LLM workers
        for i, process in enumerate(self.llm_processes):
            if process.is_alive():
                logger.info(f"Stopping LLM worker {i}...")
                process.terminate()
                process.join(timeout=10)
                if process.is_alive():
                    logger.warning(f"Force killing LLM worker {i}")
                    process.kill()
        
        logger.info("✅ All workers stopped")
    
    def get_worker_status(self):
        """Get the status of all worker processes."""
        repomix_alive = sum(1 for p in self.repomix_processes if p.is_alive())
        llm_alive = sum(1 for p in self.llm_processes if p.is_alive())
        
        return {
            "repomix_workers": {
                "total": len(self.repomix_processes),
                "alive": repomix_alive,
                "dead": len(self.repomix_processes) - repomix_alive
            },
            "llm_workers": {
                "total": len(self.llm_processes),
                "alive": llm_alive,
                "dead": len(self.llm_processes) - llm_alive
            },
            "total_processes": len(self.repomix_processes) + len(self.llm_processes),
            "running": self.running
        }

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    global worker_manager
    if worker_manager:
        worker_manager.stop_workers()
    sys.exit(0)

def main():
    """Main worker process entry point."""
    global worker_manager
    
    logger.info("🚀 Repository Research Tool - Unified Worker Process")
    logger.info("=" * 60)
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize worker manager
        worker_manager = UnifiedWorkerManager()
        
        # Start workers
        worker_manager.start_workers()
        
        # Monitor workers (blocks until interrupted)
        worker_manager.monitor_workers()
        
    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
    finally:
        if worker_manager:
            worker_manager.stop_workers()

if __name__ == '__main__':
    main()
</file>

<file path="src/github_search.py">
"""
GitHub repository search functionality
"""

import requests
import time
from typing import List, Dict, Any


class GitHubSearcher:
    """GitHub API searcher for repositories"""
    
    def __init__(self, config):
        """Initialize with configuration"""
        self.config = config
        self.base_url = "https://api.github.com"
        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Repository-Research-Tool/1.0'
        }
        
        # Add authentication if token is available
        if config.GITHUB_TOKEN:
            self.headers['Authorization'] = f'token {config.GITHUB_TOKEN}'
    
    def search_repositories(self, keyword: str, min_stars: int = 30, max_repos: int = 20) -> List[Dict[str, Any]]:
        """
        Search for repositories by keyword

        Args:
            keyword: Search keyword
            min_stars: Minimum number of stars
            max_repos: Maximum number of repositories to return

        Returns:
            List of repository dictionaries
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"🔍 Searching GitHub for '{keyword}' repositories...")

        # Build search query
        query = f"{keyword} stars:>={min_stars}"
        logger.info(f"📝 Search query: '{query}'")

        params = {
            'q': query,
            'sort': 'stars',
            'order': 'desc',
            'per_page': min(max_repos, 100),  # GitHub API limit is 100 per page
            'page': 1
        }

        logger.info(f"🌐 API request parameters: {params}")

        try:
            # Make API request
            logger.info(f"📡 Making GitHub API request...")
            response = requests.get(
                f"{self.base_url}/search/repositories",
                headers=self.headers,
                params=params,
                timeout=30
            )

            logger.info(f"📊 API response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                repositories = data.get('items', [])
                total_count = data.get('total_count', 0)

                logger.info(f"🎯 GitHub reports {total_count} total repositories available for '{keyword}'")
                logger.info(f"📦 Retrieved {len(repositories)} repositories in this request")

                # Limit to requested number
                repositories = repositories[:max_repos]

                logger.info(f"✅ Returning {len(repositories)} repositories for '{keyword}'")
                
                # Extract relevant information
                result = []
                for repo in repositories:
                    repo_info = {
                        'full_name': repo['full_name'],
                        'name': repo['name'],
                        'url': repo['html_url'],  # Add 'url' key for service compatibility
                        'clone_url': repo['clone_url'],
                        'html_url': repo['html_url'],
                        'stars': repo['stargazers_count'],  # Add 'stars' key for service compatibility
                        'stargazers_count': repo['stargazers_count'],
                        'language': repo.get('language', 'Unknown'),
                        'description': repo.get('description', ''),
                        'updated_at': repo['updated_at']
                    }
                    result.append(repo_info)
                
                return result
                
            elif response.status_code == 403:
                print(f"❌ GitHub API rate limit exceeded for '{keyword}'")
                return []
            elif response.status_code == 422:
                print(f"❌ Invalid search query for '{keyword}': {query}")
                return []
            else:
                print(f"❌ GitHub API error for '{keyword}': {response.status_code}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error searching for '{keyword}': {e}")
            return []
        except Exception as e:
            print(f"❌ Unexpected error searching for '{keyword}': {e}")
            return []
    
    def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current GitHub API rate limit status"""
        try:
            response = requests.get(
                f"{self.base_url}/rate_limit",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'error': str(e)}


def search_repositories(keywords: str, min_stars: int = 30, max_repos: int = 20) -> List[Dict[str, Any]]:
    """
    Standalone function to search repositories.

    Args:
        keywords: Comma-separated keywords to search for
        min_stars: Minimum number of stars
        max_repos: Maximum number of repositories to return PER KEYWORD

    Returns:
        List of repository dictionaries (max_repos * number_of_keywords total)

    Example:
        keywords="proxy,vpn", max_repos=60 will return up to 120 repositories
        (60 for "proxy" + 60 for "vpn", minus any duplicates)
    """
    import logging
    from config import ScraperConfig

    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    config = ScraperConfig()
    searcher = GitHubSearcher(config)

    # Split keywords and search for each
    keyword_list = [k.strip() for k in keywords.split(',')]
    logger.info(f"🔍 GITHUB SEARCH FLOW: Starting search for {len(keyword_list)} keywords: {keyword_list}")
    logger.info(f"📊 GITHUB SEARCH FLOW: Search parameters: min_stars={min_stars}, max_repos={max_repos} PER KEYWORD")
    logger.info(f"🎯 GITHUB SEARCH FLOW: Expected maximum repositories: {len(keyword_list)} × {max_repos} = {len(keyword_list) * max_repos}")

    all_repositories = []

    for i, keyword in enumerate(keyword_list, 1):
        logger.info(f"🔎 GITHUB SEARCH FLOW: [{i}/{len(keyword_list)}] Processing keyword: '{keyword}'")
        logger.info(f"🎯 GITHUB SEARCH FLOW: Target for '{keyword}': {max_repos} repositories")

        # Each keyword gets exactly max_repos repositories
        # Use GitHub API limit (100) as the search limit, then trim to max_repos
        search_limit = min(100, max_repos)
        logger.info(f"🌐 GITHUB SEARCH FLOW: API search limit for '{keyword}': {search_limit}")

        repos = searcher.search_repositories(keyword, min_stars, search_limit)
        logger.info(f"📦 GITHUB SEARCH FLOW: Retrieved {len(repos)} repositories from GitHub API for '{keyword}'")

        # Limit to exactly max_repos for this keyword
        keyword_repos = repos[:max_repos]
        logger.info(f"✅ GITHUB SEARCH FLOW: Selected {len(keyword_repos)} repositories for '{keyword}' (max: {max_repos})")

        # Add keyword info to each repo for tracking
        for repo in keyword_repos:
            repo['search_keyword'] = keyword

        all_repositories.extend(keyword_repos)
        logger.info(f"📈 GITHUB SEARCH FLOW: Total repositories accumulated: {len(all_repositories)}")
        logger.info(f"📊 GITHUB SEARCH FLOW: Progress: {i}/{len(keyword_list)} keywords processed")

    logger.info(f"🔄 GITHUB SEARCH FLOW: Starting deduplication process...")
    logger.info(f"📊 GITHUB SEARCH FLOW: Total repositories before deduplication: {len(all_repositories)}")

    # Remove duplicates based on URL
    seen_urls = set()
    unique_repos = []
    duplicate_count = 0

    for repo in all_repositories:
        if repo['url'] not in seen_urls:
            seen_urls.add(repo['url'])
            unique_repos.append(repo)
        else:
            duplicate_count += 1
            logger.debug(f"🔄 GITHUB SEARCH FLOW: Duplicate found: {repo['url']} (keyword: {repo.get('search_keyword', 'unknown')})")

    logger.info(f"🗑️ GITHUB SEARCH FLOW: Removed {duplicate_count} duplicate repositories")
    logger.info(f"✨ GITHUB SEARCH FLOW: Unique repositories found: {len(unique_repos)}")

    # Sort by stars (descending) to get the best repositories
    unique_repos.sort(key=lambda x: x.get('stars', 0), reverse=True)
    logger.info(f"⭐ GITHUB SEARCH FLOW: Repositories sorted by stars (highest first)")

    # NO FINAL LIMITING - we want all unique repos from all keywords
    final_repos = unique_repos

    logger.info(f"🎯 GITHUB SEARCH FLOW: Final result: {len(final_repos)} repositories selected")
    logger.info(f"📊 GITHUB SEARCH FLOW: Expected maximum: {len(keyword_list)} keywords × {max_repos} repos = {len(keyword_list) * max_repos} repos")
    logger.info(f"✅ GITHUB SEARCH FLOW: Per-keyword logic working correctly: {len(final_repos)} ≤ {len(keyword_list) * max_repos}")

    # Log summary by keyword
    keyword_summary = {}
    for repo in final_repos:
        keyword = repo.get('search_keyword', 'unknown')
        keyword_summary[keyword] = keyword_summary.get(keyword, 0) + 1

    logger.info("📋 Final selection by keyword:")
    for keyword, count in keyword_summary.items():
        logger.info(f"   '{keyword}': {count} repositories")

    return final_repos
</file>

<file path="src/llm_rate_limiter.py">
import time
import uuid
import redis
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class LLMRateLimiter:
    """
    A distributed rate limiter for LLM API calls using Redis sliding window log.
    Enforces rate limits globally across all Cloud Run instances.
    Uses Redis Sorted Sets for efficient distributed rate limiting.
    """

    def __init__(self, redis_client: Optional[redis.Redis] = None,
                 rate_limit_mb_per_min: float = 12.0,
                 rate_limit_requests_per_min: int = 1000,
                 window_size_seconds: int = 60):
        """
        Initialize the distributed rate limiter.

        Args:
            redis_client: Redis client instance for distributed state
            rate_limit_mb_per_min: Maximum MB that can be processed per minute
            rate_limit_requests_per_min: Maximum requests per minute
            window_size_seconds: Time window for rate limiting (default: 60 seconds)
        """
        self.redis_client = redis_client
        self.rate_limit_mb_per_min = rate_limit_mb_per_min
        self.rate_limit_requests_per_min = rate_limit_requests_per_min
        self.window_size_seconds = window_size_seconds

        # Redis keys for rate limiting
        self.mb_log_key = "llm_rate_limit_mb_log"
        self.request_log_key = "llm_rate_limit_request_log"

        # Fallback to file-based for local development
        self.use_redis = redis_client is not None
        if not self.use_redis:
            logger.warning("Redis client not provided, falling back to local file-based rate limiting")
            self._init_file_fallback()

    def _init_file_fallback(self):
        """Initialize file-based fallback for local development."""
        from pathlib import Path
        import json

        self.state_file = Path('temp_rate_limiter_state.json')

        # Initialize state file if it doesn't exist
        if not self.state_file.exists():
            self._write_state({
                'total_mb': 0.0,
                'last_reset': time.time()
            })

    def _read_state(self):
        """Read state from file with error handling (fallback mode)"""
        import json
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass

        # Return default state if file doesn't exist or is corrupted
        return {
            'total_mb': 0.0,
            'last_reset': time.time()
        }

    def _write_state(self, state):
        """Write state to file with error handling (fallback mode)"""
        import json
        try:
            # Ensure directory exists
            self.state_file.parent.mkdir(parents=True, exist_ok=True)

            # Write atomically by writing to temp file first
            temp_file = self.state_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(state, f)

            # Atomic rename
            temp_file.replace(self.state_file)
        except IOError:
            # If file operations fail, continue without crashing
            pass

    def acquire_slot(self, file_size_mb: float = 0.0) -> bool:
        """
        Acquire a slot for processing a file of given size.
        Uses Redis sliding window log for distributed rate limiting.

        Args:
            file_size_mb: Size of the file to process in MB

        Returns:
            bool: True if slot acquired, False if rate limited
        """
        if self.use_redis:
            return self._acquire_slot_redis(file_size_mb)
        else:
            return self._acquire_slot_fallback(file_size_mb)

    def _acquire_slot_redis(self, file_size_mb: float) -> bool:
        """Redis-based sliding window rate limiting."""
        try:
            current_time = time.time()
            window_start = current_time - self.window_size_seconds

            # Generate unique request ID
            request_id = f"{current_time}:{uuid.uuid4()}"

            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()

            # Remove old entries from both logs
            pipe.zremrangebyscore(self.mb_log_key, 0, window_start)
            pipe.zremrangebyscore(self.request_log_key, 0, window_start)

            # Get current usage
            pipe.zcard(self.request_log_key)  # Request count

            # Execute pipeline to get current state
            results = pipe.execute()
            current_requests = results[2]

            # Check request rate limit
            if current_requests >= self.rate_limit_requests_per_min:
                logger.debug(f"Request rate limit exceeded: {current_requests}/{self.rate_limit_requests_per_min}")
                return False

            # Check MB rate limit if file size specified
            if file_size_mb > 0:
                # Get current MB usage by summing scores in the sorted set
                mb_entries = self.redis_client.zrangebyscore(
                    self.mb_log_key, window_start, current_time, withscores=True
                )
                current_mb = sum(score for _, score in mb_entries)

                if current_mb + file_size_mb > self.rate_limit_mb_per_min:
                    logger.debug(f"MB rate limit exceeded: {current_mb + file_size_mb}/{self.rate_limit_mb_per_min}")
                    return False

                # Add to MB log with file size as score
                self.redis_client.zadd(self.mb_log_key, {request_id: file_size_mb})

            # Add to request log
            self.redis_client.zadd(self.request_log_key, {request_id: current_time})

            # Set expiration on keys to prevent memory leaks
            self.redis_client.expire(self.mb_log_key, self.window_size_seconds * 2)
            self.redis_client.expire(self.request_log_key, self.window_size_seconds * 2)

            return True

        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Fallback to allowing the request if Redis fails
            return True

    def _acquire_slot_fallback(self, file_size_mb: float) -> bool:
        """File-based fallback rate limiting for local development."""
        current_time = time.time()

        # Read current state
        state = self._read_state()

        # Check if a minute has passed since last reset
        if current_time - state['last_reset'] >= self.window_size_seconds:
            state['total_mb'] = 0.0
            state['last_reset'] = current_time

        # Check if adding this file would exceed the limit
        if state['total_mb'] + file_size_mb <= self.rate_limit_mb_per_min:
            # We can process this file
            state['total_mb'] += file_size_mb
            self._write_state(state)
            return True

        return False

    def wait_for_slot(self, file_size_mb: float = 0.0, max_wait_seconds: int = 300):
        """
        Wait for a slot to become available, with timeout.

        Args:
            file_size_mb: Size of the file to process in MB
            max_wait_seconds: Maximum time to wait for a slot
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_seconds:
            if self.acquire_slot(file_size_mb):
                return True

            # Sleep with exponential backoff, max 5 seconds
            wait_time = min(1.0 + (time.time() - start_time) * 0.1, 5.0)
            time.sleep(wait_time)

        raise TimeoutError(f"Could not acquire rate limit slot within {max_wait_seconds} seconds")

    def get_current_usage(self) -> dict:
        """Get current rate limit usage statistics."""
        if self.use_redis:
            try:
                current_time = time.time()
                window_start = current_time - self.window_size_seconds

                # Clean old entries and get current counts
                self.redis_client.zremrangebyscore(self.mb_log_key, 0, window_start)
                self.redis_client.zremrangebyscore(self.request_log_key, 0, window_start)

                # Get current usage
                current_requests = self.redis_client.zcard(self.request_log_key)

                # Calculate current MB usage
                mb_entries = self.redis_client.zrangebyscore(
                    self.mb_log_key, window_start, current_time, withscores=True
                )
                current_mb = sum(score for _, score in mb_entries)

                return {
                    "requests": current_requests,
                    "requests_limit": self.rate_limit_requests_per_min,
                    "mb_used": round(current_mb, 2),
                    "mb_limit": self.rate_limit_mb_per_min,
                    "window_seconds": self.window_size_seconds,
                    "requests_remaining": max(0, self.rate_limit_requests_per_min - current_requests),
                    "mb_remaining": max(0, self.rate_limit_mb_per_min - current_mb)
                }
            except Exception as e:
                logger.error(f"Error getting usage stats: {e}")
                return {"error": str(e)}
        else:
            # Fallback mode
            state = self._read_state()
            current_time = time.time()

            # Reset if window expired
            if current_time - state['last_reset'] >= self.window_size_seconds:
                current_mb = 0.0
            else:
                current_mb = state['total_mb']

            return {
                "mb_used": round(current_mb, 2),
                "mb_limit": self.rate_limit_mb_per_min,
                "mb_remaining": max(0, self.rate_limit_mb_per_min - current_mb),
                "mode": "fallback"
            }

    def reset_limits(self):
        """Reset all rate limits (for testing purposes)."""
        if self.use_redis:
            try:
                self.redis_client.delete(self.mb_log_key, self.request_log_key)
                logger.info("Redis rate limit logs cleared")
            except Exception as e:
                logger.error(f"Error resetting Redis limits: {e}")
        else:
            # Reset file-based state
            self._write_state({
                'total_mb': 0.0,
                'last_reset': time.time()
            })

    def cleanup(self):
        """Clean up temporary files and optionally Redis keys."""
        if not self.use_redis:
            try:
                if hasattr(self, 'state_file') and self.state_file.exists():
                    self.state_file.unlink()
                    logger.debug("Cleaned up rate limiter state file")
            except Exception as e:
                logger.debug(f"Error cleaning up state file: {e}")
        # Note: We don't clean up Redis keys as they may be shared across instances
</file>

<file path="src/redis_queue.py">
"""
Redis-based distributed queue implementation.
Replaces in-memory JoinableQueue with Redis-backed queues for cloud deployment.
"""

import json
import time
import redis
import logging
from typing import Any, Dict, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class QueueItem:
    """Base class for queue items with metadata."""
    id: str
    created_at: str
    data: Dict[str, Any]
    retries: int = 0
    max_retries: int = 3

    def to_json(self) -> str:
        """Convert to JSON string for Redis storage."""
        return json.dumps(asdict(self))
    
    @classmethod
    def from_json(cls, json_str: str) -> 'QueueItem':
        """Create from JSON string."""
        data = json.loads(json_str)
        return cls(**data)

class RepositoryQueueItem(QueueItem):
    """Queue item for repository processing tasks."""

    def __init__(self, repository_url: str, repository_name: str, job_id: str,
                 item_id: Optional[str] = None, retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"repo_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "repository_url": repository_url,
                "repository_name": repository_name,
                "job_id": job_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.repository_url = repository_url
        self.repository_name = repository_name
        self.job_id = job_id

class FileQueueItem(QueueItem):
    """Queue item for LLM processing tasks."""

    def __init__(self, file_path: str, repository_name: str, job_id: str,
                 chunk_id: Optional[str] = None, item_id: Optional[str] = None,
                 retries: int = 0, max_retries: int = 3):
        super().__init__(
            id=item_id or f"file_{int(time.time() * 1000)}",
            created_at=datetime.now().isoformat(),
            data={
                "file_path": file_path,
                "repository_name": repository_name,
                "job_id": job_id,
                "chunk_id": chunk_id
            },
            retries=retries,
            max_retries=max_retries
        )
        self.file_path = file_path
        self.repository_name = repository_name
        self.job_id = job_id
        self.chunk_id = chunk_id

class RedisQueue:
    """Redis-backed distributed queue implementation."""
    
    def __init__(self, redis_url: str, queue_name: str, 
                 processing_timeout: int = 300):
        """
        Initialize Redis queue.
        
        Args:
            redis_url: Redis connection URL
            queue_name: Name of the queue
            processing_timeout: Timeout for processing items (seconds)
        """
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.queue_name = queue_name
        self.processing_queue = f"{queue_name}:processing"
        self.failed_queue = f"{queue_name}:failed"
        self.processing_timeout = processing_timeout
        
        # Test connection
        try:
            self.redis_client.ping()
            logger.info(f"Connected to Redis queue: {queue_name}")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def put(self, item: QueueItem) -> None:
        """Add item to queue."""
        try:
            self.redis_client.lpush(self.queue_name, item.to_json())
            logger.debug(f"Added item {item.id} to queue {self.queue_name}")
        except Exception as e:
            logger.error(f"Failed to add item to queue: {e}")
            raise
    
    def get(self, timeout: Optional[int] = None) -> Optional[QueueItem]:
        """
        Get item from queue (blocking).
        
        Args:
            timeout: Timeout in seconds (None for indefinite)
            
        Returns:
            QueueItem or None if timeout
        """
        try:
            # Use BRPOPLPUSH for atomic move to processing queue
            result = self.redis_client.brpoplpush(
                self.queue_name, 
                self.processing_queue, 
                timeout=timeout or 0
            )
            
            if result:
                item = QueueItem.from_json(result)
                # Add processing timestamp
                self.redis_client.hset(
                    f"{self.processing_queue}:meta:{item.id}",
                    "started_at", 
                    datetime.now().isoformat()
                )
                logger.debug(f"Retrieved item {item.id} from queue {self.queue_name}")
                return item
            return None
            
        except Exception as e:
            logger.error(f"Failed to get item from queue: {e}")
            raise
    
    def task_done(self, item: QueueItem) -> None:
        """Mark task as completed."""
        try:
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            # Remove metadata
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            logger.debug(f"Marked item {item.id} as done")
        except Exception as e:
            logger.error(f"Failed to mark task as done: {e}")
            raise
    
    def task_failed(self, item: QueueItem, error: str) -> None:
        """Mark task as failed and handle retry logic."""
        try:
            item.retries += 1
            
            if item.retries < item.max_retries:
                # Retry: put back in main queue
                self.redis_client.lpush(self.queue_name, item.to_json())
                logger.warning(f"Retrying item {item.id} (attempt {item.retries})")
            else:
                # Max retries reached: move to failed queue
                failed_item = {
                    "item": asdict(item),
                    "error": error,
                    "failed_at": datetime.now().isoformat()
                }
                self.redis_client.lpush(self.failed_queue, json.dumps(failed_item))
                logger.error(f"Item {item.id} failed permanently: {error}")
            
            # Remove from processing queue
            self.redis_client.lrem(self.processing_queue, 1, item.to_json())
            self.redis_client.delete(f"{self.processing_queue}:meta:{item.id}")
            
        except Exception as e:
            logger.error(f"Failed to handle task failure: {e}")
            raise
    
    def size(self) -> int:
        """Get queue size."""
        return self.redis_client.llen(self.queue_name)
    
    def processing_size(self) -> int:
        """Get processing queue size."""
        return self.redis_client.llen(self.processing_queue)
    
    def failed_size(self) -> int:
        """Get failed queue size."""
        return self.redis_client.llen(self.failed_queue)
    
    def cleanup_stale_items(self) -> int:
        """Clean up items that have been processing too long."""
        try:
            stale_count = 0
            processing_items = self.redis_client.lrange(self.processing_queue, 0, -1)
            
            for item_json in processing_items:
                item = QueueItem.from_json(item_json)
                meta_key = f"{self.processing_queue}:meta:{item.id}"
                started_at_str = self.redis_client.hget(meta_key, "started_at")
                
                if started_at_str:
                    started_at = datetime.fromisoformat(started_at_str)
                    if datetime.now() - started_at > timedelta(seconds=self.processing_timeout):
                        # Item is stale, move back to main queue
                        self.redis_client.lrem(self.processing_queue, 1, item_json)
                        self.redis_client.delete(meta_key)
                        self.redis_client.lpush(self.queue_name, item_json)
                        stale_count += 1
                        logger.warning(f"Moved stale item {item.id} back to queue")
            
            return stale_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup stale items: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get queue statistics."""
        return {
            "pending": self.size(),
            "processing": self.processing_size(),
            "failed": self.failed_size()
        }

class QueueManager:
    """Manages multiple Redis queues for the application."""
    
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.queues: Dict[str, RedisQueue] = {}
        # Create a shared Redis client for rate limiting and other operations
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
    
    def get_queue(self, queue_name: str) -> RedisQueue:
        """Get or create a queue."""
        if queue_name not in self.queues:
            self.queues[queue_name] = RedisQueue(self.redis_url, queue_name)
        return self.queues[queue_name]
    
    def get_repository_queue(self) -> RedisQueue:
        """Get the repository processing queue."""
        return self.get_queue("repositories")
    
    def get_file_queue(self) -> RedisQueue:
        """Get the file processing queue."""
        return self.get_queue("files")
    
    def cleanup_all_stale_items(self) -> Dict[str, int]:
        """Cleanup stale items in all queues."""
        results = {}
        for name, queue in self.queues.items():
            results[name] = queue.cleanup_stale_items()
        return results
    
    def get_all_stats(self) -> Dict[str, Dict[str, int]]:
        """Get statistics for all queues."""
        stats = {}
        for name, queue in self.queues.items():
            stats[name] = queue.get_stats()
        return stats
</file>

<file path="src/supabase_client.py">
"""
Supabase client integration for database and storage operations.
Handles PostgreSQL database operations and S3-compatible storage.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from supabase import create_client, Client
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class JobRecord:
    """Database record for analysis jobs."""
    id: str
    keywords: str
    min_stars: int
    max_repos: int
    status: str  # pending, running, completed, failed
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    error_message: Optional[str] = None
    analysis_file_url: Optional[str] = None
    total_repositories: Optional[int] = None
    custom_prompt: Optional[str] = None
    debug: bool = False

@dataclass
class RepositoryRecord:
    """Database record for repository information."""
    id: str
    job_id: str
    name: str
    url: str
    stars: int
    file_size_mb: float
    repomix_file_url: Optional[str] = None
    processed_at: Optional[str] = None
    analysis_status: str = "pending"  # pending, processing, completed, failed
    error_message: Optional[str] = None
    created_at: Optional[str] = None

@dataclass
class AnalysisRecord:
    """Database record for analysis results."""
    id: str
    repository_id: str
    job_id: str
    worker_id: str
    processed_at: str
    analysis_content: str
    analysis_length: int
    chunk_id: Optional[str] = None

class SupabaseClient:
    """Supabase client for database and storage operations."""
    
    def __init__(self, url: str, key: str):
        """
        Initialize Supabase client.
        
        Args:
            url: Supabase project URL
            key: Supabase API key (anon or service role)
        """
        self.client: Client = create_client(url, key)
        self.url = url
        self.key = key
        
        # Test connection
        try:
            # Simple query to test connection
            result = self.client.table('jobs').select('id').limit(1).execute()
            logger.info("Connected to Supabase successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Supabase: {e}")
            raise
    
    # Job Management
    def create_job(self, job: JobRecord) -> JobRecord:
        """Create a new analysis job."""
        try:
            result = self.client.table('jobs').insert(asdict(job)).execute()
            logger.info(f"Created job {job.id}")
            return JobRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def get_job(self, job_id: str) -> Optional[JobRecord]:
        """Get job by ID."""
        try:
            result = self.client.table('jobs').select('*').eq('id', job_id).execute()
            if result.data:
                return JobRecord(**result.data[0])
            return None
        except Exception as e:
            logger.error(f"Failed to get job {job_id}: {e}")
            raise
    
    def update_job_status(self, job_id: str, status: str, 
                         error_message: Optional[str] = None,
                         analysis_file_url: Optional[str] = None,
                         total_repositories: Optional[int] = None) -> None:
        """Update job status and related fields."""
        try:
            update_data = {"status": status}
            
            if status == "running" and not self.get_job(job_id).started_at:
                update_data["started_at"] = datetime.now().isoformat()
            elif status in ["completed", "failed"]:
                update_data["completed_at"] = datetime.now().isoformat()
            
            if error_message:
                update_data["error_message"] = error_message
            if analysis_file_url:
                update_data["analysis_file_url"] = analysis_file_url
            if total_repositories is not None:
                update_data["total_repositories"] = total_repositories
            
            self.client.table('jobs').update(update_data).eq('id', job_id).execute()
            logger.debug(f"Updated job {job_id} status to {status}")
            
        except Exception as e:
            logger.error(f"Failed to update job status: {e}")
            raise
    
    def list_jobs(self, limit: int = 50, offset: int = 0) -> List[JobRecord]:
        """List jobs with pagination."""
        try:
            result = self.client.table('jobs')\
                .select('*')\
                .order('created_at', desc=True)\
                .range(offset, offset + limit - 1)\
                .execute()
            
            return [JobRecord(**job) for job in result.data]
        except Exception as e:
            logger.error(f"Failed to list jobs: {e}")
            raise
    
    # Repository Management
    def create_repository(self, repo: RepositoryRecord) -> RepositoryRecord:
        """Create a repository record."""
        try:
            result = self.client.table('repositories').insert(asdict(repo)).execute()
            logger.debug(f"Created repository record {repo.id}")
            return RepositoryRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create repository: {e}")
            raise
    
    def update_repository_status(self, repo_id: str, status: str,
                               repomix_file_url: Optional[str] = None,
                               error_message: Optional[str] = None) -> None:
        """Update repository processing status."""
        try:
            update_data = {
                "analysis_status": status,
                "processed_at": datetime.now().isoformat()
            }
            
            if repomix_file_url:
                update_data["repomix_file_url"] = repomix_file_url
            if error_message:
                update_data["error_message"] = error_message
            
            self.client.table('repositories').update(update_data).eq('id', repo_id).execute()
            logger.debug(f"Updated repository {repo_id} status to {status}")
            
        except Exception as e:
            logger.error(f"Failed to update repository status: {e}")
            raise
    
    def get_repositories_by_job(self, job_id: str) -> List[RepositoryRecord]:
        """Get all repositories for a job."""
        try:
            result = self.client.table('repositories')\
                .select('*')\
                .eq('job_id', job_id)\
                .order('created_at')\
                .execute()
            
            return [RepositoryRecord(**repo) for repo in result.data]
        except Exception as e:
            logger.error(f"Failed to get repositories for job {job_id}: {e}")
            raise
    
    # Analysis Management
    def create_analysis(self, analysis: AnalysisRecord) -> AnalysisRecord:
        """Create an analysis record."""
        try:
            result = self.client.table('analyses').insert(asdict(analysis)).execute()
            logger.debug(f"Created analysis record {analysis.id}")
            return AnalysisRecord(**result.data[0])
        except Exception as e:
            logger.error(f"Failed to create analysis: {e}")
            raise
    
    def get_analyses_by_job(self, job_id: str) -> List[AnalysisRecord]:
        """Get all analyses for a job."""
        try:
            result = self.client.table('analyses')\
                .select('*')\
                .eq('job_id', job_id)\
                .order('processed_at')\
                .execute()
            
            return [AnalysisRecord(**analysis) for analysis in result.data]
        except Exception as e:
            logger.error(f"Failed to get analyses for job {job_id}: {e}")
            raise
    
    # Storage Operations
    def upload_file(self, bucket: str, file_path: str, file_content: bytes,
                   content_type: str = "application/octet-stream") -> str:
        """
        Upload file to Supabase storage.
        
        Returns:
            Public URL of uploaded file
        """
        try:
            # Upload file
            result = self.client.storage.from_(bucket).upload(
                file_path, 
                file_content,
                file_options={"content-type": content_type}
            )
            
            if result.error:
                raise Exception(f"Upload failed: {result.error}")
            
            # Get public URL
            public_url = self.client.storage.from_(bucket).get_public_url(file_path)
            logger.debug(f"Uploaded file to {public_url}")
            return public_url
            
        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            raise
    
    def download_file(self, bucket: str, file_path: str) -> bytes:
        """Download file from Supabase storage."""
        try:
            result = self.client.storage.from_(bucket).download(file_path)
            if result.error:
                raise Exception(f"Download failed: {result.error}")
            return result.data
        except Exception as e:
            logger.error(f"Failed to download file {file_path}: {e}")
            raise
    
    def delete_file(self, bucket: str, file_path: str) -> None:
        """Delete file from Supabase storage."""
        try:
            result = self.client.storage.from_(bucket).remove([file_path])
            if result.error:
                raise Exception(f"Delete failed: {result.error}")
            logger.debug(f"Deleted file {file_path}")
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            raise
    
    def list_files(self, bucket: str, folder: str = "") -> List[Dict[str, Any]]:
        """List files in a bucket/folder."""
        try:
            result = self.client.storage.from_(bucket).list(folder)
            if result.error:
                raise Exception(f"List failed: {result.error}")
            return result.data
        except Exception as e:
            logger.error(f"Failed to list files in {bucket}/{folder}: {e}")
            raise

def create_supabase_client() -> SupabaseClient:
    """Create Supabase client from environment variables."""
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables are required")
    
    return SupabaseClient(url, key)
</file>

<file path="src/utils.py">
import os


def get_file_size_mb(file_path):
    """
    Calculate file size in megabytes.

    Args:
        file_path (str): Path to the file

    Returns:
        float: File size in megabytes
    """
    size_bytes = os.path.getsize(file_path)
    size_mb = size_bytes / (1024 * 1024)
    return size_mb


def chunk_and_retain_file(file_path, max_size_mb=3, max_chunks=3):
    """
    Chunk a file into smaller parts if it exceeds max_size_mb.
    Only retain the first max_chunks parts and delete the original file.

    Args:
        file_path (str): Path to the file to chunk
        max_size_mb (int): Maximum size in MB before chunking (default: 3)
        max_chunks (int): Maximum number of chunks to retain (default: 3)

    Returns:
        list: List of chunk file paths if file was chunked, or [file_path] if not chunked
    """
    import logging
    logger = logging.getLogger(__name__)

    # Check if file exists
    if not os.path.exists(file_path):
        logger.warning(f"📏 CHUNKING FLOW: File not found: {file_path}")
        return []

    # Check if file needs chunking
    file_size_mb = get_file_size_mb(file_path)
    logger.info(f"📏 CHUNKING FLOW: File size check: {os.path.basename(file_path)} = {file_size_mb:.2f} MB")

    if file_size_mb <= max_size_mb:
        logger.info(f"✅ CHUNKING FLOW: File within limit ({file_size_mb:.2f} MB ≤ {max_size_mb} MB) - no chunking needed")
        return [file_path]

    logger.info(f"🔄 CHUNKING FLOW: File exceeds limit ({file_size_mb:.2f} MB > {max_size_mb} MB) - starting chunking")
    logger.info(f"📊 CHUNKING FLOW: Chunking parameters: {max_size_mb} MB per chunk, max {max_chunks} chunks")

    # Calculate chunk size in bytes
    chunk_size_bytes = max_size_mb * 1024 * 1024

    # Read file in binary mode and create chunks
    chunk_paths = []
    base_name = file_path

    try:
        with open(file_path, 'rb') as f:
            chunk_num = 0
            while True:
                chunk_data = f.read(chunk_size_bytes)
                if not chunk_data:
                    break

                # Only create chunks up to max_chunks
                if chunk_num < max_chunks:
                    chunk_path = f"{base_name}.chunk_{chunk_num}"
                    logger.info(f"📝 CHUNKING FLOW: Creating chunk {chunk_num + 1}/{max_chunks}: {os.path.basename(chunk_path)}")

                    with open(chunk_path, 'w', encoding='utf-8') as chunk_file:
                        # Convert binary data to string for UTF-8 encoding
                        try:
                            chunk_text = chunk_data.decode('utf-8')
                        except UnicodeDecodeError:
                            # If binary data can't be decoded, use latin-1 as fallback
                            chunk_text = chunk_data.decode('latin-1')
                        chunk_file.write(chunk_text)

                    chunk_size_actual = len(chunk_data) / (1024 * 1024)
                    logger.info(f"✅ CHUNKING FLOW: Chunk {chunk_num + 1} created: {chunk_size_actual:.2f} MB")
                    chunk_paths.append(chunk_path)
                else:
                    logger.info(f"⏭️ CHUNKING FLOW: Skipping chunk {chunk_num + 1} (exceeds max_chunks limit of {max_chunks})")

                chunk_num += 1

        # Delete the original file
        logger.info(f"🗑️ CHUNKING FLOW: Deleting original file: {os.path.basename(file_path)}")
        os.remove(file_path)

        logger.info(f"🎯 CHUNKING FLOW: Chunking complete: {len(chunk_paths)} chunks created from {file_size_mb:.2f} MB file")
        logger.info(f"📋 CHUNKING FLOW: Chunk files: {[os.path.basename(f) for f in chunk_paths]}")

        return chunk_paths

    except FileNotFoundError:
        # If original file doesn't exist, return empty list
        return []
    except Exception as e:
        # If any error occurs, clean up partial chunks and re-raise
        for chunk_path in chunk_paths:
            try:
                os.remove(chunk_path)
            except FileNotFoundError:
                pass
        raise e
</file>

<file path="main.py">
#!/usr/bin/env python3
"""
Repository Research Tool - Main Entry Point

Usage:
    python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
    python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze this code for AI patterns"
"""

import argparse
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_custom_prompt_template():
    """Create a template for custom prompts with placeholders"""
    return """CUSTOM REPOSITORY ANALYSIS

Repository: {repo_name}
File size: {file_size_mb:.2f}MB

[Your custom analysis instructions here]

Code content:
{content}
"""

def main():
    """Main entry point for the repository research tool"""
    parser = argparse.ArgumentParser(
        description="Repository Research Tool - Analyze GitHub repositories with AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --keywords "cursor rules,code prompts" --min-stars 30 --max-repos 15
  python main.py --keywords "ai agents" --min-stars 50 --max-repos 10 --custom-prompt "Analyze for AI patterns"
  python main.py --keywords "web frameworks" --min-stars 100 --max-repos 5 --debug

Custom Prompt Template:
  Use {repo_name}, {file_size_mb}, and {content} as placeholders in your custom prompt.
        """
    )
    
    # Required arguments
    parser.add_argument(
        '--keywords',
        required=True,
        help='Comma-separated list of keywords to search for (e.g., "cursor rules,code prompts")'
    )
    
    parser.add_argument(
        '--min-stars',
        type=int,
        default=30,
        help='Minimum number of stars for repositories (default: 30)'
    )
    
    parser.add_argument(
        '--max-repos',
        type=int,
        default=15,
        help='Maximum number of repositories per keyword (default: 15)'
    )
    
    # Optional arguments
    parser.add_argument(
        '--custom-prompt',
        help='Custom LLM analysis prompt. Use {repo_name}, {file_size_mb}, and {content} as placeholders.'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode with verbose logging'
    )
    
    parser.add_argument(
        '--output-dir',
        help='Custom output directory name (default: auto-generated timestamp)'
    )
    
    args = parser.parse_args()
    
    # Parse keywords
    keywords = [k.strip() for k in args.keywords.split(',')]
    
    print("REPOSITORY RESEARCH TOOL")
    print("=" * 60)
    print(f"Configuration:")
    print(f"   Keywords: {keywords}")
    print(f"   Min stars: {args.min_stars}")
    print(f"   Max repos per keyword: {args.max_repos}")
    print(f"   Expected total repos: ~{len(keywords) * args.max_repos}")
    print(f"   Custom prompt: {'Yes' if args.custom_prompt else 'No (using default)'}")
    print(f"   Debug mode: {'Yes' if args.debug else 'No'}")
    
    try:
        # Import and configure
        from config import ScraperConfig
        from pipeline import RepositoryPipeline
        from github_search import GitHubSearcher
        from sentry_config import init_sentry

        # Initialize Sentry monitoring
        init_sentry()

        # Load configuration
        config = ScraperConfig()
        
        # Set custom prompt if provided
        if args.custom_prompt:
            config.CUSTOM_LLM_PROMPT = args.custom_prompt
            print(f"Custom prompt configured")
        
        # Validate API key
        if not config.LLM_API_KEY:
            print("Error: LLM_API_KEY not configured in .env file")
            print("   Please add your Gemini API key to .env file")
            return 1

        print(f"Configuration loaded:")
        print(f"   API key: Configured")
        print(f"   Repomix workers: {config.REPOMIX_WORKERS}")
        print(f"   LLM workers: {config.LLM_WORKERS}")
        
        # Search for repositories
        print(f"\nSearching for repositories (sorted by last updated)...")
        searcher = GitHubSearcher(config)
        
        all_repositories = []
        for keyword in keywords:
            print(f"   Searching for: '{keyword}'")
            repos = searcher.search_repositories(
                keyword, 
                min_stars=args.min_stars, 
                max_repos=args.max_repos
            )
            print(f"   Found {len(repos)} repositories for '{keyword}'")
            
            # Show first few repos
            for i, repo in enumerate(repos[:3]):
                updated_at = repo.get('updated_at', 'unknown')
                print(f"      {i+1}. {repo.get('full_name', 'unknown')} ({repo.get('stargazers_count', 0)} stars, updated: {updated_at[:10]})")
            
            if len(repos) > 3:
                print(f"      ... and {len(repos) - 3} more repositories")
            
            all_repositories.extend(repos)
        
        print(f"\nTotal repositories to process: {len(all_repositories)}")

        if len(all_repositories) == 0:
            print("No repositories found - check keywords and min-stars filter")
            return 1

        # Initialize pipeline
        print(f"\nInitializing Pipeline...")
        pipeline = RepositoryPipeline(config=config)
        
        print(f"Pipeline initialized")
        print(f"   Output directory: {pipeline.output_dir}")
        
        # Run the pipeline
        print(f"\nRunning Complete Pipeline...")
        print(f"   Processing {len(all_repositories)} repositories")
        print(f"   Using {config.REPOMIX_WORKERS} repomix workers")
        print(f"   Using {config.LLM_WORKERS} LLM workers")
        print(f"   Expected time: ~{len(all_repositories) * 1.5} minutes")
        print("=" * 60)

        # Run the pipeline
        keywords_str = ','.join(keywords)
        pipeline.run(
            repositories=all_repositories,
            keywords=keywords_str,
            min_stars=args.min_stars,
            max_repos=args.max_repos,
            custom_prompt=args.custom_prompt
        )

        print("=" * 60)
        print(f"ANALYSIS COMPLETED!")
        
        # Show results
        analysis_file = pipeline.output_dir / "analysis.json"
        if analysis_file.exists():
            file_size = analysis_file.stat().st_size
            print(f"Results saved to: {pipeline.output_dir}")
            print(f"   Main analysis: analysis.json ({file_size:,} bytes)")
            print(f"   Repomix files: repomixes/ directory")
            print(f"   Error tracking: sentry_analysis.json")
        else:
            print(f"Analysis file not found - check for errors")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\nAnalysis interrupted by user")
        return 0

    except Exception as e:
        print(f"\nAnalysis failed: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
</file>

<file path="src/config.py">
import os
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    # Look for .env file in project root
    env_path = Path(__file__).parent.parent / '.env'
    load_dotenv(env_path)
    print(f"Loaded environment variables from {env_path}")
except ImportError:
    print("python-dotenv not installed, using system environment variables")
except Exception as e:
    print(f"Could not load .env file: {e}")

class ScraperConfig:
    # GitHub Configuration
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
    MAX_REPOS_PER_KEYWORD = int(os.getenv('MAX_REPOS_PER_KEYWORD', '20'))
    MIN_STARS = int(os.getenv('MIN_STARS', '30'))

    # Repomix Configuration
    REPOMIX_WORKERS = int(os.getenv('REPOMIX_WORKERS', '15'))
    FILE_INCLUDES = "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"

    # Chunking Configuration
    MAX_FILE_SIZE_MB = 3
    MAX_CHUNKS_TO_RETAIN = 3

    # LLM Configuration
    LLM_API_KEY = os.getenv('LLM_API_KEY')
    LLM_BASE_URL = os.getenv('LLM_BASE_URL', 'https://generativelanguage.googleapis.com/v1beta')
    LLM_MODEL = os.getenv('LLM_MODEL', 'gemini-2.0-flash')
    LLM_WORKERS = int(os.getenv('LLM_WORKERS', '4'))
    LLM_RATE_LIMIT_MB_PER_MIN = 12

    # Output Configuration
    OUTPUT_BASE = os.getenv('OUTPUT_BASE', 'output')

    # Cloud Configuration - Always use cloud services (Redis + Supabase)
    # Local development uses Docker containers, production uses managed services

    # Redis Configuration
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    REDIS_QUEUE_TIMEOUT = int(os.getenv('REDIS_QUEUE_TIMEOUT', '300'))

    # Supabase Configuration
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY')  # Service role key
    SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')  # Anonymous key

    # Storage Configuration
    STORAGE_BUCKET_REPOMIXES = os.getenv('STORAGE_BUCKET_REPOMIXES', 'repomixes')
    STORAGE_BUCKET_ANALYSES = os.getenv('STORAGE_BUCKET_ANALYSES', 'analyses')
    STORAGE_BUCKET_LOGS = os.getenv('STORAGE_BUCKET_LOGS', 'logs')

    # Enhanced LLM Configuration
    LLM_TIMEOUT = int(os.getenv('LLM_TIMEOUT', '90'))
    LLM_MAX_OUTPUT_TOKENS = int(os.getenv('LLM_MAX_OUTPUT_TOKENS', '8192'))

    # Custom prompt override
    CUSTOM_LLM_PROMPT = os.getenv('CUSTOM_LLM_PROMPT')

    # Default comprehensive analysis prompt
    DEFAULT_LLM_PROMPT = """Analyze this repository comprehensively and provide a detailed summary covering:

1. **Main Purpose and Functionality**: What does this repository do? What problem does it solve?

2. **Key Technologies and Frameworks Used**: List and briefly describe the main technologies, frameworks, libraries, and tools used.

3. **Architecture Overview**: Describe the overall architecture, design patterns, and structure of the codebase.

4. **Notable Features or Patterns**: Highlight interesting implementation details, design patterns, or unique approaches used.

5. **Potential Use Cases**: Describe scenarios where this repository could be useful or applied.

Please be comprehensive and specific, focusing on the actual implementation details and functionality rather than generic descriptions."""
</file>

<file path="src/pipeline.py">
"""
Cloud-integrated pipeline for Repository Research Tool.
Uses Redis queues, Supabase database, and cloud storage.
"""

import os
import uuid
import time
import json
import logging
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any

from config import ScraperConfig
from redis_queue import QueueManager, RepositoryQueueItem, FileQueueItem
from supabase_client import create_supabase_client, JobRecord, RepositoryRecord, AnalysisRecord
from storage_manager import create_storage_manager
from monitoring import RepositoryMonitor
from llm_rate_limiter import LLMRateLimiter

# Standalone worker functions that initialize their own resources

def repomix_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone repomix worker function that initializes its own resources."""
    import subprocess
    import shutil
    import tempfile
    from pathlib import Path

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from redis_queue import QueueManager, FileQueueItem
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client

    config = ScraperConfig()  # Loads from .env
    queue_manager = QueueManager(config.REDIS_URL)
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    logger = logging.getLogger(f'repomix_worker_{worker_id}')
    logger.info(f"Repomix Worker {worker_id} for job {job_id} started")

    repo_queue = queue_manager.get_repository_queue()
    file_queue = queue_manager.get_file_queue()

    while True:
        try:
            # Get repository from Redis queue
            repo_item = repo_queue.get(timeout=30)
            if repo_item is None:
                break

            repo_name = repo_item.data['repository_name']
            repo_url = repo_item.data['repository_url']
            logger.info(f"Repomix Worker {worker_id}: Starting {repo_name}")

            # Create a temporary local file for repomix output
            temp_dir = Path(tempfile.gettempdir()) / job_id
            temp_dir.mkdir(parents=True, exist_ok=True)
            safe_repo_name = repo_name.replace('/', '_')
            local_output_file = temp_dir / f"{safe_repo_name}.md"

            # Run the actual repomix command
            npx_path = shutil.which('npx')
            if not npx_path:
                raise RuntimeError("npx not found in PATH")

            cmd = [
                npx_path, 'repomix', '--remote', repo_url,
                '--include', config.FILE_INCLUDES,
                '--output', str(local_output_file)
            ]
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=600,
                encoding='utf-8', errors='replace'
            )

            if result.returncode != 0 or not local_output_file.exists():
                logger.error(f"Repomix Worker {worker_id}: Failed {repo_name} - {result.stderr[:200]}")
                # Clean up and continue
                if local_output_file.exists():
                    local_output_file.unlink()
                repo_queue.task_done(repo_item)
                continue

            # Read content from successful repomix run
            with open(local_output_file, 'r', encoding='utf-8') as f:
                repomix_content = f.read()

            # Upload to cloud storage
            repomix_url = storage_manager.upload_repomix_file(
                job_id, safe_repo_name, repomix_content
            )

            # Update repository record in database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if repo_record:
                supabase_client.update_repository_status(
                    repo_record.id,
                    "completed",
                    repomix_file_url=repomix_url
                )

            # Add to LLM queue
            file_item = FileQueueItem(
                file_path=repomix_url,
                repository_name=repo_name,
                job_id=job_id
            )
            file_queue.put(file_item)

            # Clean up temporary file
            local_output_file.unlink()
            repo_queue.task_done(repo_item)

            logger.info(f"Repomix Worker {worker_id}: Completed {repo_name}")

        except Exception as e:
            logger.error(f"Repomix Worker {worker_id}: Error processing repository: {e}")
            if 'repo_item' in locals():
                repo_queue.task_done(repo_item)
            break


def llm_worker(worker_id: int, config_dict: dict, job_id: str):
    """Standalone LLM worker function that initializes its own resources."""
    import requests
    import uuid
    from datetime import datetime

    # Initialize resources INSIDE the worker
    from config import ScraperConfig
    from redis_queue import QueueManager
    from storage_manager import create_storage_manager
    from supabase_client import create_supabase_client, AnalysisRecord
    from llm_rate_limiter import LLMRateLimiter

    config = ScraperConfig()  # Loads from .env
    queue_manager = QueueManager(config.REDIS_URL)
    storage_manager = create_storage_manager()
    supabase_client = create_supabase_client()

    import redis
    redis_client = redis.from_url(config.REDIS_URL)
    rate_limiter = LLMRateLimiter(redis_client=redis_client)

    logger = logging.getLogger(f'llm_worker_{worker_id}')
    logger.info(f"LLM Worker {worker_id} for job {job_id} started")

    file_queue = queue_manager.get_file_queue()

    def build_analysis_prompt(repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(config, 'CUSTOM_PROMPT') and config.CUSTOM_PROMPT:
            return config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    while True:
        try:
            # Get file from Redis queue
            file_item = file_queue.get(timeout=30)
            if file_item is None:
                break

            repo_name = file_item.data['repository_name']
            file_url = file_item.data['file_path']
            logger.info(f"LLM Worker {worker_id}: Starting analysis of {repo_name}")

            # Download repomix content from cloud storage
            repomix_content = storage_manager.download_repomix_file(file_url)
            file_size_mb = len(repomix_content.encode('utf-8')) / (1024 * 1024)

            # Wait for rate limit slot
            if not rate_limiter.wait_for_slot(file_size_mb, max_wait_seconds=300):
                logger.warning(f"LLM Worker {worker_id} timed out waiting for rate limit slot")
                continue

            # Prepare and make the actual Gemini API call
            prompt = build_analysis_prompt(repo_name, repomix_content)

            data = {
                'contents': [{'parts': [{'text': prompt}]}],
                'generationConfig': {
                    'maxOutputTokens': config.LLM_MAX_OUTPUT_TOKENS,
                    'temperature': 0.1
                }
            }

            response = requests.post(
                f"{config.LLM_BASE_URL}/models/{config.LLM_MODEL}:generateContent?key={config.LLM_API_KEY}",
                json=data,
                timeout=config.LLM_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(f"LLM Worker {worker_id}: Failed analysis for {repo_name} - API Status: {response.status_code}")
                file_queue.task_done(file_item)
                continue

            result = response.json()
            analysis_content = result['candidates'][0]['content']['parts'][0]['text']

            # Find the corresponding repository_id from the database
            repositories = supabase_client.get_repositories_by_job(job_id)
            repo_record = next((r for r in repositories if r.name == repo_name), None)

            if not repo_record:
                logger.error(f"LLM Worker {worker_id}: Could not find repository record for {repo_name}")
                file_queue.task_done(file_item)
                continue

            # Create analysis record
            analysis_record = AnalysisRecord(
                id=str(uuid.uuid4()),
                repository_id=repo_record.id,
                job_id=job_id,
                worker_id=str(worker_id),
                processed_at=datetime.now().isoformat(),
                analysis_content=analysis_content,
                analysis_length=len(analysis_content)
            )

            supabase_client.create_analysis(analysis_record)
            file_queue.task_done(file_item)

            logger.info(f"LLM Worker {worker_id}: Completed analysis for {repo_name}")

        except Exception as e:
            logger.error(f"LLM Worker {worker_id}: Error processing file: {e}")
            if 'file_item' in locals():
                file_queue.task_done(file_item)
            break


logger = logging.getLogger(__name__)

class RepositoryPipeline:
    """
    Cloud-native pipeline for repository processing.
    Uses Redis for distributed queues, Supabase for persistence, and cloud storage.
    """
    
    def __init__(self, config=None, job_id=None):
        """
        Initialize the cloud pipeline.
        
        Args:
            config: Configuration object (defaults to ScraperConfig)
            job_id: Unique job identifier (generated if not provided)
        """
        self.config = config or ScraperConfig()
        self.job_id = job_id or str(uuid.uuid4())
        
        # Initialize cloud services
        self._initialize_cloud_services()
        
        # Initialize local components
        self.repomix_processes = []
        self.llm_processes = []
        self.monitor = None
        
        # Create local output directory for temporary files
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
        self.output_dir = Path(self.config.OUTPUT_BASE) / f"cloud_{timestamp_str}"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize monitoring
        self.monitor = RepositoryMonitor(self.output_dir)
        
        logger.info(f"Initialized cloud pipeline for job {self.job_id}")
    
    def _initialize_cloud_services(self):
        """Initialize cloud service connections."""
        try:
            # Initialize queue manager
            self.queue_manager = QueueManager(self.config.REDIS_URL)
            self.repo_queue = self.queue_manager.get_repository_queue()
            self.file_queue = self.queue_manager.get_file_queue()

            # Initialize distributed rate limiter with Redis client
            self.rate_limiter = LLMRateLimiter(
                redis_client=self.queue_manager.redis_client,
                rate_limit_mb_per_min=self.config.LLM_RATE_LIMIT_MB_PER_MIN,
                rate_limit_requests_per_min=getattr(self.config, 'LLM_RATE_LIMIT_REQUESTS_PER_MIN', 1000)
            )

            # Initialize Supabase client
            self.supabase_client = create_supabase_client()

            # Initialize storage manager
            self.storage_manager = create_storage_manager()
            
            logger.info("Cloud services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cloud services: {e}")
            raise
    
    def create_job(self, keywords: str, min_stars: int, max_repos: int,
                   custom_prompt: Optional[str] = None, debug: bool = False) -> JobRecord:
        """
        Create a new analysis job in the database.
        
        Args:
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            custom_prompt: Optional custom LLM prompt
            debug: Debug mode flag
            
        Returns:
            Created job record
        """
        try:
            job = JobRecord(
                id=self.job_id,
                keywords=keywords,
                min_stars=min_stars,
                max_repos=max_repos,
                status="pending",
                created_at=datetime.now().isoformat(),
                custom_prompt=custom_prompt,
                debug=debug
            )
            
            created_job = self.supabase_client.create_job(job)
            logger.info(f"Created job {self.job_id} in database")
            return created_job
            
        except Exception as e:
            logger.error(f"Failed to create job: {e}")
            raise
    
    def add_repositories(self, repositories: List[Dict[str, Any]]) -> int:
        """
        Add repositories to the processing queue and database.
        
        Args:
            repositories: List of repository information dictionaries
            
        Returns:
            Number of repositories added
        """
        try:
            added_count = 0
            
            for repo_info in repositories:
                # Create repository record in database
                repo_record = RepositoryRecord(
                    id=str(uuid.uuid4()),
                    job_id=self.job_id,
                    name=repo_info.get('full_name', 'unknown'),
                    url=repo_info.get('html_url', ''),
                    stars=repo_info.get('stargazers_count', 0),
                    file_size_mb=0.0  # Will be updated after repomix processing
                )
                
                self.supabase_client.create_repository(repo_record)
                
                # Add to Redis queue
                queue_item = RepositoryQueueItem(
                    repository_url=repo_info.get('html_url', ''),
                    repository_name=repo_info.get('full_name', 'unknown'),
                    job_id=self.job_id
                )
                
                self.repo_queue.put(queue_item)
                added_count += 1
            
            # Update job with total repositories
            self.supabase_client.update_job_status(
                self.job_id, 
                "running", 
                total_repositories=added_count
            )
            
            logger.info(f"Added {added_count} repositories to queue and database")
            return added_count
            
        except Exception as e:
            logger.error(f"Failed to add repositories: {e}")
            raise
    
    def start_workers(self):
        """Start repomix and LLM worker processes."""
        try:
            # Convert config to dict for passing to workers
            config_dict = {
                'REDIS_URL': self.config.REDIS_URL,
                'SUPABASE_URL': self.config.SUPABASE_URL,
                'SUPABASE_KEY': self.config.SUPABASE_KEY,
                'LLM_API_KEY': self.config.LLM_API_KEY,
                'LLM_BASE_URL': self.config.LLM_BASE_URL,
                'LLM_MODEL': self.config.LLM_MODEL,
                'FILE_INCLUDES': self.config.FILE_INCLUDES
            }

            # Start repomix workers
            for i in range(self.config.REPOMIX_WORKERS):
                p = multiprocessing.Process(
                    target=repomix_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.repomix_processes.append(p)

            # Start LLM workers
            for i in range(self.config.LLM_WORKERS):
                p = multiprocessing.Process(
                    target=llm_worker,
                    args=(i, config_dict, self.job_id)
                )
                p.start()
                self.llm_processes.append(p)

            logger.info(f"Started {len(self.repomix_processes)} repomix and {len(self.llm_processes)} LLM workers")

        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            raise


    
    def wait_for_completion(self, timeout: Optional[int] = None):
        """
        Wait for all processing to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
        """
        try:
            start_time = time.time()
            
            while True:
                # Check queue statistics
                stats = self.queue_manager.get_all_stats()
                repo_pending = stats.get('repositories', {}).get('pending', 0)
                file_pending = stats.get('files', {}).get('pending', 0)
                
                if repo_pending == 0 and file_pending == 0:
                    logger.info("All processing completed")
                    break
                
                if timeout and (time.time() - start_time) > timeout:
                    logger.warning(f"Timeout reached after {timeout} seconds")
                    break
                
                logger.info(f"Waiting... Repos: {repo_pending}, Files: {file_pending}")
                time.sleep(10)
            
        except Exception as e:
            logger.error(f"Error waiting for completion: {e}")
            raise
    
    def finalize_job(self) -> str:
        """
        Finalize the job by creating the final analysis file.
        
        Returns:
            URL of the final analysis file
        """
        try:
            # Get all analyses for this job
            analyses = self.supabase_client.get_analyses_by_job(self.job_id)
            repositories = self.supabase_client.get_repositories_by_job(self.job_id)
            
            # Create final analysis data
            analysis_data = {
                "metadata": {
                    "job_id": self.job_id,
                    "generated": datetime.now().isoformat(),
                    "total_repositories": len(repositories),
                    "total_analyses": len(analyses),
                    "processing_configuration": {
                        "repomix_workers": self.config.REPOMIX_WORKERS,
                        "llm_workers": self.config.LLM_WORKERS,
                        "deployment_mode": "unified"
                    }
                },
                "analyses": []
            }
            
            # Add analyses to final data
            for analysis in analyses:
                repo = next((r for r in repositories if r.id == analysis.repository_id), None)
                if repo:
                    analysis_data["analyses"].append({
                        "repository": {
                            "name": repo.name,
                            "url": repo.url,
                            "stars": repo.stars,
                            "repomix_file_url": repo.repomix_file_url
                        },
                        "processing": {
                            "worker_id": analysis.worker_id,
                            "processed_at": analysis.processed_at,
                            "analysis_length": analysis.analysis_length
                        },
                        "analysis": {
                            "content": analysis.analysis_content
                        }
                    })
            
            # Upload final analysis file
            analysis_url = self.storage_manager.upload_analysis_file(
                job_id=self.job_id,
                analysis_data=analysis_data,
                filename="final_analysis.json"
            )
            
            # Update job status
            self.supabase_client.update_job_status(
                self.job_id,
                "completed",
                analysis_file_url=analysis_url
            )
            
            logger.info(f"Job {self.job_id} finalized with analysis file: {analysis_url}")
            return analysis_url
            
        except Exception as e:
            logger.error(f"Failed to finalize job: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise

    def _build_analysis_prompt(self, repository_name: str, content: str) -> str:
        """Build the analysis prompt for LLM processing"""
        if hasattr(self.config, 'CUSTOM_PROMPT') and self.config.CUSTOM_PROMPT:
            return self.config.CUSTOM_PROMPT.format(
                repo_name=repository_name,
                file_size_mb=len(content.encode('utf-8')) / (1024 * 1024),
                content=content
            )

        # Default comprehensive analysis prompt
        return f"""Analyze this repository code and provide a comprehensive summary.

Repository: {repository_name}

Please provide:
1. Main Purpose and Functionality
2. Key Technologies and Frameworks Used
3. Architecture Overview
4. Notable Features or Patterns
5. Potential Use Cases

Repository Content:
{content[:50000]}"""

    def stop_workers(self):
        """Stop all worker processes."""
        try:
            # Send poison pills to stop workers
            for _ in range(len(self.repomix_processes)):
                self.repo_queue.put(None)
            
            for _ in range(len(self.llm_processes)):
                self.file_queue.put(None)
            
            # Wait for processes to finish
            for p in self.repomix_processes + self.llm_processes:
                p.join(timeout=10)
                if p.is_alive():
                    p.terminate()
            
            logger.info("All workers stopped")
            
        except Exception as e:
            logger.error(f"Error stopping workers: {e}")
    
    def run(self, repositories: List[Dict[str, Any]], keywords: str, 
            min_stars: int, max_repos: int, **kwargs) -> str:
        """
        Run the complete cloud pipeline.
        
        Args:
            repositories: List of repository information
            keywords: Search keywords
            min_stars: Minimum stars filter
            max_repos: Maximum repositories per keyword
            **kwargs: Additional job parameters
            
        Returns:
            URL of the final analysis file
        """
        try:
            # Create job
            self.create_job(keywords, min_stars, max_repos, **kwargs)
            
            # Add repositories
            self.add_repositories(repositories)
            
            # Start workers
            self.start_workers()
            
            # Wait for completion
            self.wait_for_completion(timeout=3600)  # 1 hour timeout
            
            # Finalize job
            analysis_url = self.finalize_job()
            
            return analysis_url
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.supabase_client.update_job_status(
                self.job_id,
                "failed",
                error_message=str(e)
            )
            raise
        finally:
            # Always stop workers
            self.stop_workers()
</file>

</files>
