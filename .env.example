# GitHub API Token (optional but recommended for higher rate limits)
GITHUB_TOKEN=your_github_token_here

# Gemini API Key (required)
GEMINI_API_KEY=your_gemini_api_key_here

# Sentry Configuration (optional but recommended for monitoring)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=development
SENTRY_RELEASE=1.0.0

# Sentry API Configuration (optional - for fetching logs and analysis)
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here
SENTRY_ORG_SLUG=your_organization_slug
SENTRY_PROJECT_SLUG=your_project_slug

# Gemini API Configuration (optional)
# Set to true to use OpenAI-compatible API endpoint
USE_OPENAI_COMPATIBLE=false
# Custom API base URL (only used if USE_OPENAI_COMPATIBLE=true)
GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com/v1beta
