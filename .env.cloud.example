# Cloud Environment Configuration Template
# Copy this to .env and configure your actual values

# Deployment Configuration
DEPLOYMENT_MODE=cloud
# Options: local, cloud

# API Keys
LLM_API_KEY=your_gemini_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
GITHUB_TOKEN=your_github_token_here

# LLM Configuration
LLM_BASE_URL=https://generativelanguage.googleapis.com/v1beta
LLM_MODEL=gemini-2.0-flash
LLM_TIMEOUT=90
LLM_MAX_OUTPUT_TOKENS=8192

# Worker Configuration
REPOMIX_WORKERS=15
LLM_WORKERS=4
LLM_RATE_LIMIT_MB_PER_MIN=12

# Repository Configuration
MAX_REPOS_PER_KEYWORD=20
MIN_STARS=30

# Redis Configuration (Cloud)
REDIS_URL=redis://your-redis-host:6379
# For Google Cloud Memorystore: redis://10.x.x.x:6379
# For Redis Cloud: redis://username:password@host:port
REDIS_QUEUE_TIMEOUT=300

# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_KEY=your_service_role_key_here
SUPABASE_ANON_KEY=your_anon_key_here

# Storage Bucket Names
STORAGE_BUCKET_REPOMIXES=repomixes
STORAGE_BUCKET_ANALYSES=analyses
STORAGE_BUCKET_LOGS=logs

# Output Configuration
OUTPUT_BASE=output

# Optional: Custom LLM Prompt
# CUSTOM_LLM_PROMPT=Your custom analysis prompt here

# Sentry Configuration (Optional)
SENTRY_DSN=your_sentry_dsn_here

# Google Cloud Configuration (for deployment)
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_REGION=us-central1

# Service Configuration
SERVICE_PORT=8080
SERVICE_HOST=0.0.0.0
SERVICE_DEBUG=false

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30
READINESS_CHECK_TIMEOUT=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Security Configuration
CORS_ORIGINS=*
API_KEY_HEADER=X-API-Key
# API_KEY=your_api_key_for_service_authentication

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Cleanup Configuration
AUTO_CLEANUP_DAYS=30
CLEANUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# Development Configuration
DEV_MODE=false
DEV_SKIP_REPOMIX=false
DEV_MOCK_LLM=false
