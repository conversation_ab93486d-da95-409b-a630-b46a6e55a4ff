apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: repository-research-tool
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource allocation
        run.googleapis.com/cpu: "2"
        run.googleapis.com/memory: "4Gi"
        
        # Timeout configuration
        run.googleapis.com/timeout: "3600s"
        
        # VPC configuration for Redis access
        run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/redis-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only
        
        # Execution environment
        run.googleapis.com/execution-environment: gen2
        
    spec:
      containerConcurrency: 10
      timeoutSeconds: 3600
      serviceAccountName: repository-research-sa@PROJECT_ID.iam.gserviceaccount.com
      
      containers:
      - name: repository-research-tool
        image: gcr.io/PROJECT_ID/repository-research-tool:latest
        
        ports:
        - name: http1
          containerPort: 8080
          
        env:
        # Deployment configuration
        - name: DEPLOYMENT_MODE
          value: "cloud"
        - name: PORT
          value: "8080"
          
        # Redis configuration
        - name: REDIS_URL
          value: "redis://REDIS_PRIVATE_IP:6379"
          
        # Supabase configuration
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: url
        - name: SUPABASE_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: service-key
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: anon-key
              
        # API keys
        - name: LLM_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: gemini-api-key
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: github-token
              
        # Worker configuration
        - name: REPOMIX_WORKERS
          value: "15"
        - name: LLM_WORKERS
          value: "4"
        - name: LLM_TIMEOUT
          value: "90"
        - name: LLM_MAX_OUTPUT_TOKENS
          value: "8192"
          
        # Storage configuration
        - name: STORAGE_BUCKET_REPOMIXES
          value: "repomixes"
        - name: STORAGE_BUCKET_ANALYSES
          value: "analyses"
        - name: STORAGE_BUCKET_LOGS
          value: "logs"
          
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
            
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30

  traffic:
  - percent: 100
    latestRevision: true
