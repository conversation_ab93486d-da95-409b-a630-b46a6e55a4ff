#!/usr/bin/env python3
"""
Comprehensive test runner that bypasses Redis requirement
and runs the complete analysis with your specified parameters.
"""

import os
import sys
import uuid
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from github_search import search_repositories
from config import ScraperConfig

def main():
    """Run comprehensive test with specified parameters."""
    
    print("🚀 COMPREHENSIVE REPOSITORY RESEARCH TEST")
    print("=" * 60)
    print("Parameters:")
    print("   Keywords: 'proxy list', 'free proxy', 'proxy scrape'")
    print("   Min stars: 30")
    print("   Max repos per keyword: 60")
    print("   Expected total: ~180 repositories")
    print()
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = ScraperConfig()
        print("✅ Configuration loaded successfully")
        
        # Search for repositories
        print("\n🔍 Searching GitHub repositories...")
        keywords = "proxy list,free proxy,proxy scrape"
        
        repositories = search_repositories(
            keywords=keywords,
            min_stars=30,
            max_repos=60
        )
        
        print(f"✅ Found {len(repositories)} repositories total")
        
        if not repositories:
            print("❌ No repositories found. Check GitHub token and rate limits.")
            return False
        
        # Show top repositories by category
        print("\n📊 Top repositories found:")
        for i, repo in enumerate(repositories[:10]):
            print(f"   {i+1:2d}. {repo['name']} ({repo['stars']} stars)")
        
        if len(repositories) > 10:
            print(f"   ... and {len(repositories) - 10} more repositories")
        
        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        output_dir = f"output/comprehensive_test_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n📁 Output directory: {output_dir}")
        
        # Process repositories (simplified without Redis)
        print("\n🔄 Processing repositories...")
        
        # Create analysis data structure
        analysis_data = {
            "metadata": {
                "generated": datetime.now().isoformat(),
                "keywords": keywords,
                "total_repositories": len(repositories),
                "min_stars": 30,
                "max_repos_per_keyword": 60,
                "github_token_used": bool(config.GITHUB_TOKEN),
                "processing_mode": "comprehensive_test"
            },
            "repositories": [],
            "summary": {
                "total_found": len(repositories),
                "highest_stars": max(repo['stars'] for repo in repositories),
                "lowest_stars": min(repo['stars'] for repo in repositories),
                "average_stars": sum(repo['stars'] for repo in repositories) / len(repositories),
                "languages": {},
                "topics": {}
            }
        }
        
        # Process each repository
        for i, repo in enumerate(repositories):
            print(f"   Processing {i+1}/{len(repositories)}: {repo['name']}")
            
            # Add repository data
            repo_data = {
                "name": repo['name'],
                "full_name": repo['full_name'],
                "url": repo['html_url'],
                "stars": repo['stars'],
                "language": repo.get('language', 'Unknown'),
                "description": repo.get('description', ''),
                "updated_at": repo.get('updated_at', ''),
                "topics": repo.get('topics', [])
            }
            
            analysis_data["repositories"].append(repo_data)
            
            # Update summary statistics
            lang = repo.get('language', 'Unknown')
            analysis_data["summary"]["languages"][lang] = analysis_data["summary"]["languages"].get(lang, 0) + 1
            
            for topic in repo.get('topics', []):
                analysis_data["summary"]["topics"][topic] = analysis_data["summary"]["topics"].get(topic, 0) + 1
        
        # Save analysis file
        import json
        analysis_file = os.path.join(output_dir, "comprehensive_analysis.json")
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Analysis saved to: {analysis_file}")
        
        # Create summary report
        summary_file = os.path.join(output_dir, "summary_report.md")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"# Repository Research Test Results\n\n")
            f.write(f"**Generated:** {datetime.now().isoformat()}\n\n")
            f.write(f"## Search Parameters\n")
            f.write(f"- **Keywords:** {keywords}\n")
            f.write(f"- **Min stars:** 30\n")
            f.write(f"- **Max repos per keyword:** 60\n")
            f.write(f"- **Total found:** {len(repositories)} repositories\n\n")
            
            f.write(f"## Summary Statistics\n")
            f.write(f"- **Highest stars:** {analysis_data['summary']['highest_stars']:,}\n")
            f.write(f"- **Lowest stars:** {analysis_data['summary']['lowest_stars']:,}\n")
            f.write(f"- **Average stars:** {analysis_data['summary']['average_stars']:.1f}\n\n")
            
            f.write(f"## Top Languages\n")
            sorted_langs = sorted(analysis_data['summary']['languages'].items(), key=lambda x: x[1], reverse=True)
            for lang, count in sorted_langs[:10]:
                f.write(f"- **{lang}:** {count} repositories\n")
            
            f.write(f"\n## Top Repositories\n")
            for i, repo in enumerate(repositories[:20]):
                f.write(f"{i+1}. **{repo['name']}** ({repo['stars']:,} stars) - {repo.get('description', 'No description')[:100]}...\n")
        
        print(f"✅ Summary report saved to: {summary_file}")
        
        print("\n🎉 COMPREHENSIVE TEST COMPLETED SUCCESSFULLY!")
        print(f"   Total repositories processed: {len(repositories)}")
        print(f"   Analysis file: {analysis_file}")
        print(f"   Summary report: {summary_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Comprehensive test failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
