#!/usr/bin/env python3
"""
Component test for repomix_worker token-based chunking functionality.

Tests that the repomix_worker correctly:
1. Checks token count of repomix output
2. If > 850,000 tokens: chunks into parts, keeps only first 3
3. If <= 850,000 tokens: uses original file
4. Uploads resulting file(s) to Supabase Storage
5. Queues separate task for each uploaded file/chunk
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class TestRepomixWorkerTokenChunking(unittest.TestCase):
    """Test token-based chunking in repomix worker."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_job_id = "test_job_123"
        self.test_repo_name = "test/repo"
        self.test_repo_url = "https://github.com/test/repo"
        
        # Mock configuration
        self.mock_config = {
            'REDIS_URL': 'redis://localhost:6379',
            'SUPABASE_URL': 'http://localhost:3000',
            'SUPABASE_KEY': 'test_key',
            'LLM_API_KEY': 'test_api_key',
            'LLM_BASE_URL': 'https://api.test.com',
            'LLM_MODEL': 'gemini-2.0-flash',
            'FILE_INCLUDES': '**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md'
        }
    
    @patch('src.pipeline.get_queue_manager')
    @patch('src.pipeline.create_storage_manager')
    @patch('src.pipeline.create_supabase_client')
    @patch('src.pipeline.ScraperConfig')
    @patch('src.pipeline.subprocess.run')
    @patch('src.pipeline.shutil.which')
    @patch('src.pipeline.count_tokens_in_text')
    @patch('src.pipeline.get_token_chunker')
    def test_repomix_worker_large_file_chunking(self, mock_get_chunker, mock_count_tokens, 
                                               mock_which, mock_subprocess, mock_config_class,
                                               mock_supabase, mock_storage, mock_queue_manager):
        """Test that files > 850K tokens are properly chunked."""
        from src.pipeline import repomix_worker
        from src.queue_manager import RepositoryQueueItem, FileQueueItem
        
        # Setup mocks
        mock_config_class.return_value = Mock(**self.mock_config)
        mock_which.return_value = '/usr/bin/npx'
        
        # Mock successful repomix execution
        mock_subprocess.return_value = Mock(returncode=0, stderr='')
        
        # Mock large file (> 850K tokens)
        mock_count_tokens.return_value = 2000000  # 2M tokens
        
        # Mock chunker
        mock_chunker = Mock()
        mock_chunker.chunk_content_by_tokens.return_value = [
            "chunk 1 content",
            "chunk 2 content", 
            "chunk 3 content"
        ]
        mock_get_chunker.return_value = mock_chunker
        
        # Mock queue manager
        mock_repo_queue = Mock()
        mock_file_queue = Mock()
        mock_queue_manager_instance = Mock()
        mock_queue_manager_instance.get_repository_queue.return_value = mock_repo_queue
        mock_queue_manager_instance.get_file_queue.return_value = mock_file_queue
        mock_queue_manager_instance.get_repomix_validated_queue.return_value = Mock()
        mock_queue_manager.return_value = mock_queue_manager_instance
        
        # Mock storage manager
        mock_storage_instance = Mock()
        mock_storage_instance.upload_repomix_file.side_effect = [
            "https://storage.test/chunk_0.md",
            "https://storage.test/chunk_1.md", 
            "https://storage.test/chunk_2.md",
            "https://storage.test/full.md"
        ]
        mock_storage.return_value = mock_storage_instance
        
        # Mock supabase client
        mock_supabase_instance = Mock()
        mock_supabase_instance.get_repositories_by_job.return_value = [
            Mock(id="repo_123", name=self.test_repo_name)
        ]
        mock_supabase.return_value = mock_supabase_instance
        
        # Mock repository queue item
        repo_item = RepositoryQueueItem(
            repository_name=self.test_repo_name,
            repository_url=self.test_repo_url,
            job_id=self.test_job_id
        )
        mock_repo_queue.get.side_effect = [repo_item, None]  # Return item once, then None to exit
        
        # Create temporary file for repomix output
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
            temp_file.write("Large repository content that exceeds 850K tokens...")
            temp_file_path = temp_file.name
        
        try:
            # Mock file operations
            with patch('builtins.open', unittest.mock.mock_open(read_data="Large repository content")):
                with patch('src.pipeline.Path') as mock_path:
                    mock_path.return_value.__truediv__.return_value = Path(temp_file_path)
                    mock_path.return_value.mkdir = Mock()
                    
                    with patch('src.pipeline.get_file_size_mb', return_value=5.2):
                        # Run the worker
                        repomix_worker(1, self.mock_config, self.test_job_id)
            
            # Verify chunking was called
            mock_get_chunker.assert_called_once_with(max_tokens_per_chunk=850000, max_chunks=3)
            mock_chunker.chunk_content_by_tokens.assert_called_once()
            
            # Verify 4 uploads: 3 chunks + 1 full file
            self.assertEqual(mock_storage_instance.upload_repomix_file.call_count, 4)
            
            # Verify 3 file queue items were added (one for each chunk)
            self.assertEqual(mock_file_queue.put.call_count, 3)
            
            # Verify repository status was updated
            mock_supabase_instance.update_repository_status.assert_called_once()
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    @patch('src.pipeline.get_queue_manager')
    @patch('src.pipeline.create_storage_manager')
    @patch('src.pipeline.create_supabase_client')
    @patch('src.pipeline.ScraperConfig')
    @patch('src.pipeline.subprocess.run')
    @patch('src.pipeline.shutil.which')
    @patch('src.pipeline.count_tokens_in_text')
    def test_repomix_worker_small_file_no_chunking(self, mock_count_tokens, mock_which, 
                                                  mock_subprocess, mock_config_class,
                                                  mock_supabase, mock_storage, mock_queue_manager):
        """Test that files <= 850K tokens are not chunked."""
        from src.pipeline import repomix_worker
        from src.queue_manager import RepositoryQueueItem
        
        # Setup mocks
        mock_config_class.return_value = Mock(**self.mock_config)
        mock_which.return_value = '/usr/bin/npx'
        mock_subprocess.return_value = Mock(returncode=0, stderr='')
        
        # Mock small file (<= 850K tokens)
        mock_count_tokens.return_value = 500000  # 500K tokens
        
        # Mock queue manager
        mock_repo_queue = Mock()
        mock_file_queue = Mock()
        mock_queue_manager_instance = Mock()
        mock_queue_manager_instance.get_repository_queue.return_value = mock_repo_queue
        mock_queue_manager_instance.get_file_queue.return_value = mock_file_queue
        mock_queue_manager_instance.get_repomix_validated_queue.return_value = Mock()
        mock_queue_manager.return_value = mock_queue_manager_instance
        
        # Mock storage manager
        mock_storage_instance = Mock()
        mock_storage_instance.upload_repomix_file.return_value = "https://storage.test/file.md"
        mock_storage.return_value = mock_storage_instance
        
        # Mock supabase client
        mock_supabase_instance = Mock()
        mock_supabase_instance.get_repositories_by_job.return_value = [
            Mock(id="repo_123", name=self.test_repo_name)
        ]
        mock_supabase.return_value = mock_supabase_instance
        
        # Mock repository queue item
        repo_item = RepositoryQueueItem(
            repository_name=self.test_repo_name,
            repository_url=self.test_repo_url,
            job_id=self.test_job_id
        )
        mock_repo_queue.get.side_effect = [repo_item, None]
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
            temp_file.write("Small repository content...")
            temp_file_path = temp_file.name
        
        try:
            # Mock file operations
            with patch('builtins.open', unittest.mock.mock_open(read_data="Small repository content")):
                with patch('src.pipeline.Path') as mock_path:
                    mock_path.return_value.__truediv__.return_value = Path(temp_file_path)
                    mock_path.return_value.mkdir = Mock()
                    
                    with patch('src.pipeline.get_file_size_mb', return_value=1.2):
                        # Run the worker
                        repomix_worker(1, self.mock_config, self.test_job_id)
            
            # Verify only 1 upload (original file, no chunks)
            self.assertEqual(mock_storage_instance.upload_repomix_file.call_count, 1)
            
            # Verify 1 file queue item was added
            self.assertEqual(mock_file_queue.put.call_count, 1)
            
            # Verify repository status was updated
            mock_supabase_instance.update_repository_status.assert_called_once()
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)


if __name__ == '__main__':
    print("🧪 Testing Token-Based Chunking in Repomix Worker...")
    unittest.main(verbosity=2)
