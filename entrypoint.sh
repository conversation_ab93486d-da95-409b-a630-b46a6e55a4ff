#!/bin/sh
"""
Entrypoint script for Repository Research Tool containers.
Allows running either API service or Worker service from the same container image.
"""

set -e

# Function to display usage
usage() {
    echo "Usage: $0 [api|worker|help]"
    echo ""
    echo "Commands:"
    echo "  api     - Start the API service (Flask REST API)"
    echo "  worker  - Start the Worker service (distributed processing workers)"
    echo "  help    - Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DEPLOYMENT_MODE     - Set to 'cloud' for production deployment"
    echo "  REDIS_URL          - Redis connection URL for distributed queues"
    echo "  SUPABASE_URL       - Supabase project URL"
    echo "  SUPABASE_KEY       - Supabase service role key"
    echo "  LLM_API_KEY        - Gemini API key for LLM processing"
    echo "  GITHUB_TOKEN       - GitHub personal access token"
    echo ""
}

# Function to check required environment variables
check_env() {
    local missing_vars=""
    
    # Check for required environment variables
    if [ -z "$DEPLOYMENT_MODE" ]; then
        echo "⚠️ DEPLOYMENT_MODE not set, defaulting to 'local'"
        export DEPLOYMENT_MODE="local"
    fi
    
    if [ "$DEPLOYMENT_MODE" = "cloud" ]; then
        # Cloud mode requires additional variables
        [ -z "$REDIS_URL" ] && missing_vars="$missing_vars REDIS_URL"
        [ -z "$SUPABASE_URL" ] && missing_vars="$missing_vars SUPABASE_URL"
        [ -z "$SUPABASE_KEY" ] && missing_vars="$missing_vars SUPABASE_KEY"
    fi
    
    # Always required
    [ -z "$LLM_API_KEY" ] && missing_vars="$missing_vars LLM_API_KEY"
    [ -z "$GITHUB_TOKEN" ] && missing_vars="$missing_vars GITHUB_TOKEN"
    
    if [ -n "$missing_vars" ]; then
        echo "❌ Missing required environment variables:$missing_vars"
        echo "Please set these variables before starting the service."
        exit 1
    fi
}

# Function to start API service
start_api() {
    echo "🚀 Starting Repository Research Tool API Service"
    echo "   Mode: $DEPLOYMENT_MODE"
    echo "   Port: ${PORT:-8080}"
    
    if [ "$DEPLOYMENT_MODE" = "cloud" ]; then
        echo "   Redis: $REDIS_URL"
        echo "   Supabase: $SUPABASE_URL"
    fi
    
    exec python service.py
}

# Function to start Worker service
start_worker() {
    echo "🚀 Starting Repository Research Tool Worker Service"
    echo "   Mode: $DEPLOYMENT_MODE"
    
    if [ "$DEPLOYMENT_MODE" != "cloud" ]; then
        echo "❌ Worker service requires cloud mode (DEPLOYMENT_MODE=cloud)"
        exit 1
    fi
    
    echo "   Redis: $REDIS_URL"
    echo "   Supabase: $SUPABASE_URL"
    
    exec python worker.py
}

# Main entrypoint logic
main() {
    # Check environment variables
    check_env
    
    # Parse command
    case "${1:-api}" in
        "api")
            start_api
            ;;
        "worker")
            start_worker
            ;;
        "help"|"--help"|"-h")
            usage
            exit 0
            ;;
        *)
            echo "❌ Unknown command: $1"
            echo ""
            usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
