"""
GitHub repository search functionality
"""

import requests
import time
from typing import List, Dict, Any


class GitHubSearcher:
    """GitHub API searcher for repositories"""
    
    def __init__(self, config):
        """Initialize with configuration"""
        self.config = config
        self.base_url = "https://api.github.com"
        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Repository-Research-Tool/1.0'
        }
        
        # Add authentication if token is available
        if config.GITHUB_TOKEN:
            self.headers['Authorization'] = f'token {config.GITHUB_TOKEN}'
    
    def search_repositories(self, keyword: str, min_stars: int = 30, max_repos: int = 20) -> List[Dict[str, Any]]:
        """
        Search for repositories by keyword

        Args:
            keyword: Search keyword
            min_stars: Minimum number of stars
            max_repos: Maximum number of repositories to return

        Returns:
            List of repository dictionaries
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"🔍 Searching GitHub for '{keyword}' repositories...")

        # Build search query
        query = f"{keyword} stars:>={min_stars}"
        logger.info(f"📝 Search query: '{query}'")

        params = {
            'q': query,
            'sort': 'updated',
            'order': 'desc',
            'per_page': min(max_repos, 100),  # GitHub API limit is 100 per page
            'page': 1
        }

        logger.info(f"🌐 API request parameters: {params}")

        try:
            # Make API request
            logger.info(f"📡 Making GitHub API request...")
            response = requests.get(
                f"{self.base_url}/search/repositories",
                headers=self.headers,
                params=params,
                timeout=30
            )

            logger.info(f"📊 API response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                repositories = data.get('items', [])
                total_count = data.get('total_count', 0)

                logger.info(f"🎯 GitHub reports {total_count} total repositories available for '{keyword}'")
                logger.info(f"📦 Retrieved {len(repositories)} repositories in this request")

                # Limit to requested number
                repositories = repositories[:max_repos]

                logger.info(f"✅ Returning {len(repositories)} repositories for '{keyword}'")
                
                # Extract relevant information
                result = []
                for repo in repositories:
                    repo_info = {
                        'full_name': repo['full_name'],
                        'name': repo['name'],
                        'url': repo['html_url'],  # Add 'url' key for service compatibility
                        'clone_url': repo['clone_url'],
                        'html_url': repo['html_url'],
                        'stars': repo['stargazers_count'],  # Add 'stars' key for service compatibility
                        'stargazers_count': repo['stargazers_count'],
                        'language': repo.get('language', 'Unknown'),
                        'description': repo.get('description', ''),
                        'updated_at': repo['updated_at']
                    }
                    result.append(repo_info)
                
                return result
                
            elif response.status_code == 403:
                print(f"❌ GitHub API rate limit exceeded for '{keyword}'")
                return []
            elif response.status_code == 422:
                print(f"❌ Invalid search query for '{keyword}': {query}")
                return []
            else:
                print(f"❌ GitHub API error for '{keyword}': {response.status_code}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error searching for '{keyword}': {e}")
            return []
        except Exception as e:
            print(f"❌ Unexpected error searching for '{keyword}': {e}")
            return []
    
    def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current GitHub API rate limit status"""
        try:
            response = requests.get(
                f"{self.base_url}/rate_limit",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'error': str(e)}


def search_repositories(keywords: str, min_stars: int = 30, max_repos: int = 20) -> List[Dict[str, Any]]:
    """
    Standalone function to search repositories.

    Args:
        keywords: Comma-separated keywords to search for
        min_stars: Minimum number of stars
        max_repos: Maximum number of repositories to return PER KEYWORD

    Returns:
        List of repository dictionaries (max_repos * number_of_keywords total)

    Example:
        keywords="proxy,vpn", max_repos=60 will return up to 120 repositories
        (60 for "proxy" + 60 for "vpn", minus any duplicates)
    """
    import logging
    from config import ScraperConfig

    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    config = ScraperConfig()
    searcher = GitHubSearcher(config)

    # Split keywords and search for each
    keyword_list = [k.strip() for k in keywords.split(',')]
    logger.info(f"🔍 GITHUB SEARCH FLOW: Starting search for {len(keyword_list)} keywords: {keyword_list}")
    logger.info(f"📊 GITHUB SEARCH FLOW: Search parameters: min_stars={min_stars}, max_repos={max_repos} PER KEYWORD")
    logger.info(f"🎯 GITHUB SEARCH FLOW: Expected maximum repositories: {len(keyword_list)} × {max_repos} = {len(keyword_list) * max_repos}")

    all_repositories = []

    for i, keyword in enumerate(keyword_list, 1):
        logger.info(f"🔎 GITHUB SEARCH FLOW: [{i}/{len(keyword_list)}] Processing keyword: '{keyword}'")
        logger.info(f"🎯 GITHUB SEARCH FLOW: Target for '{keyword}': {max_repos} repositories")

        # Each keyword gets exactly max_repos repositories
        # Use GitHub API limit (100) as the search limit, then trim to max_repos
        search_limit = min(100, max_repos)
        logger.info(f"🌐 GITHUB SEARCH FLOW: API search limit for '{keyword}': {search_limit}")

        repos = searcher.search_repositories(keyword, min_stars, search_limit)
        logger.info(f"📦 GITHUB SEARCH FLOW: Retrieved {len(repos)} repositories from GitHub API for '{keyword}'")

        # Limit to exactly max_repos for this keyword
        keyword_repos = repos[:max_repos]
        logger.info(f"✅ GITHUB SEARCH FLOW: Selected {len(keyword_repos)} repositories for '{keyword}' (max: {max_repos})")

        # Add keyword info to each repo for tracking
        for repo in keyword_repos:
            repo['search_keyword'] = keyword

        all_repositories.extend(keyword_repos)
        logger.info(f"📈 GITHUB SEARCH FLOW: Total repositories accumulated: {len(all_repositories)}")
        logger.info(f"📊 GITHUB SEARCH FLOW: Progress: {i}/{len(keyword_list)} keywords processed")

    logger.info(f"🔄 GITHUB SEARCH FLOW: Starting deduplication process...")
    logger.info(f"📊 GITHUB SEARCH FLOW: Total repositories before deduplication: {len(all_repositories)}")

    # Remove duplicates based on URL
    seen_urls = set()
    unique_repos = []
    duplicate_count = 0

    for repo in all_repositories:
        if repo['url'] not in seen_urls:
            seen_urls.add(repo['url'])
            unique_repos.append(repo)
        else:
            duplicate_count += 1
            logger.debug(f"🔄 GITHUB SEARCH FLOW: Duplicate found: {repo['url']} (keyword: {repo.get('search_keyword', 'unknown')})")

    logger.info(f"🗑️ GITHUB SEARCH FLOW: Removed {duplicate_count} duplicate repositories")
    logger.info(f"✨ GITHUB SEARCH FLOW: Unique repositories found: {len(unique_repos)}")

    # Sort by last updated (most recent first) as required by user specifications
    unique_repos.sort(key=lambda x: x.get('updated_at', ''), reverse=True)
    logger.info(f"📅 GITHUB SEARCH FLOW: Repositories sorted by last updated (most recent first)")

    # NO FINAL LIMITING - we want all unique repos from all keywords
    final_repos = unique_repos

    logger.info(f"🎯 GITHUB SEARCH FLOW: Final result: {len(final_repos)} repositories selected")
    logger.info(f"📊 GITHUB SEARCH FLOW: Expected maximum: {len(keyword_list)} keywords × {max_repos} repos = {len(keyword_list) * max_repos} repos")
    logger.info(f"✅ GITHUB SEARCH FLOW: Per-keyword logic working correctly: {len(final_repos)} ≤ {len(keyword_list) * max_repos}")

    # Log summary by keyword
    keyword_summary = {}
    for repo in final_repos:
        keyword = repo.get('search_keyword', 'unknown')
        keyword_summary[keyword] = keyword_summary.get(keyword, 0) + 1

    logger.info("📋 Final selection by keyword:")
    for keyword, count in keyword_summary.items():
        logger.info(f"   '{keyword}': {count} repositories")

    return final_repos
