import time
import uuid
import redis
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class LLMRateLimiter:
    """
    A distributed rate limiter for LLM API calls using Redis sliding window log.
    Enforces rate limits globally across all Cloud Run instances.
    Uses Redis Sorted Sets for efficient distributed rate limiting.
    """

    def __init__(self, redis_client: Optional[redis.Redis] = None,
                 rate_limit_mb_per_min: float = 12.0,
                 rate_limit_requests_per_min: int = 1000,
                 window_size_seconds: int = 60):
        """
        Initialize the distributed rate limiter.

        Args:
            redis_client: Redis client instance for distributed state
            rate_limit_mb_per_min: Maximum MB that can be processed per minute
            rate_limit_requests_per_min: Maximum requests per minute
            window_size_seconds: Time window for rate limiting (default: 60 seconds)
        """
        self.redis_client = redis_client
        self.rate_limit_mb_per_min = rate_limit_mb_per_min
        self.rate_limit_requests_per_min = rate_limit_requests_per_min
        self.window_size_seconds = window_size_seconds

        # Redis keys for rate limiting
        self.mb_log_key = "llm_rate_limit_mb_log"
        self.request_log_key = "llm_rate_limit_request_log"

        # Fallback to file-based for local development
        self.use_redis = redis_client is not None
        if not self.use_redis:
            logger.warning("Redis client not provided, falling back to local file-based rate limiting")
            self._init_file_fallback()

    def _init_file_fallback(self):
        """Initialize file-based fallback for local development."""
        from pathlib import Path
        import json

        self.state_file = Path('temp_rate_limiter_state.json')

        # Initialize state file if it doesn't exist
        if not self.state_file.exists():
            self._write_state({
                'total_mb': 0.0,
                'last_reset': time.time()
            })

    def _read_state(self):
        """Read state from file with error handling (fallback mode)"""
        import json
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass

        # Return default state if file doesn't exist or is corrupted
        return {
            'total_mb': 0.0,
            'last_reset': time.time()
        }

    def _write_state(self, state):
        """Write state to file with error handling (fallback mode)"""
        import json
        try:
            # Ensure directory exists
            self.state_file.parent.mkdir(parents=True, exist_ok=True)

            # Write atomically by writing to temp file first
            temp_file = self.state_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(state, f)

            # Atomic rename
            temp_file.replace(self.state_file)
        except IOError:
            # If file operations fail, continue without crashing
            pass

    def acquire_slot(self, file_size_mb: float = 0.0) -> bool:
        """
        Acquire a slot for processing a file of given size.
        Uses Redis sliding window log for distributed rate limiting.

        Args:
            file_size_mb: Size of the file to process in MB

        Returns:
            bool: True if slot acquired, False if rate limited
        """
        if self.use_redis:
            return self._acquire_slot_redis(file_size_mb)
        else:
            return self._acquire_slot_fallback(file_size_mb)

    def _acquire_slot_redis(self, file_size_mb: float) -> bool:
        """Redis-based sliding window rate limiting."""
        try:
            current_time = time.time()
            window_start = current_time - self.window_size_seconds

            # Generate unique request ID
            request_id = f"{current_time}:{uuid.uuid4()}"

            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()

            # Remove old entries from both logs
            pipe.zremrangebyscore(self.mb_log_key, 0, window_start)
            pipe.zremrangebyscore(self.request_log_key, 0, window_start)

            # Get current usage
            pipe.zcard(self.request_log_key)  # Request count

            # Execute pipeline to get current state
            results = pipe.execute()
            current_requests = results[2]

            # Check request rate limit
            if current_requests >= self.rate_limit_requests_per_min:
                logger.debug(f"Request rate limit exceeded: {current_requests}/{self.rate_limit_requests_per_min}")
                return False

            # Check MB rate limit if file size specified
            if file_size_mb > 0:
                # Get current MB usage by summing scores in the sorted set
                mb_entries = self.redis_client.zrangebyscore(
                    self.mb_log_key, window_start, current_time, withscores=True
                )
                current_mb = sum(score for _, score in mb_entries)

                if current_mb + file_size_mb > self.rate_limit_mb_per_min:
                    logger.debug(f"MB rate limit exceeded: {current_mb + file_size_mb}/{self.rate_limit_mb_per_min}")
                    return False

                # Add to MB log with file size as score
                self.redis_client.zadd(self.mb_log_key, {request_id: file_size_mb})

            # Add to request log
            self.redis_client.zadd(self.request_log_key, {request_id: current_time})

            # Set expiration on keys to prevent memory leaks
            self.redis_client.expire(self.mb_log_key, self.window_size_seconds * 2)
            self.redis_client.expire(self.request_log_key, self.window_size_seconds * 2)

            return True

        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Fallback to allowing the request if Redis fails
            return True

    def _acquire_slot_fallback(self, file_size_mb: float) -> bool:
        """File-based fallback rate limiting for local development."""
        current_time = time.time()

        # Read current state
        state = self._read_state()

        # Check if a minute has passed since last reset
        if current_time - state['last_reset'] >= self.window_size_seconds:
            state['total_mb'] = 0.0
            state['last_reset'] = current_time

        # Check if adding this file would exceed the limit
        if state['total_mb'] + file_size_mb <= self.rate_limit_mb_per_min:
            # We can process this file
            state['total_mb'] += file_size_mb
            self._write_state(state)
            return True

        return False

    def wait_for_slot(self, file_size_mb: float = 0.0, max_wait_seconds: int = 300):
        """
        Wait for a slot to become available, with timeout.

        Args:
            file_size_mb: Size of the file to process in MB
            max_wait_seconds: Maximum time to wait for a slot
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_seconds:
            if self.acquire_slot(file_size_mb):
                return True

            # Sleep with exponential backoff, max 5 seconds
            wait_time = min(1.0 + (time.time() - start_time) * 0.1, 5.0)
            time.sleep(wait_time)

        raise TimeoutError(f"Could not acquire rate limit slot within {max_wait_seconds} seconds")

    def get_current_usage(self) -> dict:
        """Get current rate limit usage statistics."""
        if self.use_redis:
            try:
                current_time = time.time()
                window_start = current_time - self.window_size_seconds

                # Clean old entries and get current counts
                self.redis_client.zremrangebyscore(self.mb_log_key, 0, window_start)
                self.redis_client.zremrangebyscore(self.request_log_key, 0, window_start)

                # Get current usage
                current_requests = self.redis_client.zcard(self.request_log_key)

                # Calculate current MB usage
                mb_entries = self.redis_client.zrangebyscore(
                    self.mb_log_key, window_start, current_time, withscores=True
                )
                current_mb = sum(score for _, score in mb_entries)

                return {
                    "requests": current_requests,
                    "requests_limit": self.rate_limit_requests_per_min,
                    "mb_used": round(current_mb, 2),
                    "mb_limit": self.rate_limit_mb_per_min,
                    "window_seconds": self.window_size_seconds,
                    "requests_remaining": max(0, self.rate_limit_requests_per_min - current_requests),
                    "mb_remaining": max(0, self.rate_limit_mb_per_min - current_mb)
                }
            except Exception as e:
                logger.error(f"Error getting usage stats: {e}")
                return {"error": str(e)}
        else:
            # Fallback mode
            state = self._read_state()
            current_time = time.time()

            # Reset if window expired
            if current_time - state['last_reset'] >= self.window_size_seconds:
                current_mb = 0.0
            else:
                current_mb = state['total_mb']

            return {
                "mb_used": round(current_mb, 2),
                "mb_limit": self.rate_limit_mb_per_min,
                "mb_remaining": max(0, self.rate_limit_mb_per_min - current_mb),
                "mode": "fallback"
            }

    def reset_limits(self):
        """Reset all rate limits (for testing purposes)."""
        if self.use_redis:
            try:
                self.redis_client.delete(self.mb_log_key, self.request_log_key)
                logger.info("Redis rate limit logs cleared")
            except Exception as e:
                logger.error(f"Error resetting Redis limits: {e}")
        else:
            # Reset file-based state
            self._write_state({
                'total_mb': 0.0,
                'last_reset': time.time()
            })

    def cleanup(self):
        """Clean up temporary files and optionally Redis keys."""
        if not self.use_redis:
            try:
                if hasattr(self, 'state_file') and self.state_file.exists():
                    self.state_file.unlink()
                    logger.debug("Cleaned up rate limiter state file")
            except Exception as e:
                logger.debug(f"Error cleaning up state file: {e}")
        # Note: We don't clean up Redis keys as they may be shared across instances
