#!/usr/bin/env python3
"""
Test ONLY the Chunking Worker component to ensure it works correctly.
This tests the new local retrieval and chunking workflow.
"""

import sys
import os
import time
import uuid
import tempfile
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_chunking_worker_initialization():
    """Test if chunking worker can be initialized."""
    print("🧪 TESTING CHUNKING WORKER INITIALIZATION")
    print("=" * 50)
    
    try:
        from chunking_worker import ChunkingWorker
        from config import ScraperConfig

        config = ScraperConfig()
        worker = ChunkingWorker("test_worker", config)
        
        print("✅ ChunkingWorker initialized successfully")
        print(f"   Worker ID: {worker.worker_id}")
        print(f"   Queue type: {'Redis' if hasattr(worker, 'queue_manager') else 'Local'}")
        
        return worker
        
    except Exception as e:
        print(f"❌ ChunkingWorker initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_file_size_check():
    """Test file size checking logic."""
    print("\n🧪 TESTING FILE SIZE CHECK LOGIC")
    print("=" * 50)
    
    try:
        from utils import get_file_size_mb
        
        # Create test files of different sizes
        test_files = []
        
        # Small file (< 3MB)
        small_content = "Small test file content\n" * 1000  # ~24KB
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(small_content)
            small_file = f.name
            test_files.append(small_file)
        
        small_size = get_file_size_mb(small_file)
        print(f"✅ Small file created: {small_size:.2f}MB")
        
        # Large file (> 3MB)
        large_content = "Large test file content with lots of text\n" * 100000  # ~4MB
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(large_content)
            large_file = f.name
            test_files.append(large_file)
        
        large_size = get_file_size_mb(large_file)
        print(f"✅ Large file created: {large_size:.2f}MB")
        
        # Test size check logic
        max_size = 3.0
        print(f"\n📏 Size check with {max_size}MB limit:")
        print(f"   Small file ({small_size:.2f}MB): {'✅ Within limit' if small_size <= max_size else '❌ Exceeds limit'}")
        print(f"   Large file ({large_size:.2f}MB): {'✅ Within limit' if large_size <= max_size else '❌ Exceeds limit'}")
        
        # Clean up
        for file_path in test_files:
            os.unlink(file_path)
        
        return True
        
    except Exception as e:
        print(f"❌ File size check test failed: {e}")
        return False

def test_chunking_logic():
    """Test the token-based chunking logic."""
    print("\n🧪 TESTING TOKEN-BASED CHUNKING LOGIC")
    print("=" * 50)

    try:
        from token_chunking import chunk_and_retain_file_by_tokens, count_tokens_in_file, count_tokens_in_text
        from utils import get_file_size_mb
        
        # Create a large test file with many tokens
        large_content = "This is a test line for token-based chunking with additional content.\n" * 50000  # ~3.5M tokens
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(large_content)
            test_file = f.name

        original_size = get_file_size_mb(test_file)
        original_tokens = count_tokens_in_file(test_file)
        print(f"📄 Created test file: {original_tokens:,} tokens ({original_size:.2f}MB)")

        # Test token-based chunking
        max_tokens = 850000  # 850K tokens per chunk
        max_chunks = 3

        print(f"🔄 Token chunking with {max_tokens:,} tokens per chunk, max {max_chunks} chunks...")
        chunk_paths = chunk_and_retain_file_by_tokens(test_file, max_tokens, max_chunks)
        
        print(f"✅ Chunking completed: {len(chunk_paths)} chunks created")
        
        # Verify chunks
        total_chunk_tokens = 0
        total_chunk_size = 0
        for i, chunk_path in enumerate(chunk_paths):
            if os.path.exists(chunk_path):
                chunk_tokens = count_tokens_in_file(chunk_path)
                chunk_size = get_file_size_mb(chunk_path)
                total_chunk_tokens += chunk_tokens
                total_chunk_size += chunk_size
                print(f"   Chunk {i+1}: {chunk_tokens:,} tokens ({chunk_size:.2f}MB)")

                # Verify chunk doesn't exceed token limit
                if chunk_tokens > max_tokens:
                    print(f"   ❌ Chunk {i+1} exceeds token limit!")
                    return False

                # Clean up chunk
                os.unlink(chunk_path)
            else:
                print(f"   ❌ Chunk {i+1}: File not found")
                return False

        print(f"📊 Total chunk tokens: {total_chunk_tokens:,}")
        print(f"📊 Total chunk size: {total_chunk_size:.2f}MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Chunking logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_queue_integration():
    """Test queue integration for chunking worker."""
    print("\n🧪 TESTING QUEUE INTEGRATION")
    print("=" * 50)
    
    try:
        from queue_manager import get_queue_manager, FileQueueItem
        from config import ScraperConfig

        config = ScraperConfig()
        queue_manager = get_queue_manager(config.REDIS_URL)
        
        # Get the repomix validated queue
        repomix_validated_queue = queue_manager.get_repomix_validated_queue()
        file_queue = queue_manager.get_file_queue()
        
        print("✅ Queue manager initialized")
        print(f"   Queue type: {'Redis' if queue_manager.is_redis() else 'Local'}")
        
        # Test adding item to repomix validated queue
        test_item = FileQueueItem(
            file_url="https://example.com/test-file.md",
            repository_name="test/repo",
            job_id=str(uuid.uuid4())
        )
        
        repomix_validated_queue.put(test_item)
        print("✅ Added test item to repomix_validated_queue")
        
        # Test retrieving item
        retrieved_item = repomix_validated_queue.get(timeout=5)
        if retrieved_item:
            print("✅ Retrieved item from repomix_validated_queue")
            print(f"   Repository: {getattr(retrieved_item, 'repository_name', 'Unknown')}")
            
            # Mark as completed
            repomix_validated_queue.task_done(retrieved_item)
            print("✅ Task marked as completed")
        else:
            print("❌ No item retrieved from queue")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Queue integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test chunking worker component."""
    print("🔧 TESTING CHUNKING WORKER COMPONENT")
    print("=" * 60)
    print("TESTING:")
    print("1. Chunking worker initialization")
    print("2. File size check logic")
    print("3. Chunking logic")
    print("4. Queue integration")
    print()
    
    # Test chunking worker initialization
    worker = test_chunking_worker_initialization()
    if not worker:
        print("\n❌ CHUNKING WORKER TESTS FAILED!")
        print("❌ Worker initialization failed")
        return False
    
    # Test file size check
    size_check_success = test_file_size_check()
    if not size_check_success:
        print("\n❌ CHUNKING WORKER TESTS FAILED!")
        print("❌ File size check failed")
        return False
    
    # Test chunking logic
    chunking_success = test_chunking_logic()
    if not chunking_success:
        print("\n❌ CHUNKING WORKER TESTS FAILED!")
        print("❌ Chunking logic failed")
        return False
    
    # Test queue integration
    queue_success = test_queue_integration()
    if not queue_success:
        print("\n❌ CHUNKING WORKER TESTS FAILED!")
        print("❌ Queue integration failed")
        return False
    
    # Final assessment
    print("\n🎉 ALL CHUNKING WORKER TESTS PASSED!")
    print("✅ Component is working correctly")
    print("✅ Worker initialization working")
    print("✅ File size check logic working")
    print("✅ Chunking logic working")
    print("✅ Queue integration working")
    print()
    print("🚀 READY FOR INTEGRATION:")
    print("   - Chunking worker can process files from repomix_validated_queue")
    print("   - Size check determines if chunking is needed")
    print("   - Files >3MB are chunked locally and re-uploaded")
    print("   - Files ≤3MB are queued directly for LLM")
    print("   - New workflow: Repomix → Upload Full → Chunking Worker → LLM")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
