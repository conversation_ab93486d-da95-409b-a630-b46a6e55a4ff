"""
Sentry data analyzer for automatic log analysis and insights.
"""
import os
import requests
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional


class SentryAnalyzer:
    """Analyzer for Sentry data with automatic insights generation."""
    
    def __init__(self):
        self.base_url = "https://de.sentry.io/api/0"
        self.headers = {
            "Authorization": f"Bearer {os.getenv('SENTRY_AUTH_TOKEN', '')}",
            "Content-Type": "application/json"
        }
        
    def get_recent_issues(self, organization_slug: str, project_slug: str, hours_back: int = 2) -> List[Dict]:
        """Fetch recent issues from Sentry."""
        try:
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            url = f"{self.base_url}/projects/{organization_slug}/{project_slug}/issues/"
            params = {
                "statsPeriod": f"{hours_back}h",
                "query": f"timestamp:>{start_time.isoformat()}",
                "sort": "date",
                "limit": 50
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to fetch Sentry issues: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Error fetching Sentry issues: {e}")
            return []
    
    def get_recent_events(self, organization_slug: str, project_slug: str, hours_back: int = 2) -> List[Dict]:
        """Fetch recent events from Sentry."""
        try:
            url = f"{self.base_url}/projects/{organization_slug}/{project_slug}/events/"
            params = {
                "statsPeriod": f"{hours_back}h",
                "full": "true",
                "limit": 100
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to fetch Sentry events: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Error fetching Sentry events: {e}")
            return []
    
    def analyze_patterns(self, issues: List[Dict], events: List[Dict]) -> Dict[str, Any]:
        """Analyze patterns in Sentry data."""
        analysis = {
            "total_issues": len(issues),
            "total_events": len(events),
            "error_patterns": {},
            "rate_limit_events": 0,
            "api_errors": 0,
            "network_errors": 0,
            "most_common_errors": [],
            "error_frequency": {},
            "recommendations": []
        }
        
        # Analyze issues
        error_counts = {}
        for issue in issues:
            error_type = issue.get('type', 'unknown')
            title = issue.get('title', 'unknown')
            count = issue.get('count', 1)
            
            if error_type not in error_counts:
                error_counts[error_type] = 0
            error_counts[error_type] += count
            
            # Check for specific error patterns
            if 'rate limit' in title.lower() or '429' in title:
                analysis["rate_limit_events"] += count
            elif 'api' in title.lower() or 'http' in title.lower():
                analysis["api_errors"] += count
            elif 'network' in title.lower() or 'connection' in title.lower():
                analysis["network_errors"] += count
        
        # Sort errors by frequency
        analysis["most_common_errors"] = sorted(
            error_counts.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        analysis["error_frequency"] = error_counts
        
        # Analyze events for additional patterns
        event_patterns = {}
        for event in events:
            event_type = event.get('type', 'unknown')
            if event_type not in event_patterns:
                event_patterns[event_type] = 0
            event_patterns[event_type] += 1
        
        analysis["event_patterns"] = event_patterns
        
        return analysis
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Rate limiting recommendations
        if analysis["rate_limit_events"] > 10:
            recommendations.append(
                f"🚨 HIGH RATE LIMITING: {analysis['rate_limit_events']} rate limit events detected. "
                "Consider reducing concurrent workers or increasing delays between requests."
            )
        elif analysis["rate_limit_events"] > 0:
            recommendations.append(
                f"⚠️ Rate limiting detected: {analysis['rate_limit_events']} events. Monitor API usage patterns."
            )
        
        # API error recommendations
        if analysis["api_errors"] > 5:
            recommendations.append(
                f"🔧 API ERRORS: {analysis['api_errors']} API-related errors. "
                "Check API endpoint URLs, authentication, and request formats."
            )
        
        # Network error recommendations
        if analysis["network_errors"] > 5:
            recommendations.append(
                f"🌐 NETWORK ISSUES: {analysis['network_errors']} network-related errors. "
                "Check internet connectivity and consider implementing better retry logic."
            )
        
        # General error frequency recommendations
        if analysis["total_issues"] > 20:
            recommendations.append(
                f"📊 HIGH ERROR VOLUME: {analysis['total_issues']} total issues. "
                "Review error patterns and implement preventive measures."
            )
        
        # Most common error recommendations
        if analysis["most_common_errors"]:
            top_error = analysis["most_common_errors"][0]
            recommendations.append(
                f"🎯 TOP ERROR: '{top_error[0]}' occurred {top_error[1]} times. "
                "Focus on fixing this error type first for maximum impact."
            )
        
        # Success recommendations
        if analysis["total_issues"] == 0:
            recommendations.append("✅ NO ERRORS: Clean run with no Sentry issues detected!")
        
        if not recommendations:
            recommendations.append("📈 System appears stable with minimal error activity.")
        
        return recommendations
    
    def generate_summary_report(self, analysis: Dict[str, Any]) -> str:
        """Generate a human-readable summary report."""
        report = []
        report.append("# Sentry Analysis Summary")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Overview
        report.append("## Overview")
        report.append(f"- Total Issues: {analysis['total_issues']}")
        report.append(f"- Total Events: {analysis['total_events']}")
        report.append(f"- Rate Limit Events: {analysis['rate_limit_events']}")
        report.append(f"- API Errors: {analysis['api_errors']}")
        report.append(f"- Network Errors: {analysis['network_errors']}")
        report.append("")
        
        # Top errors
        if analysis["most_common_errors"]:
            report.append("## Most Common Errors")
            for error_type, count in analysis["most_common_errors"][:5]:
                report.append(f"- {error_type}: {count} occurrences")
            report.append("")
        
        # Recommendations
        recommendations = self.get_recommendations(analysis)
        if recommendations:
            report.append("## Recommendations")
            for rec in recommendations:
                report.append(f"- {rec}")
            report.append("")
        
        return "\n".join(report)
