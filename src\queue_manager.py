#!/usr/bin/env python3
"""
Unified queue manager that automatically chooses between Redis and local queues.
For local development: Uses in-memory queues
For GCP production: Uses Redis/Cloud Tasks
"""

import os
import logging
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

# Try to import Redis, fall back to local if not available
try:
    import redis
    from redis_queue import QueueManager as RedisQueueManager, RepositoryQueueItem, FileQueueItem
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from local_queue import LocalQueueManager, RepositoryQueueItem, FileQueueItem

class UnifiedQueueManager:
    """
    Unified queue manager that automatically chooses the best queue implementation.
    
    Priority:
    1. Redis (if available and configured)
    2. Local in-memory queues (for development)
    """
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379')
        self._manager = None
        self._queue_type = None
        
        self._initialize_queue_manager()
    
    def _initialize_queue_manager(self):
        """Initialize the appropriate queue manager."""
        
        # Try Redis first if available
        if REDIS_AVAILABLE and self._try_redis():
            logger.info(f"🌐 Using Redis queue manager: {self.redis_url}")
            self._manager = RedisQueueManager(self.redis_url)
            self._queue_type = "redis"
            return
        
        # Fall back to local queues
        logger.info("🔧 Using local in-memory queue manager")
        self._manager = LocalQueueManager()
        self._queue_type = "local"
    
    def _try_redis(self) -> bool:
        """Test if Redis is available and working."""
        try:
            client = redis.from_url(self.redis_url, socket_timeout=5)
            client.ping()
            return True
        except Exception as e:
            logger.debug(f"Redis not available: {e}")
            return False
    
    def get_repository_queue(self):
        """Get the repository processing queue."""
        return self._manager.get_repository_queue()
    
    def get_file_queue(self):
        """Get the file processing queue."""
        return self._manager.get_file_queue()

    def get_repomix_validated_queue(self):
        """Get the repomix validated queue for chunking workflow."""
        return self._manager.get_repomix_validated_queue()
    
    def get_queue_type(self) -> str:
        """Get the type of queue manager being used."""
        return self._queue_type
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        if hasattr(self._manager, 'get_all_stats'):
            stats = self._manager.get_all_stats()
        else:
            stats = {"queue_type": self._queue_type}

        stats["queue_manager_type"] = self._queue_type
        return stats

    def get_all_stats(self) -> Dict[str, Any]:
        """Get all queue statistics (alias for get_stats for compatibility)."""
        return self.get_stats()
    
    def is_redis(self) -> bool:
        """Check if using Redis queues."""
        return self._queue_type == "redis"
    
    def is_local(self) -> bool:
        """Check if using local queues."""
        return self._queue_type == "local"

# Global instance
_unified_queue_manager = None

def get_queue_manager(redis_url: Optional[str] = None) -> UnifiedQueueManager:
    """Get the global unified queue manager instance."""
    global _unified_queue_manager
    if _unified_queue_manager is None:
        _unified_queue_manager = UnifiedQueueManager(redis_url)
    return _unified_queue_manager

def create_queue_manager(redis_url: Optional[str] = None) -> UnifiedQueueManager:
    """Create a new unified queue manager instance."""
    return UnifiedQueueManager(redis_url)

# For backward compatibility, export the queue item classes
__all__ = [
    'UnifiedQueueManager',
    'get_queue_manager', 
    'create_queue_manager',
    'RepositoryQueueItem',
    'FileQueueItem'
]
