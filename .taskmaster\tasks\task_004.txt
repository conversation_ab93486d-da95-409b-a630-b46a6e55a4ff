# Task ID: 4
# Title: Implement Size Check Before LLM Processing
# Status: pending
# Dependencies: 2 (Not found)
# Priority: medium
# Description: Add size validation step before LLM processing. If file > 3MB, trigger local retrieval and chunking process.
# Details:
Current implementation chunks during repomix but PRD requires size check before LLM analysis. Implementation: Size check occurs within new chunking_worker immediately after downloading repomix output from Supabase storage. Pipeline flow: Repomix Worker → Repomix Validated Queue → Chunking Worker (size check) → LLM Queue → LLM Worker.

# Test Strategy:
Test size validation logic, verify chunking trigger for large files
