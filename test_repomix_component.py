#!/usr/bin/env python3
"""
Test ONLY the repomix component to ensure it:
1. Runs npx repomix correctly
2. Generates output files
3. Applies your chunking rules if needed
"""

import sys
import os
import subprocess
import tempfile
import shutil
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_npx_availability():
    """Test that npx is available."""
    print("🧪 TESTING NPX AVAILABILITY")
    print("=" * 50)
    
    try:
        npx_path = shutil.which('npx')
        if npx_path:
            print(f"✅ NPX found at: {npx_path}")
            
            # Test npx version
            result = subprocess.run([npx_path, '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ NPX version: {result.stdout.strip()}")
                return True
            else:
                print(f"❌ NPX version check failed: {result.stderr}")
                return False
        else:
            print("❌ NPX not found in PATH")
            return False
            
    except Exception as e:
        print(f"❌ NPX test failed: {e}")
        return False

def test_repomix_command():
    """Test repomix command with a simple repository."""
    print("\n🧪 TESTING REPOMIX COMMAND")
    print("=" * 50)
    
    try:
        # Test with a small public repository
        test_repo = "microsoft/TypeScript-Node-Starter"  # Small, well-known repo
        
        print(f"📦 Testing repomix with: {test_repo}")
        
        # Build repomix command
        npx_path = shutil.which('npx')
        cmd = [
            npx_path, 'repomix', '--remote', test_repo,
            '--include', '**/*.py,**/*.js,**/*.ts,**/*.md',
            '--output', 'test_repomix_output.md'
        ]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        
        # Run repomix with timeout
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=120,  # 2 minute timeout
            encoding='utf-8', 
            errors='replace'
        )
        
        print(f"📊 Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ Repomix command executed successfully")
            
            # Check if output file was created
            if os.path.exists('test_repomix_output.md'):
                file_size = os.path.getsize('test_repomix_output.md') / (1024 * 1024)
                print(f"✅ Output file created: test_repomix_output.md ({file_size:.2f} MB)")
                
                # Read first few lines to verify content
                with open('test_repomix_output.md', 'r', encoding='utf-8', errors='replace') as f:
                    first_lines = f.read(500)
                    # Check for repomix indicators
                    if 'repomix' in first_lines.lower() or 'merged representation' in first_lines.lower():
                        print("✅ Output file contains valid repomix content")

                        # Cleanup (try to remove, but don't fail if locked)
                        try:
                            os.remove('test_repomix_output.md')
                        except OSError:
                            pass  # File might be locked on Windows
                        return True
                    else:
                        print("❌ Output file doesn't contain expected repomix content")
                        print(f"First 500 chars: {first_lines}")
                        return False
            else:
                print("❌ Output file was not created")
                return False
        else:
            print(f"❌ Repomix command failed")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Repomix command timed out (>2 minutes)")
        return False
    except Exception as e:
        print(f"❌ Repomix test failed: {e}")
        return False

def test_repomix_with_chunking():
    """Test repomix with chunking for larger repositories."""
    print("\n🧪 TESTING REPOMIX WITH CHUNKING")
    print("=" * 50)
    
    try:
        from utils import chunk_and_retain_file, get_file_size_mb
        
        # Create a large fake repomix output to test chunking
        large_content = "# Large Repository Analysis\n\n" + ("This is test content. " * 200000)  # ~4MB
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
            f.write(large_content)
            test_file = f.name
        
        print(f"📁 Created test file: {test_file}")
        file_size = get_file_size_mb(test_file)
        print(f"📊 File size: {file_size:.2f} MB")
        
        if file_size <= 3:
            print("❌ Test file should be > 3MB for chunking test")
            return False
        
        print("🔪 Testing chunking on repomix output...")
        chunks = chunk_and_retain_file(test_file, max_size_mb=3, max_chunks=3)
        
        print(f"📊 Chunks created: {len(chunks)}")
        
        if len(chunks) == 0:
            print("❌ No chunks created")
            return False
        
        # Verify chunks
        for i, chunk_path in enumerate(chunks):
            if os.path.exists(chunk_path):
                chunk_size = get_file_size_mb(chunk_path)
                print(f"   Chunk {i+1}: {chunk_size:.2f} MB")
                
                # Cleanup
                os.remove(chunk_path)
            else:
                print(f"❌ Chunk {i+1} file not found: {chunk_path}")
                return False
        
        print("✅ Repomix chunking test passed")
        return True
        
    except Exception as e:
        print(f"❌ Repomix chunking test failed: {e}")
        return False

def main():
    """Test repomix component."""
    print("🔧 TESTING REPOMIX COMPONENT")
    print("=" * 60)
    print("TESTING:")
    print("1. NPX availability")
    print("2. Repomix command execution")
    print("3. Chunking integration")
    print()
    
    success1 = test_npx_availability()
    success2 = test_repomix_command() if success1 else False
    success3 = test_repomix_with_chunking()
    
    if success1 and success2 and success3:
        print("\n🎉 ALL REPOMIX TESTS PASSED!")
        print("✅ Component is working correctly")
        print("✅ NPX available")
        print("✅ Repomix command working")
        print("✅ Chunking integration working")
        return True
    else:
        print("\n❌ REPOMIX TESTS FAILED!")
        print("❌ Component needs fixing")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
