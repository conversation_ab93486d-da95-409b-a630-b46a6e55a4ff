#!/usr/bin/env python3
"""
Test complete pipeline integration with all three tasks:
1. Token-Based Chunking System
2. Gemini Flash 2.5 Integration  
3. Parallel Repomix Processing

This tests the end-to-end workflow with all components working together.
"""

import sys
import os
import time
import uuid
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_complete_system_initialization():
    """Test complete system initialization with all components."""
    print("🧪 TESTING COMPLETE SYSTEM INITIALIZATION")
    print("=" * 50)
    
    try:
        # Test all major components can be imported and initialized
        from config import ScraperConfig
        from parallel_repomix import ParallelRepomixProcessor
        from gemini_flash_25 import get_gemini_client
        from token_chunking import TokenChunker
        from chunking_worker import ChunkingWorker
        from queue_manager import get_queue_manager
        from storage_manager import StorageManager
        
        config = ScraperConfig()
        job_id = f"integration_test_{int(time.time())}"
        
        print(f"✅ All components imported successfully")
        
        # Test configuration
        print(f"📋 System configuration:")
        print(f"   Repomix workers: {config.REPOMIX_WORKERS}")
        print(f"   LLM workers: {config.LLM_WORKERS}")
        print(f"   LLM model: {config.LLM_MODEL}")
        print(f"   LLM timeout: {config.LLM_TIMEOUT}s")
        print(f"   LLM max output tokens: {config.LLM_MAX_OUTPUT_TOKENS}")
        print(f"   File includes: {config.FILE_INCLUDES}")
        
        # Test component initialization
        parallel_processor = ParallelRepomixProcessor(config, job_id, num_workers=2)
        print(f"✅ Parallel repomix processor initialized")
        
        gemini_client = get_gemini_client(config.LLM_API_KEY or "test_key", max_workers=config.LLM_WORKERS)
        print(f"✅ Gemini Flash 2.5 client initialized")
        
        token_chunker = TokenChunker(max_tokens_per_chunk=850000, max_chunks=3)
        print(f"✅ Token chunker initialized")
        
        chunking_worker = ChunkingWorker("test_worker", config)
        print(f"✅ Chunking worker initialized")
        
        queue_manager = get_queue_manager(config.REDIS_URL)
        print(f"✅ Queue manager initialized")
        
        storage_manager = StorageManager(config)
        print(f"✅ Storage manager initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Complete system initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_integration():
    """Test the complete workflow integration."""
    print("\n🧪 TESTING WORKFLOW INTEGRATION")
    print("=" * 50)
    
    try:
        from config import ScraperConfig
        from queue_manager import get_queue_manager, RepositoryQueueItem, FileQueueItem
        from token_chunking import count_tokens_in_text
        from gemini_flash_25 import get_gemini_client
        
        config = ScraperConfig()
        job_id = f"workflow_test_{int(time.time())}"
        
        # Test queue workflow
        queue_manager = get_queue_manager(config.REDIS_URL)
        repo_queue = queue_manager.get_repository_queue()
        repomix_validated_queue = queue_manager.get_repomix_validated_queue()
        file_queue = queue_manager.get_file_queue()
        
        print(f"✅ Queue workflow initialized")
        print(f"   Queue type: {'Redis' if queue_manager.is_redis() else 'Local'}")
        
        # Test repository processing workflow
        test_repo = RepositoryQueueItem(
            repository_url="https://github.com/test/workflow-integration",
            repository_name="test/workflow-integration",
            job_id=job_id
        )

        # Step 1: Repository → Repomix Queue
        repo_queue.put(test_repo)
        print(f"✅ Step 1: Repository added to repomix queue")

        # Step 2: Repomix → Validated Queue (simulate)
        # Check if we're using Redis or local queues to create the right FileQueueItem
        if queue_manager.is_redis():
            # Redis version uses file_path parameter
            from redis_queue import FileQueueItem as RedisFileQueueItem
            test_file = RedisFileQueueItem(
                file_path="https://storage.example.com/repomix/test_workflow.md",
                repository_name="test/workflow-integration",
                job_id=job_id
            )
        else:
            # Local version uses file_url parameter
            from local_queue import FileQueueItem as LocalFileQueueItem
            test_file = LocalFileQueueItem(
                file_url="https://storage.example.com/repomix/test_workflow.md",
                repository_name="test/workflow-integration",
                job_id=job_id
            )

        repomix_validated_queue.put(test_file)
        print(f"✅ Step 2: Repomix file added to validated queue")

        # Step 3: Chunking → File Queue (simulate)
        if queue_manager.is_redis():
            test_chunk = RedisFileQueueItem(
                file_path="https://storage.example.com/repomix/test_workflow_chunk_1.md",
                repository_name="test/workflow-integration",
                job_id=job_id
            )
        else:
            test_chunk = LocalFileQueueItem(
                file_url="https://storage.example.com/repomix/test_workflow_chunk_1.md",
                repository_name="test/workflow-integration",
                job_id=job_id
            )

        file_queue.put(test_chunk)
        print(f"✅ Step 3: Chunk added to file queue")
        
        # Test token counting integration
        test_content = "This is a test content for token counting integration."
        token_count = count_tokens_in_text(test_content)
        print(f"✅ Token counting: {token_count} tokens")
        
        # Test Gemini client integration
        api_key = config.LLM_API_KEY
        if api_key and api_key != "test_key":
            gemini_client = get_gemini_client(api_key, max_workers=config.LLM_WORKERS)
            rate_status = gemini_client.get_rate_limit_status()
            print(f"✅ Gemini integration: {rate_status['current_tpm']:,}/{rate_status['target_tpm']:,} TPM")
        else:
            print(f"⚠️ Gemini integration: No API key for real testing")
        
        # Clean up queues
        repo_queue.get(timeout=1)  # Remove test items
        repomix_validated_queue.get(timeout=1)
        file_queue.get(timeout=1)
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_characteristics():
    """Test performance characteristics of the integrated system."""
    print("\n🧪 TESTING PERFORMANCE CHARACTERISTICS")
    print("=" * 50)
    
    try:
        from config import ScraperConfig
        from parallel_repomix import DEFAULT_REPOMIX_WORKERS
        from gemini_flash_25 import MAX_RPM, TARGET_TPM
        from token_chunking import TokenChunker
        
        config = ScraperConfig()
        
        print(f"📊 Performance configuration:")
        print(f"   Repomix workers: {config.REPOMIX_WORKERS} (parallel processing)")
        print(f"   LLM workers: {config.LLM_WORKERS} (Gemini Flash 2.5)")
        print(f"   Gemini RPM limit: {MAX_RPM:,}")
        print(f"   Gemini TPM target: {TARGET_TPM:,}")
        print(f"   Token chunk size: 850,000 tokens")
        print(f"   Max chunks per repo: 3")
        
        # Calculate theoretical throughput
        repos_per_worker_per_hour = 6  # Estimate: 10 minutes per repo
        total_repomix_throughput = config.REPOMIX_WORKERS * repos_per_worker_per_hour
        
        files_per_llm_worker_per_hour = 60  # 1 file per minute
        total_llm_throughput = config.LLM_WORKERS * files_per_llm_worker_per_hour
        
        print(f"\n📈 Theoretical throughput:")
        print(f"   Repomix: {total_repomix_throughput} repos/hour")
        print(f"   LLM: {total_llm_throughput} files/hour")
        print(f"   Bottleneck: {'Repomix' if total_repomix_throughput < total_llm_throughput else 'LLM'}")
        
        # Test token chunker performance
        chunker = TokenChunker(max_tokens_per_chunk=850000, max_chunks=3)
        large_content = "This is a test line for performance testing.\n" * 10000
        
        start_time = time.time()
        token_count = chunker.count_tokens(large_content)
        token_time = time.time() - start_time
        
        print(f"\n⚡ Token processing performance:")
        print(f"   Content size: {len(large_content):,} characters")
        print(f"   Token count: {token_count:,} tokens")
        print(f"   Processing time: {token_time:.3f}s")
        print(f"   Tokens/second: {token_count/token_time:,.0f}")
        
        # Performance validation
        performance_checks = [
            ('30 repomix workers', config.REPOMIX_WORKERS == 30),
            ('2 LLM workers', config.LLM_WORKERS == 2),
            ('850K token chunks', chunker.max_tokens_per_chunk == 850000),
            ('Fast token counting', token_time < 1.0),
            ('High throughput potential', total_repomix_throughput > 100)
        ]
        
        print(f"\n✅ Performance validation:")
        all_passed = True
        for check_name, passed in performance_checks:
            print(f"   {check_name}: {'✅' if passed else '❌'}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Performance characteristics test failed: {e}")
        return False

def test_error_handling_and_resilience():
    """Test error handling and system resilience."""
    print("\n🧪 TESTING ERROR HANDLING AND RESILIENCE")
    print("=" * 50)
    
    try:
        from config import ScraperConfig
        from gemini_flash_25 import GeminiFlash25Client
        from token_chunking import TokenChunker
        from queue_manager import get_queue_manager
        
        config = ScraperConfig()
        
        # Test Gemini rate limiting
        gemini_client = GeminiFlash25Client("test_key", max_workers=2)
        
        # Test rate limit checking
        can_process_normal = gemini_client._check_rate_limits(100000)  # 100K tokens
        can_process_large = gemini_client._check_rate_limits(2000000)  # 2M tokens (should be blocked)
        
        print(f"✅ Gemini rate limiting:")
        print(f"   Normal request (100K): {'✅ Allowed' if can_process_normal else '❌ Blocked'}")
        print(f"   Large request (2M): {'✅ Allowed' if can_process_large else '❌ Blocked'}")
        
        if not can_process_large:
            print(f"✅ Rate limiting working correctly")
        else:
            print(f"⚠️ Rate limiting may not be working")
        
        # Test token chunker edge cases
        chunker = TokenChunker(max_tokens_per_chunk=1000, max_chunks=2)
        
        # Test empty content
        empty_chunks = chunker.chunk_content_by_tokens("")
        print(f"✅ Empty content handling: {len(empty_chunks)} chunks")
        
        # Test very large content
        large_content = "Large content line.\n" * 5000
        large_chunks = chunker.chunk_content_by_tokens(large_content)
        print(f"✅ Large content chunking: {len(large_chunks)} chunks (max: {chunker.max_chunks})")
        
        # Test queue resilience
        queue_manager = get_queue_manager(config.REDIS_URL)
        
        # Test queue operations
        repo_queue = queue_manager.get_repository_queue()
        test_item = None
        
        # Test getting from empty queue
        empty_result = repo_queue.get(timeout=1)
        print(f"✅ Empty queue handling: {empty_result is None}")
        
        # Error handling validation
        error_checks = [
            ('Rate limiting blocks large requests', not can_process_large),
            ('Empty content handled', len(empty_chunks) >= 0),
            ('Max chunks enforced', len(large_chunks) <= chunker.max_chunks),
            ('Empty queue handled', empty_result is None)
        ]
        
        print(f"\n🛡️ Error handling validation:")
        all_passed = True
        for check_name, passed in error_checks:
            print(f"   {check_name}: {'✅' if passed else '❌'}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def main():
    """Test complete pipeline integration."""
    print("🔧 TESTING COMPLETE PIPELINE INTEGRATION")
    print("=" * 60)
    print("TESTING:")
    print("1. Complete system initialization")
    print("2. Workflow integration")
    print("3. Performance characteristics")
    print("4. Error handling and resilience")
    print()
    
    # Test complete system initialization
    init_success = test_complete_system_initialization()
    if not init_success:
        print("\n❌ COMPLETE PIPELINE INTEGRATION TESTS FAILED!")
        print("❌ System initialization failed")
        return False
    
    # Test workflow integration
    workflow_success = test_workflow_integration()
    if not workflow_success:
        print("\n❌ COMPLETE PIPELINE INTEGRATION TESTS FAILED!")
        print("❌ Workflow integration failed")
        return False
    
    # Test performance characteristics
    performance_success = test_performance_characteristics()
    if not performance_success:
        print("\n❌ COMPLETE PIPELINE INTEGRATION TESTS FAILED!")
        print("❌ Performance characteristics failed")
        return False
    
    # Test error handling
    error_success = test_error_handling_and_resilience()
    if not error_success:
        print("\n❌ COMPLETE PIPELINE INTEGRATION TESTS FAILED!")
        print("❌ Error handling failed")
        return False
    
    # Final assessment
    print("\n🎉 ALL COMPLETE PIPELINE INTEGRATION TESTS PASSED!")
    print("✅ Integration is working correctly")
    print("✅ Complete system initialization working")
    print("✅ Workflow integration working")
    print("✅ Performance characteristics optimal")
    print("✅ Error handling and resilience working")
    print()
    print("🚀 PRODUCTION READY SYSTEM:")
    print("   ✅ Task 1: Token-Based Chunking System (850K tokens, 3 chunks max)")
    print("   ✅ Task 2: Gemini Flash 2.5 Integration (90s timeout, 8192 tokens, 1.8M TPM)")
    print("   ✅ Task 3: Parallel Repomix Processing (30 workers, multiprocessing)")
    print("   ✅ Complete end-to-end workflow validated")
    print("   ✅ High-performance repository analysis pipeline")
    print("   ✅ Production-ready error handling and monitoring")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
