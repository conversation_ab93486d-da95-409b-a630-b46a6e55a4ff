"""
Sentry configuration and instrumentation for repository research tool.
"""
import os
import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
import logging


def init_sentry():
    """Initialize Sentry SDK with proper configuration."""
    sentry_dsn = os.getenv('SENTRY_DSN')

    if not sentry_dsn or sentry_dsn == 'your_sentry_dsn_here':
        print("⚠️ SENTRY_DSN not configured. Sentry monitoring disabled.")
        return False
    
    # Configure logging integration
    logging_integration = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )
    
    try:
        sentry_sdk.init(
            dsn=sentry_dsn,
            # Enable tracing for performance monitoring
            traces_sample_rate=1.0,  # 100% sampling for development/testing
            # Enable profiling
            profiles_sample_rate=1.0,
            # Add integrations
            integrations=[logging_integration],
            # Environment and release info
            environment=os.getenv('ENVIRONMENT', 'development'),
            release=os.getenv('RELEASE', 'repository-research-tool@1.0.0'),
            # Additional options
            send_default_pii=False,  # Don't send personally identifiable information
            debug=os.getenv('SENTRY_DEBUG', 'false').lower() == 'true',
            # Custom tags
            before_send=add_custom_tags,
        )
        
        print("✅ Sentry monitoring initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize Sentry: {e}")
        return False


def add_custom_tags(event, hint):
    """Add custom tags to Sentry events."""
    event.setdefault('tags', {}).update({
        'component': 'repository-research-tool',
        'worker_type': os.getenv('WORKER_TYPE', 'unknown'),
        'process_id': str(os.getpid()),
    })
    return event


def trace_function(operation_name):
    """Decorator to trace function execution with Sentry."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(name=operation_name, op="function"):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    sentry_sdk.capture_exception(e)
                    raise
        return wrapper
    return decorator


def trace_api_call(api_name, repo_name=None):
    """Context manager for tracing API calls."""
    class APITracer:
        def __init__(self, api_name, repo_name=None):
            self.api_name = api_name
            self.repo_name = repo_name
            self.transaction = None
            
        def __enter__(self):
            transaction_name = f"{self.api_name}"
            if self.repo_name:
                transaction_name += f" - {self.repo_name}"
                
            self.transaction = sentry_sdk.start_transaction(
                name=transaction_name,
                op="api_call"
            )
            self.transaction.__enter__()
            
            # Add context
            sentry_sdk.set_tag("api_name", self.api_name)
            if self.repo_name:
                sentry_sdk.set_tag("repository", self.repo_name)
                
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            if exc_type:
                sentry_sdk.capture_exception(exc_val)
            self.transaction.__exit__(exc_type, exc_val, exc_tb)
            
        def set_status(self, status_code, error_msg=None):
            """Set the status of the API call."""
            sentry_sdk.set_tag("status_code", str(status_code))
            if error_msg:
                sentry_sdk.set_context("error_details", {"message": error_msg})
                
        def add_breadcrumb(self, message, category="api", level="info"):
            """Add a breadcrumb to track API call progress."""
            sentry_sdk.add_breadcrumb(
                message=message,
                category=category,
                level=level
            )
    
    return APITracer(api_name, repo_name)


def log_worker_start(worker_type, worker_id):
    """Log worker startup with Sentry context."""
    sentry_sdk.set_tag("worker_type", worker_type)
    sentry_sdk.set_tag("worker_id", str(worker_id))
    sentry_sdk.add_breadcrumb(
        message=f"{worker_type} worker {worker_id} started",
        category="worker",
        level="info"
    )


def log_rate_limit_event(worker_id, repo_name, retry_delay, attempt):
    """Log rate limiting events for monitoring."""
    sentry_sdk.add_breadcrumb(
        message=f"Rate limited - Worker {worker_id}, Repo: {repo_name}, Delay: {retry_delay}s, Attempt: {attempt}",
        category="rate_limit",
        level="warning",
        data={
            "worker_id": worker_id,
            "repository": repo_name,
            "retry_delay": retry_delay,
            "attempt": attempt
        }
    )


def log_processing_metrics(phase, duration, count, success_count=None):
    """Log processing metrics for performance monitoring."""
    sentry_sdk.add_breadcrumb(
        message=f"{phase} completed - Duration: {duration:.2f}s, Count: {count}",
        category="metrics",
        level="info",
        data={
            "phase": phase,
            "duration": duration,
            "total_count": count,
            "success_count": success_count or count,
            "success_rate": (success_count or count) / count if count > 0 else 0
        }
    )
