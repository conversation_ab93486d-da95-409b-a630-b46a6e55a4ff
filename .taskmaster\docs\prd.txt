# Product Requirements Document (PRD)
## Repository Research Tool v2.0.0

### **Product Overview**
A high-performance repository research service that searches GitHub repositories by keywords, processes them with repomix, and generates comprehensive AI-powered analysis using Gemini Flash 2.5. Designed for cloud deployment with Redis queues, Supabase storage, and Sentry monitoring.

---

## **Core Workflow**

### **1. GitHub Repository Search**
- **API Search**: GitHub API search by keywords, sorted by last updated
- **Per-Keyword Processing**: Max repos per keyword (e.g., 60 repos per keyword separately)
- **Filtering**: Minimum stars threshold (e.g., 100+ stars)
- **Deduplication**: Remove duplicate repositories across keyword searches
- **File Includes**: `--include "**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md"`

### **2. Parallel Repomix Processing**
- **Command**: `npx repomix --remote <repo_url> --include <file_types> --output <file>`
- **Workers**: 30 workers with Python multiprocessing (not async)
- **Processing Rate**: 2 workers processing 1 file/min
- **No Cloning**: Use --remote flag to avoid local cloning
- **Cloud Storage**: Upload repomix outputs to Supabase storage

### **3. Token-Based Chunking**
- **Token Estimation**: Use tiktoken library for accurate token counting (not API calls)
- **Chunk Size**: 850K token chunks (not 3MB file chunks)
- **Gemini Limit**: Stay under 1.8M TPM limit for optimal utilization
- **Max Chunks**: Maximum 3 chunks per repository (first 3 only)
- **Batch Processing**: Dynamic worker allocation for 1.8M TPM utilization

### **4. AI Analysis with Gemini Flash 2.5**
- **Model**: Gemini 2.5 Flash (not Flash 2.0)
- **Rate Limits**: 1,000 RPM and 2,000,000 TPM (paid tier)
- **Timeouts**: 90s timeouts and 8192 token outputs
- **Prompt**: Comprehensive feature and functionality analysis (default)
- **Analysis**: "Exhaustive listing of all features with underlying functionalities"

### **5. Aggregated Analysis Output**
- **Format**: Single aggregated analysis file (JSON/Markdown)
- **Content**: Comprehensive implementation plans and feature analysis
- **Storage**: Save to Supabase storage and local output directory

---

## **Technical Architecture**

### **Unified Architecture**
- **worker.py**: Only starts/monitors standalone worker functions
- **service.py**: Endpoints only create jobs and queue tasks (not run pipelines directly)
- **Clean Separation**: Job orchestration vs execution separation

### **Queue System (Redis)**
- **Repository Queue**: Redis queue for GitHub search results
- **Repomix Queue**: Redis queue for repomix processing tasks
- **LLM Queue**: Redis queue for Gemini analysis tasks
- **Worker Independence**: Scalable across containers/VMs

### **Data Flow**
```
GitHub API Search (per keyword) → Deduplication → Repository Queue →
30 Repomix Workers (parallel) → Token Chunking (850K chunks) →
LLM Queue → 2 LLM Workers (1.8M TPM) → Analysis Storage →
Aggregated Report Generation
```

### **Storage & Services**
- **Database**: Supabase (jobs, repositories, analyses)
- **File Storage**: Supabase Storage (repomix files, chunks, reports)
- **Queue System**: Redis (distributed task management)
- **Monitoring**: Sentry for logging and error tracking
- **Deployment**: Containerized with Docker, configurable LLM providers

---

## **Key Features**

### **Intelligent Processing**
- ✅ Keyword-based repository discovery
- ✅ Automatic deduplication
- ✅ Size-aware chunking
- ✅ Parallel processing with workers

### **Scalability**
- ✅ Redis queue system for distributed processing
- ✅ Cloud-native storage
- ✅ Independent worker scaling
- ✅ Configurable processing limits

### **Reliability**
- ✅ Comprehensive logging throughout pipeline
- ✅ Error handling and retry mechanisms
- ✅ Component-based testing strategy
- ✅ Validation at each processing stage

---

## **Configuration Parameters**

### **Search Parameters**
- `keywords`: List of search terms
- `min_stars`: Minimum repository stars
- `max_repos_per_keyword`: Repository limit per keyword
- `file_includes`: File types for repomix processing

### **Processing Parameters**
- `max_file_size_mb`: Size threshold for chunking (3MB)
- `max_chunks`: Maximum chunks to retain (3)
- `chunk_size_mb`: Individual chunk size (3MB)

### **LLM Parameters**
- `model`: AI model for analysis
- `max_tokens`: Output token limit
- `temperature`: Analysis creativity level

---

## **Development Strategy**

### **Component-Based Testing**
Each component must be tested independently before integration:

1. **Chunking Component**: Validate 3MB chunking rules
2. **Repomix Component**: Test repository processing
3. **Supabase Component**: Verify database and storage operations
4. **LLM Component**: Validate AI analysis functionality
5. **Queue Component**: Test Redis queue operations
6. **Integration Testing**: End-to-end workflow validation

### **Validation Process**
- ✅ Unit tests for each component
- ✅ Integration tests for component interactions
- ✅ End-to-end workflow testing
- ✅ Performance and scalability testing

---

## **Success Metrics**

### **Functional Requirements**
- ✅ Successfully process repositories up to configured limits
- ✅ Maintain 3MB chunking compliance
- ✅ Generate comprehensive AI analysis reports
- ✅ Produce consolidated JSON output

### **Performance Requirements**
- ✅ Process multiple repositories concurrently
- ✅ Handle files of varying sizes efficiently
- ✅ Maintain system responsiveness under load
- ✅ Complete full workflow within reasonable timeframes

### **Quality Requirements**
- ✅ Comprehensive logging for debugging
- ✅ Error recovery and graceful degradation
- ✅ Data integrity throughout pipeline
- ✅ Consistent output format and quality

---

## **Implementation Phases**

### **Phase 1: Core Components** 
- [x] Chunking logic implementation
- [x] Repomix processing
- [x] Supabase integration
- [x] LLM integration

### **Phase 2: Queue System** 🔄 **CURRENT**
- [ ] Redis queue implementation
- [ ] Worker process architecture
- [ ] Local retrieval for chunking
- [ ] Queue monitoring and management

### **Phase 3: Integration & Testing**
- [ ] End-to-end workflow integration
- [ ] Comprehensive testing suite
- [ ] Performance optimization
- [ ] Documentation and deployment

### **Phase 4: Production Deployment**
- [ ] Cloud infrastructure setup
- [ ] Monitoring and alerting
- [ ] Scaling configuration
- [ ] Production validation

---

## **Risk Mitigation**

### **Technical Risks**
- **API Rate Limits**: Implement exponential backoff and retry logic
- **Large File Processing**: Robust chunking with size validation
- **Queue Failures**: Dead letter queues and error recovery
- **Storage Limits**: Cleanup policies and size monitoring

### **Operational Risks**
- **Cost Management**: Processing limits and budget controls
- **Data Quality**: Validation at each processing stage
- **System Reliability**: Health checks and monitoring
- **Scalability**: Load testing and capacity planning

---

This PRD serves as the blueprint for building a robust, scalable GitHub repository research tool that meets all specified requirements while maintaining high quality and reliability standards.
