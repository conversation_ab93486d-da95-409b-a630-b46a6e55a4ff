{"master": {"tasks": [{"id": "1", "title": "Implement Token-Based Chunking System", "description": "Replace 3MB file-based chunking with 850K token-based chunking using tiktoken library for proper Gemini API utilization.", "details": "Requirements: 1) Use tiktoken library for accurate token counting (not API calls), 2) Implement 850K token chunks instead of 3MB file chunks, 3) Stay under 1.8M TPM limit, 4) Maximum 3 chunks per repository, 5) Dynamic worker allocation for optimal TPM utilization. This is foundational for LLM processing pipeline.", "status": "pending", "priority": "high", "dependencies": [], "testStrategy": "Component test token counting accuracy, validate chunk sizes, test TPM utilization"}, {"id": "2", "title": "Implement Gemini Flash 2.5 Integration", "description": "Implement proper Gemini 2.5 Flash API integration with rate limiting and optimal configuration for repository analysis.", "details": "Requirements: 1) Gemini 2.5 Flash (not Flash 2.0), 2) 90s timeouts and 8192 token outputs, 3) Rate limits: 1,000 RPM and 2,000,000 TPM (paid tier), 4) 1.8M TPM utilization with 2 workers processing 1 file/min, 5) Comprehensive feature analysis prompts, 6) Error handling and retry logic.", "status": "pending", "priority": "high", "dependencies": ["1"], "testStrategy": "Test API integration, validate rate limiting, test comprehensive analysis output"}, {"id": "3", "title": "Implement Parallel Repomix Processing", "description": "Implement 30 parallel repomix workers with Python multiprocessing for high-throughput repository processing.", "details": "Requirements: 1) 30 workers with Python multiprocessing (not async), 2) npx repomix --remote processing without cloning, 3) File includes: --include '**/*.py,**/*.js,**/*.ts,**/*.go,**/*.rs,**/*.md', 4) Redis queue distribution, 5) Processing rate optimization, 6) Error handling and retry logic.", "status": "pending", "priority": "high", "dependencies": ["1"], "testStrategy": "Test parallel processing, validate worker scaling, test repomix command execution"}, {"id": "4", "title": "Implement GitHub API Search with Deduplication", "description": "Implement GitHub API search by keywords with per-keyword processing and repository deduplication.", "details": "Requirements: 1) GitHub API search sorted by last updated, 2) Per-keyword processing (max repos per keyword separately), 3) Minimum stars filtering (e.g., 100+ stars), 4) Repository deduplication across keywords, 5) File type filtering integration, 6) Rate limiting and error handling.", "status": "pending", "priority": "high", "dependencies": [], "testStrategy": "Test GitHub API integration, validate deduplication logic, test keyword processing"}, {"id": "5", "title": "Implement Unified Architecture (worker.py/service.py)", "description": "Implement clean separation between job orchestration and execution with unified architecture pattern.", "details": "Requirements: 1) worker.py only starts/monitors standalone worker functions, 2) service.py endpoints only create jobs and queue tasks (not run pipelines directly), 3) Clean separation between job orchestration and execution, 4) Redis queue integration, 5) Containerized deployment support, 6) Sentry monitoring integration.", "status": "pending", "priority": "medium", "dependencies": ["2", "3"], "testStrategy": "Test worker independence, validate service endpoints, test containerized deployment"}, {"id": "6", "title": "Implement Aggregated Analysis Output", "description": "Implement single aggregated analysis file generation with comprehensive feature analysis and implementation plans.", "details": "Requirements: 1) Single aggregated analysis file (JSON/Markdown), 2) Comprehensive implementation plans and feature analysis, 3) Exhaustive listing of all features with underlying functionalities, 4) Storage to Supabase and local output directory, 5) Integration with Sentry monitoring, 6) Final report consolidation logic.", "status": "pending", "priority": "medium", "dependencies": ["2", "5"], "testStrategy": "Validate aggregated output format, test comprehensive analysis quality, test storage integration"}], "metadata": {"version": "1.0.0", "created": "2025-01-26", "project": "GitHub Repository Research Tool", "totalTasks": 6}}}