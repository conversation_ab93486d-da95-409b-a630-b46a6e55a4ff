#!/usr/bin/env python3
"""
Gemini Flash 2.0 Integration for Repository Research Tool

Implements proper Gemini 2.0 Flash API integration with:
- 90s timeouts and 8192 token outputs
- Rate limits: 1,000 RPM and 3,000,000 TPM (targeting 3M TPM capacity)
- Comprehensive feature analysis prompts
- Error handling and retry logic

Key Features:
- Gemini 2.0 Flash (per Constitution v3)
- Optimal rate limiting for 3M TPM target
- Token-aware processing
- Comprehensive analysis prompts
- Production-ready error handling
"""

import os
import time
import logging
import requests
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass

from token_chunking import count_tokens_in_text

logger = logging.getLogger(__name__)

# Gemini Flash 2.0 Configuration (per Constitution v3)
GEMINI_20_FLASH_MODEL = "gemini-2.0-flash"
GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta"

# Rate Limits for Gemini 2.0 Flash (targeting 3M TPM capacity)
MAX_RPM = 1000  # Requests per minute
MAX_TPM = 3000000  # Tokens per minute (3M TPM target)
TARGET_TPM = 2500000  # Target 2.5M TPM for 3M capacity (83% utilization)

# API Configuration
DEFAULT_TIMEOUT = 90  # 90s timeouts as specified
DEFAULT_MAX_OUTPUT_TOKENS = 8192  # 8192 token outputs as specified
DEFAULT_TEMPERATURE = 0.1  # Low temperature for consistent analysis

@dataclass
class GeminiRequest:
    """Represents a Gemini API request with token tracking."""
    content: str
    repository_name: str
    chunk_id: Optional[str] = None
    input_tokens: int = 0
    timestamp: float = 0.0

class GeminiFlash25Client:
    """
    Gemini Flash 2.5 client with proper rate limiting and configuration.
    """
    
    def __init__(self, api_key: str, max_workers: int = 2):
        """
        Initialize Gemini Flash 2.5 client.
        
        Args:
            api_key: Gemini API key
            max_workers: Number of workers (default: 2 as specified)
        """
        self.api_key = api_key
        self.max_workers = max_workers
        
        # Rate limiting state
        self._rate_limit_lock = threading.Lock()
        self._request_times: List[float] = []
        self._token_usage: List[tuple] = []  # (timestamp, tokens)
        
        # API configuration
        self.base_url = GEMINI_BASE_URL
        self.model = GEMINI_20_FLASH_MODEL
        self.timeout = DEFAULT_TIMEOUT
        self.max_output_tokens = DEFAULT_MAX_OUTPUT_TOKENS
        self.temperature = DEFAULT_TEMPERATURE
        
        logger.info(f"🔧 Gemini Flash 2.0 Client initialized")
        logger.info(f"   Model: {self.model}")
        logger.info(f"   Max workers: {max_workers}")
        logger.info(f"   Rate limits: {MAX_RPM} RPM, {MAX_TPM:,} TPM")
        logger.info(f"   Target TPM: {TARGET_TPM:,}")
        logger.info(f"   Timeout: {self.timeout}s")
        logger.info(f"   Max output tokens: {self.max_output_tokens}")
    
    def _check_rate_limits(self, input_tokens: int) -> bool:
        """
        Check if request can be made within rate limits.
        
        Args:
            input_tokens: Number of input tokens for the request
            
        Returns:
            bool: True if request can be made, False if rate limited
        """
        with self._rate_limit_lock:
            current_time = time.time()
            window_start = current_time - 60  # 1 minute window
            
            # Clean old entries
            self._request_times = [t for t in self._request_times if t > window_start]
            self._token_usage = [(t, tokens) for t, tokens in self._token_usage if t > window_start]
            
            # Check RPM limit
            if len(self._request_times) >= MAX_RPM:
                logger.debug(f"RPM limit reached: {len(self._request_times)}/{MAX_RPM}")
                return False
            
            # Check TPM limit
            current_tokens = sum(tokens for _, tokens in self._token_usage)
            if current_tokens + input_tokens > TARGET_TPM:
                logger.debug(f"TPM limit would be exceeded: {current_tokens + input_tokens:,}/{TARGET_TPM:,}")
                return False
            
            return True
    
    def _record_request(self, input_tokens: int):
        """Record a successful request for rate limiting."""
        with self._rate_limit_lock:
            current_time = time.time()
            self._request_times.append(current_time)
            self._token_usage.append((current_time, input_tokens))
    
    def _wait_for_rate_limit(self, input_tokens: int, max_wait_seconds: int = 300) -> bool:
        """
        Wait for rate limits to allow the request.
        
        Args:
            input_tokens: Number of input tokens
            max_wait_seconds: Maximum time to wait
            
        Returns:
            bool: True if rate limit cleared, False if timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_seconds:
            if self._check_rate_limits(input_tokens):
                return True
            
            # Calculate wait time based on rate limits
            with self._rate_limit_lock:
                current_time = time.time()
                window_start = current_time - 60
                
                # Find when oldest request/token usage will expire
                oldest_request = min(self._request_times) if self._request_times else current_time
                oldest_token = min(t for t, _ in self._token_usage) if self._token_usage else current_time
                
                wait_time = min(
                    max(0, oldest_request - window_start + 1),
                    max(0, oldest_token - window_start + 1),
                    5.0  # Max 5 second wait
                )
            
            if wait_time > 0:
                logger.debug(f"Rate limited, waiting {wait_time:.1f}s")
                time.sleep(wait_time)
            else:
                time.sleep(0.1)  # Small delay
        
        return False
    
    def analyze_repository(self, content: str, repository_name: str, 
                          chunk_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze repository content with Gemini Flash 2.0.

        Args:
            content: Repository content to analyze
            repository_name: Name of the repository
            chunk_id: Optional chunk identifier
            
        Returns:
            Dict containing analysis results
        """
        try:
            # Count input tokens
            input_tokens = count_tokens_in_text(content)
            logger.info(f"🔍 Gemini Flash 2.0: Analyzing {repository_name} ({input_tokens:,} tokens)")
            
            if chunk_id:
                logger.info(f"   Chunk: {chunk_id}")
            
            # Wait for rate limit clearance
            if not self._wait_for_rate_limit(input_tokens):
                raise Exception("Rate limit timeout - could not acquire slot")
            
            # Build comprehensive analysis prompt
            prompt = self._build_comprehensive_prompt(repository_name, content)
            
            # Prepare API request
            data = {
                'contents': [{'parts': [{'text': prompt}]}],
                'generationConfig': {
                    'maxOutputTokens': self.max_output_tokens,
                    'temperature': self.temperature,
                    'topP': 0.95,
                    'topK': 40
                }
            }
            
            # Make API request
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/models/{self.model}:generateContent?key={self.api_key}",
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            request_duration = time.time() - start_time
            
            # Record successful request
            self._record_request(input_tokens)
            
            # Handle response
            if response.status_code == 200:
                result = response.json()
                
                # Extract analysis text
                analysis_text = ""
                if 'candidates' in result and result['candidates']:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        analysis_text = candidate['content']['parts'][0].get('text', '')
                
                # Count output tokens
                output_tokens = count_tokens_in_text(analysis_text)
                
                logger.info(f"✅ Gemini Flash 2.5: Analysis complete for {repository_name}")
                logger.info(f"   Duration: {request_duration:.1f}s")
                logger.info(f"   Input tokens: {input_tokens:,}")
                logger.info(f"   Output tokens: {output_tokens:,}")
                
                return {
                    'success': True,
                    'analysis': analysis_text,
                    'repository_name': repository_name,
                    'chunk_id': chunk_id,
                    'input_tokens': input_tokens,
                    'output_tokens': output_tokens,
                    'duration': request_duration,
                    'model': self.model
                }
            
            else:
                error_msg = f"API error {response.status_code}: {response.text}"
                logger.error(f"❌ Gemini Flash 2.5: {error_msg}")
                
                return {
                    'success': False,
                    'error': error_msg,
                    'repository_name': repository_name,
                    'chunk_id': chunk_id,
                    'status_code': response.status_code
                }
        
        except Exception as e:
            logger.error(f"❌ Gemini Flash 2.5: Exception analyzing {repository_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'repository_name': repository_name,
                'chunk_id': chunk_id
            }
    
    def _build_comprehensive_prompt(self, repository_name: str, content: str) -> str:
        """
        Build comprehensive analysis prompt as specified by user.
        
        Args:
            repository_name: Name of the repository
            content: Repository content
            
        Returns:
            Comprehensive analysis prompt
        """
        return f"""Analyze this repository comprehensively and provide an exhaustive listing of all features with underlying functionalities used in each feature.

Repository: {repository_name}

Please provide a detailed analysis covering:

1. **Core Features and Functionalities**
   - List every major feature implemented in the repository
   - For each feature, describe the underlying technologies, libraries, and implementation approaches used
   - Include specific technical details about how each feature works

2. **Architecture and Design Patterns**
   - Overall system architecture and design patterns
   - Database design and data models
   - API design and endpoints
   - Authentication and authorization mechanisms

3. **Technology Stack Analysis**
   - Programming languages and frameworks used
   - External libraries and dependencies
   - Database systems and storage solutions
   - Infrastructure and deployment technologies

4. **Implementation Quality**
   - Code organization and structure
   - Testing strategies and coverage
   - Error handling and logging
   - Performance considerations

5. **Integration and Connectivity**
   - External service integrations
   - API connections and data sources
   - Message queues and communication patterns
   - Third-party service dependencies

6. **Development and Operations**
   - Build and deployment processes
   - Configuration management
   - Monitoring and observability
   - Documentation quality

Provide specific examples from the code where possible. Focus on being comprehensive and exhaustive in your analysis.

Repository Content:
{content}"""
    
    def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current rate limit status."""
        with self._rate_limit_lock:
            current_time = time.time()
            window_start = current_time - 60
            
            # Clean old entries
            recent_requests = [t for t in self._request_times if t > window_start]
            recent_tokens = [(t, tokens) for t, tokens in self._token_usage if t > window_start]
            
            current_rpm = len(recent_requests)
            current_tpm = sum(tokens for _, tokens in recent_tokens)
            
            return {
                'current_rpm': current_rpm,
                'max_rpm': MAX_RPM,
                'current_tpm': current_tpm,
                'target_tpm': TARGET_TPM,
                'max_tpm': MAX_TPM,
                'rpm_utilization': current_rpm / MAX_RPM * 100,
                'tpm_utilization': current_tpm / TARGET_TPM * 100
            }


# Global client instance
_gemini_client = None

def get_gemini_client(api_key: str, max_workers: int = 2) -> GeminiFlash25Client:
    """
    Get global Gemini Flash 2.0 client instance.

    Args:
        api_key: Gemini API key
        max_workers: Number of workers
        
    Returns:
        GeminiFlash25Client instance
    """
    global _gemini_client
    if _gemini_client is None:
        _gemini_client = GeminiFlash25Client(api_key, max_workers)
    return _gemini_client
