#!/usr/bin/env python3
"""
Test suite for distributed rate limiter using Redis.
Tests concurrent access and rate limiting across multiple workers.
"""

import time
import threading
import multiprocessing
import redis
import pytest
from unittest.mock import Mock, patch
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from llm_rate_limiter import LLMRateLimiter


class TestDistributedRateLimiter:
    """Test suite for distributed rate limiter."""
    
    @pytest.fixture
    def redis_client(self):
        """Create a Redis client for testing."""
        try:
            client = redis.Redis(host='localhost', port=6379, decode_responses=True, db=1)
            client.ping()  # Test connection
            
            # Clean up any existing test data
            client.flushdb()
            
            yield client
            
            # Clean up after test
            client.flushdb()
        except redis.ConnectionError:
            pytest.skip("Redis not available for testing")
    
    @pytest.fixture
    def rate_limiter(self, redis_client):
        """Create a rate limiter instance for testing."""
        limiter = LLMRateLimiter(
            redis_client=redis_client,
            rate_limit_mb_per_min=10.0,  # 10MB per minute for testing
            rate_limit_requests_per_min=100,  # 100 requests per minute
            window_size_seconds=60
        )
        yield limiter
        limiter.cleanup()
    
    def test_basic_rate_limiting(self, rate_limiter):
        """Test basic rate limiting functionality."""
        # Should allow requests under the limit
        assert rate_limiter.acquire_slot(1.0) == True
        assert rate_limiter.acquire_slot(2.0) == True
        assert rate_limiter.acquire_slot(3.0) == True
        
        # Should reject requests over the limit
        assert rate_limiter.acquire_slot(5.0) == False  # Would exceed 10MB limit
    
    def test_request_count_limiting(self, rate_limiter):
        """Test request count rate limiting."""
        # Make requests up to the limit
        for i in range(100):
            assert rate_limiter.acquire_slot(0.01) == True  # Small files
        
        # Next request should be rejected
        assert rate_limiter.acquire_slot(0.01) == False
    
    def test_sliding_window_behavior(self, rate_limiter):
        """Test that the sliding window properly expires old entries."""
        # Use a short window for testing
        short_limiter = LLMRateLimiter(
            redis_client=rate_limiter.redis_client,
            rate_limit_mb_per_min=5.0,
            window_size_seconds=2  # 2 second window
        )
        
        # Fill up the limit
        assert short_limiter.acquire_slot(2.0) == True
        assert short_limiter.acquire_slot(2.0) == True
        assert short_limiter.acquire_slot(1.0) == True
        
        # Should be at limit
        assert short_limiter.acquire_slot(0.1) == False
        
        # Wait for window to expire
        time.sleep(2.5)
        
        # Should be able to make requests again
        assert short_limiter.acquire_slot(2.0) == True
    
    def test_concurrent_access_threading(self, rate_limiter):
        """Test concurrent access using threading."""
        results = []
        
        def worker_thread(worker_id):
            """Worker thread that tries to acquire slots."""
            for i in range(10):
                result = rate_limiter.acquire_slot(0.5)  # 0.5MB per request
                results.append((worker_id, i, result))
                time.sleep(0.01)  # Small delay
        
        # Start multiple threads
        threads = []
        for worker_id in range(5):
            thread = threading.Thread(target=worker_thread, args=(worker_id,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Analyze results
        successful_requests = sum(1 for _, _, success in results if success)
        failed_requests = sum(1 for _, _, success in results if not success)
        
        # Should have some successful and some failed requests
        assert successful_requests > 0
        assert failed_requests > 0
        assert successful_requests <= 20  # 10MB limit / 0.5MB per request
        
        print(f"Threading test: {successful_requests} successful, {failed_requests} failed")
    
    def test_concurrent_access_multiprocessing(self, redis_client):
        """Test concurrent access using multiprocessing."""
        def worker_process(worker_id, results_queue):
            """Worker process that tries to acquire slots."""
            # Create rate limiter in each process
            limiter = LLMRateLimiter(
                redis_client=redis_client,
                rate_limit_mb_per_min=8.0,  # 8MB per minute
                rate_limit_requests_per_min=50
            )
            
            successful = 0
            failed = 0
            
            for i in range(10):
                if limiter.acquire_slot(0.4):  # 0.4MB per request
                    successful += 1
                else:
                    failed += 1
                time.sleep(0.01)
            
            results_queue.put((worker_id, successful, failed))
        
        # Start multiple processes
        processes = []
        results_queue = multiprocessing.Queue()
        
        for worker_id in range(4):
            process = multiprocessing.Process(
                target=worker_process, 
                args=(worker_id, results_queue)
            )
            processes.append(process)
            process.start()
        
        # Wait for all processes to complete
        for process in processes:
            process.join()
        
        # Collect results
        total_successful = 0
        total_failed = 0
        
        while not results_queue.empty():
            worker_id, successful, failed = results_queue.get()
            total_successful += successful
            total_failed += failed
            print(f"Worker {worker_id}: {successful} successful, {failed} failed")
        
        # Should have some successful and some failed requests
        assert total_successful > 0
        assert total_failed > 0
        assert total_successful <= 20  # 8MB limit / 0.4MB per request
        
        print(f"Multiprocessing test: {total_successful} successful, {total_failed} failed")
    
    def test_usage_statistics(self, rate_limiter):
        """Test usage statistics functionality."""
        # Make some requests
        rate_limiter.acquire_slot(2.0)
        rate_limiter.acquire_slot(3.0)
        
        # Get usage stats
        stats = rate_limiter.get_current_usage()
        
        assert "requests" in stats
        assert "mb_used" in stats
        assert "requests_remaining" in stats
        assert "mb_remaining" in stats
        
        assert stats["requests"] == 2
        assert stats["mb_used"] == 5.0
        assert stats["mb_remaining"] == 5.0  # 10MB limit - 5MB used
    
    def test_fallback_mode(self):
        """Test fallback mode when Redis is not available."""
        # Create rate limiter without Redis client
        fallback_limiter = LLMRateLimiter(
            redis_client=None,
            rate_limit_mb_per_min=5.0
        )
        
        # Should work in fallback mode
        assert fallback_limiter.acquire_slot(2.0) == True
        assert fallback_limiter.acquire_slot(2.0) == True
        assert fallback_limiter.acquire_slot(2.0) == False  # Over limit
        
        # Test usage stats in fallback mode
        stats = fallback_limiter.get_current_usage()
        assert "mode" in stats
        assert stats["mode"] == "fallback"
        
        fallback_limiter.cleanup()
    
    def test_wait_for_slot(self, rate_limiter):
        """Test wait_for_slot functionality."""
        # Fill up the rate limit
        for i in range(20):  # 20 * 0.5MB = 10MB
            rate_limiter.acquire_slot(0.5)
        
        # Should timeout waiting for slot
        start_time = time.time()
        with pytest.raises(TimeoutError):
            rate_limiter.wait_for_slot(1.0, max_wait_seconds=2)
        
        elapsed = time.time() - start_time
        assert elapsed >= 2.0  # Should have waited at least 2 seconds
        assert elapsed < 3.0   # But not much longer
    
    def test_reset_limits(self, rate_limiter):
        """Test reset functionality."""
        # Make some requests
        rate_limiter.acquire_slot(5.0)
        
        # Should be at limit
        assert rate_limiter.acquire_slot(6.0) == False
        
        # Reset limits
        rate_limiter.reset_limits()
        
        # Should be able to make requests again
        assert rate_limiter.acquire_slot(5.0) == True


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
