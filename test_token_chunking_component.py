#!/usr/bin/env python3
"""
Test ONLY the Token-Based Chunking component to ensure it works correctly.
This tests the new 850K token chunking system using tiktoken.
"""

import sys
import os
import time
import tempfile
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_tiktoken_availability():
    """Test if tiktoken is available and working."""
    print("🧪 TESTING TIKTOKEN AVAILABILITY")
    print("=" * 50)
    
    try:
        import tiktoken
        
        # Test basic encoding
        encoding = tiktoken.get_encoding("cl100k_base")
        test_text = "Hello, world! This is a test."
        tokens = encoding.encode(test_text)
        
        print(f"✅ tiktoken imported successfully")
        print(f"✅ Encoding loaded: cl100k_base")
        print(f"✅ Test encoding: '{test_text}' = {len(tokens)} tokens")
        
        return True
        
    except ImportError:
        print("❌ tiktoken not available - install with: pip install tiktoken")
        return False
    except Exception as e:
        print(f"❌ tiktoken error: {e}")
        return False

def test_token_chunker_initialization():
    """Test TokenChunker initialization."""
    print("\n🧪 TESTING TOKEN CHUNKER INITIALIZATION")
    print("=" * 50)
    
    try:
        from token_chunking import TokenChunker, get_token_chunker
        
        # Test direct initialization
        chunker = TokenChunker(max_tokens_per_chunk=850000, max_chunks=3)
        print(f"✅ TokenChunker initialized directly")
        print(f"   Max tokens per chunk: {chunker.max_tokens_per_chunk:,}")
        print(f"   Max chunks: {chunker.max_chunks}")
        print(f"   Tiktoken encoding: {'Available' if chunker.encoding else 'Fallback estimation'}")
        
        # Test global instance
        global_chunker = get_token_chunker()
        print(f"✅ Global TokenChunker retrieved")
        
        return chunker
        
    except Exception as e:
        print(f"❌ TokenChunker initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_token_counting():
    """Test token counting accuracy."""
    print("\n🧪 TESTING TOKEN COUNTING")
    print("=" * 50)
    
    try:
        from token_chunking import TokenChunker, count_tokens_in_text
        
        chunker = TokenChunker()
        
        # Test various text samples
        test_cases = [
            ("Hello world", "Simple text"),
            ("def hello():\n    print('Hello, world!')\n    return True", "Python code"),
            ("# Repository Analysis\n\nThis is a **markdown** file with `code` snippets.", "Markdown text"),
            ("A" * 1000, "Repeated characters (1000 A's)"),
            ("Word " * 1000, "Repeated words (1000 'Word ')"),
        ]
        
        for text, description in test_cases:
            token_count = chunker.count_tokens(text)
            char_count = len(text)
            word_count = len(text.split())
            
            print(f"📊 {description}:")
            print(f"   Characters: {char_count}")
            print(f"   Words: {word_count}")
            print(f"   Tokens: {token_count}")
            print(f"   Tokens/Word ratio: {token_count/word_count:.2f}" if word_count > 0 else "   No words")
            print()
        
        # Test the convenience function
        convenience_tokens = count_tokens_in_text("Test convenience function")
        print(f"✅ Convenience function: {convenience_tokens} tokens")
        
        return True
        
    except Exception as e:
        print(f"❌ Token counting test failed: {e}")
        return False

def test_chunking_logic():
    """Test the token-based chunking logic."""
    print("\n🧪 TESTING TOKEN CHUNKING LOGIC")
    print("=" * 50)
    
    try:
        from token_chunking import TokenChunker
        
        # Create chunker with smaller limits for testing
        chunker = TokenChunker(max_tokens_per_chunk=100, max_chunks=3)
        
        # Create test content that will definitely need chunking
        test_content = """
# Repository Analysis

This is a comprehensive analysis of a repository.

## Features

1. **Authentication System**
   - User login and registration
   - JWT token management
   - Password reset functionality
   - OAuth integration with Google and GitHub

2. **Data Processing Pipeline**
   - File upload and validation
   - Data transformation and cleaning
   - Batch processing capabilities
   - Real-time streaming support

3. **API Endpoints**
   - RESTful API design
   - GraphQL support
   - Rate limiting and throttling
   - Comprehensive error handling

4. **Database Integration**
   - PostgreSQL primary database
   - Redis for caching
   - MongoDB for document storage
   - Database migration system

5. **Monitoring and Logging**
   - Application performance monitoring
   - Structured logging with ELK stack
   - Health check endpoints
   - Metrics collection and alerting

## Implementation Details

The system uses a microservices architecture with Docker containers.
Each service is independently deployable and scalable.
Communication between services uses message queues and HTTP APIs.

## Testing Strategy

- Unit tests with pytest
- Integration tests with test containers
- End-to-end tests with Selenium
- Performance tests with Locust
- Security tests with OWASP ZAP

This content should be chunked into multiple parts based on token limits.
"""
        
        # Test token counting
        total_tokens = chunker.count_tokens(test_content)
        print(f"📄 Test content: {total_tokens} tokens")
        
        # Test chunking
        chunks = chunker.chunk_content_by_tokens(test_content)
        print(f"🔄 Chunking result: {len(chunks)} chunks created")
        
        # Verify each chunk
        total_chunk_tokens = 0
        for i, chunk in enumerate(chunks):
            chunk_tokens = chunker.count_tokens(chunk)
            total_chunk_tokens += chunk_tokens
            print(f"   Chunk {i+1}: {chunk_tokens} tokens")
            
            if chunk_tokens > chunker.max_tokens_per_chunk:
                print(f"❌ Chunk {i+1} exceeds token limit!")
                return False
        
        print(f"📊 Total tokens in chunks: {total_chunk_tokens}")
        print(f"📊 Token preservation: {total_chunk_tokens/total_tokens*100:.1f}%")
        
        # Verify max chunks limit
        if len(chunks) <= chunker.max_chunks:
            print(f"✅ Respects max chunks limit ({len(chunks)} ≤ {chunker.max_chunks})")
        else:
            print(f"❌ Exceeds max chunks limit ({len(chunks)} > {chunker.max_chunks})")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Token chunking logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_chunking():
    """Test file-based token chunking."""
    print("\n🧪 TESTING FILE TOKEN CHUNKING")
    print("=" * 50)
    
    try:
        from token_chunking import chunk_and_retain_file_by_tokens, count_tokens_in_file
        
        # Create test file with content that needs chunking
        large_content = """
# Large Repository Analysis

This file contains a lot of content that will exceed the token limit.

""" + "\n".join([f"This is line number {i} with some additional content to increase token count." for i in range(1000)])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(large_content)
            test_file_path = f.name
        
        try:
            # Count tokens in original file
            original_tokens = count_tokens_in_file(test_file_path)
            print(f"📄 Original file: {original_tokens:,} tokens")
            
            # Test chunking with 850K token limit
            chunk_paths = chunk_and_retain_file_by_tokens(
                test_file_path, 
                max_tokens_per_chunk=850000, 
                max_chunks=3
            )
            
            print(f"🔄 Chunking result: {len(chunk_paths)} chunks")
            
            # Verify chunks
            total_chunk_tokens = 0
            for i, chunk_path in enumerate(chunk_paths):
                if os.path.exists(chunk_path):
                    chunk_tokens = count_tokens_in_file(chunk_path)
                    total_chunk_tokens += chunk_tokens
                    print(f"   Chunk {i+1}: {chunk_tokens:,} tokens -> {os.path.basename(chunk_path)}")
                    
                    # Clean up chunk file
                    os.unlink(chunk_path)
                else:
                    print(f"❌ Chunk file not found: {chunk_path}")
                    return False
            
            print(f"📊 Total chunk tokens: {total_chunk_tokens:,}")
            
            # Verify original file was deleted (if chunked)
            if len(chunk_paths) > 1:
                if not os.path.exists(test_file_path):
                    print("✅ Original file deleted after chunking")
                else:
                    print("❌ Original file should be deleted after chunking")
                    os.unlink(test_file_path)  # Clean up
                    return False
            
            return True
            
        finally:
            # Clean up test file if it still exists
            if os.path.exists(test_file_path):
                os.unlink(test_file_path)
        
    except Exception as e:
        print(f"❌ File token chunking test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test token-based chunking component."""
    print("🔧 TESTING TOKEN-BASED CHUNKING COMPONENT")
    print("=" * 60)
    print("TESTING:")
    print("1. tiktoken availability and functionality")
    print("2. TokenChunker initialization")
    print("3. Token counting accuracy")
    print("4. Token-based chunking logic")
    print("5. File-based token chunking")
    print()
    
    # Test tiktoken availability
    tiktoken_available = test_tiktoken_availability()
    if not tiktoken_available:
        print("\n⚠️ tiktoken not available - chunking will use estimation")
        print("   Install tiktoken for accurate token counting: pip install tiktoken")
    
    # Test TokenChunker initialization
    chunker = test_token_chunker_initialization()
    if not chunker:
        print("\n❌ TOKEN CHUNKING TESTS FAILED!")
        print("❌ TokenChunker initialization failed")
        return False
    
    # Test token counting
    counting_success = test_token_counting()
    if not counting_success:
        print("\n❌ TOKEN CHUNKING TESTS FAILED!")
        print("❌ Token counting failed")
        return False
    
    # Test chunking logic
    logic_success = test_chunking_logic()
    if not logic_success:
        print("\n❌ TOKEN CHUNKING TESTS FAILED!")
        print("❌ Token chunking logic failed")
        return False
    
    # Test file chunking
    file_success = test_file_chunking()
    if not file_success:
        print("\n❌ TOKEN CHUNKING TESTS FAILED!")
        print("❌ File token chunking failed")
        return False
    
    # Final assessment
    print("\n🎉 ALL TOKEN CHUNKING TESTS PASSED!")
    print("✅ Component is working correctly")
    print("✅ tiktoken integration working")
    print("✅ Token counting accurate")
    print("✅ Chunking logic respects 850K token limit")
    print("✅ File chunking working")
    print("✅ Maximum 3 chunks per repository enforced")
    print()
    print("🚀 READY FOR INTEGRATION:")
    print("   - Replace file-based chunking with token-based chunking")
    print("   - 850K token chunks for optimal Gemini API utilization")
    print("   - Stay under 1.8M TPM limit")
    print("   - Accurate token counting with tiktoken")
    print("   - Maximum 3 chunks per repository")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
