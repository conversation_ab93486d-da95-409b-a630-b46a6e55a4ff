#!/usr/bin/env python3
"""
Complete cloud infrastructure setup for Repository Research Tool.
Sets up Redis, VPC, secrets, and all required Google Cloud resources.
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

def setup_redis_instance(project_id, region):
    """Set up Google Cloud Memorystore Redis instance."""
    print("\n🔴 Setting up Redis instance...")
    
    redis_instance_id = "repo-research-redis"
    
    try:
        # Check if instance already exists
        result = subprocess.run([
            "gcloud", "redis", "instances", "describe", redis_instance_id,
            "--region", region,
            "--project", project_id
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Redis instance {redis_instance_id} already exists")
            
            # Get instance details
            instance_info = json.loads(result.stdout)
            redis_ip = instance_info.get("host", "")
            print(f"   Redis IP: {redis_ip}")
            return redis_ip
        
        # Create new Redis instance
        print(f"   Creating Redis instance: {redis_instance_id}")
        create_result = subprocess.run([
            "gcloud", "redis", "instances", "create", redis_instance_id,
            "--size", "1",
            "--region", region,
            "--redis-version", "redis_7_0",
            "--tier", "basic",
            "--project", project_id
        ], capture_output=True, text=True)
        
        if create_result.returncode == 0:
            print("✅ Redis instance created successfully")
            
            # Wait for instance to be ready
            print("   Waiting for Redis instance to be ready...")
            for i in range(30):  # Wait up to 5 minutes
                time.sleep(10)
                
                status_result = subprocess.run([
                    "gcloud", "redis", "instances", "describe", redis_instance_id,
                    "--region", region,
                    "--format", "value(state)",
                    "--project", project_id
                ], capture_output=True, text=True)
                
                if status_result.returncode == 0 and status_result.stdout.strip() == "READY":
                    # Get Redis IP
                    ip_result = subprocess.run([
                        "gcloud", "redis", "instances", "describe", redis_instance_id,
                        "--region", region,
                        "--format", "value(host)",
                        "--project", project_id
                    ], capture_output=True, text=True)
                    
                    if ip_result.returncode == 0:
                        redis_ip = ip_result.stdout.strip()
                        print(f"✅ Redis instance ready at: {redis_ip}")
                        return redis_ip
                
                print(f"   Still waiting... ({i+1}/30)")
            
            print("❌ Redis instance creation timed out")
            return None
        else:
            print(f"❌ Failed to create Redis instance: {create_result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Error setting up Redis: {e}")
        return None

def setup_vpc_connector(project_id, region):
    """Set up VPC connector for Cloud Run to access Redis."""
    print("\n🌐 Setting up VPC connector...")
    
    connector_name = "redis-connector"
    
    try:
        # Check if connector already exists
        result = subprocess.run([
            "gcloud", "compute", "networks", "vpc-access", "connectors", "describe", connector_name,
            "--region", region,
            "--project", project_id
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ VPC connector {connector_name} already exists")
            return True
        
        # Create VPC connector
        print(f"   Creating VPC connector: {connector_name}")
        create_result = subprocess.run([
            "gcloud", "compute", "networks", "vpc-access", "connectors", "create", connector_name,
            "--region", region,
            "--subnet-project", project_id,
            "--subnet", "default",
            "--range", "10.8.0.0/28",
            "--project", project_id
        ], capture_output=True, text=True)
        
        if create_result.returncode == 0:
            print("✅ VPC connector created successfully")
            return True
        else:
            print(f"❌ Failed to create VPC connector: {create_result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up VPC connector: {e}")
        return False

def setup_service_account(project_id):
    """Set up service account for Cloud Run."""
    print("\n👤 Setting up service account...")
    
    sa_name = "repository-research-sa"
    sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
    
    try:
        # Check if service account exists
        result = subprocess.run([
            "gcloud", "iam", "service-accounts", "describe", sa_email,
            "--project", project_id
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Service account {sa_email} already exists")
        else:
            # Create service account
            create_result = subprocess.run([
                "gcloud", "iam", "service-accounts", "create", sa_name,
                "--display-name", "Repository Research Tool Service Account",
                "--project", project_id
            ], capture_output=True, text=True)
            
            if create_result.returncode == 0:
                print(f"✅ Service account created: {sa_email}")
            else:
                print(f"❌ Failed to create service account: {create_result.stderr}")
                return False
        
        # Grant necessary roles
        roles = [
            "roles/cloudsql.client",
            "roles/secretmanager.secretAccessor",
            "roles/storage.objectAdmin"
        ]
        
        for role in roles:
            role_result = subprocess.run([
                "gcloud", "projects", "add-iam-policy-binding", project_id,
                "--member", f"serviceAccount:{sa_email}",
                "--role", role
            ], capture_output=True, text=True)
            
            if role_result.returncode == 0:
                print(f"✅ Granted role: {role}")
            else:
                print(f"❌ Failed to grant role {role}: {role_result.stderr}")
        
        return sa_email
        
    except Exception as e:
        print(f"❌ Error setting up service account: {e}")
        return None

def create_deployment_config(project_id, region, redis_ip, sa_email):
    """Create deployment configuration file."""
    print("\n📝 Creating deployment configuration...")
    
    config = {
        "project_id": project_id,
        "region": region,
        "redis_ip": redis_ip,
        "service_account": sa_email,
        "service_name": "repository-research-tool",
        "image": f"gcr.io/{project_id}/repository-research-tool:latest",
        "environment": {
            "DEPLOYMENT_MODE": "cloud",
            "REDIS_URL": f"redis://{redis_ip}:6379",
            "REPOMIX_WORKERS": "15",
            "LLM_WORKERS": "4",
            "LLM_TIMEOUT": "90",
            "LLM_MAX_OUTPUT_TOKENS": "8192"
        },
        "secrets": {
            "SUPABASE_URL": "supabase-config:url",
            "SUPABASE_KEY": "supabase-config:service-key", 
            "SUPABASE_ANON_KEY": "supabase-config:anon-key",
            "LLM_API_KEY": "api-keys:gemini-api-key",
            "GITHUB_TOKEN": "api-keys:github-token"
        },
        "resources": {
            "cpu": "2",
            "memory": "4Gi",
            "timeout": "3600s",
            "max_instances": "10",
            "min_instances": "1",
            "concurrency": "10"
        }
    }
    
    config_file = Path("deploy/deployment-config.json")
    config_file.parent.mkdir(exist_ok=True)
    config_file.write_text(json.dumps(config, indent=2))
    
    print(f"✅ Created deployment config: {config_file}")
    return config

def update_cloud_run_yaml(project_id, region, redis_ip):
    """Update Cloud Run service YAML with actual values."""
    print("\n📄 Updating Cloud Run service YAML...")
    
    yaml_file = Path("deploy/cloud-run-service.yaml")
    if not yaml_file.exists():
        print(f"❌ Cloud Run YAML not found: {yaml_file}")
        return False
    
    # Read and update YAML content
    yaml_content = yaml_file.read_text()
    
    # Replace placeholders
    yaml_content = yaml_content.replace("PROJECT_ID", project_id)
    yaml_content = yaml_content.replace("REGION", region)
    yaml_content = yaml_content.replace("REDIS_PRIVATE_IP", redis_ip)
    
    # Write updated YAML
    updated_yaml_file = Path("deploy/cloud-run-service-configured.yaml")
    updated_yaml_file.write_text(yaml_content)
    
    print(f"✅ Updated Cloud Run YAML: {updated_yaml_file}")
    return True

def main():
    """Main infrastructure setup function."""
    print("🏗️ Complete Cloud Infrastructure Setup")
    print("=" * 60)
    
    # Get project configuration
    try:
        result = subprocess.run([
            "gcloud", "config", "get-value", "project"
        ], capture_output=True, text=True)
        
        if result.returncode != 0 or not result.stdout.strip():
            print("❌ No Google Cloud project configured")
            print("   Run: gcloud config set project YOUR_PROJECT_ID")
            return 1
        
        project_id = result.stdout.strip()
        print(f"📋 Project ID: {project_id}")
        
        # Get region
        region_result = subprocess.run([
            "gcloud", "config", "get-value", "compute/region"
        ], capture_output=True, text=True)
        
        if region_result.returncode == 0 and region_result.stdout.strip():
            region = region_result.stdout.strip()
        else:
            region = "us-central1"
            print(f"⚠️ Using default region: {region}")
        
        print(f"📍 Region: {region}")
        
    except Exception as e:
        print(f"❌ Error getting project config: {e}")
        return 1
    
    # Set up infrastructure components
    print("\n🏗️ Setting up infrastructure components...")
    
    # 1. Set up Redis instance
    redis_ip = setup_redis_instance(project_id, region)
    if not redis_ip:
        print("❌ Failed to set up Redis instance")
        return 1
    
    # 2. Set up VPC connector
    if not setup_vpc_connector(project_id, region):
        print("❌ Failed to set up VPC connector")
        return 1
    
    # 3. Set up service account
    sa_email = setup_service_account(project_id)
    if not sa_email:
        print("❌ Failed to set up service account")
        return 1
    
    # 4. Create deployment configuration
    config = create_deployment_config(project_id, region, redis_ip, sa_email)
    
    # 5. Update Cloud Run YAML
    if not update_cloud_run_yaml(project_id, region, redis_ip):
        print("⚠️ Failed to update Cloud Run YAML")
    
    print("\n🎉 Infrastructure setup complete!")
    print("=" * 60)
    print("✅ Redis instance created")
    print("✅ VPC connector configured")
    print("✅ Service account set up")
    print("✅ Deployment configuration created")
    
    print(f"\n📋 Infrastructure Details:")
    print(f"   Redis IP: {redis_ip}")
    print(f"   Service Account: {sa_email}")
    print(f"   VPC Connector: redis-connector")
    
    print(f"\n🚀 Next Steps:")
    print("1. Configure secrets: python deploy_to_cloud_run.py")
    print("2. Build and deploy: python deploy_to_cloud_run.py")
    print("3. Test deployment: python test_cloud_deployment.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
