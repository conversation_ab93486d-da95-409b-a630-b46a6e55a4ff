# Container Environment Configuration
# Copy this to .env and fill in your values

# GitHub Configuration
GITHUB_TOKEN=your_github_token_here

# LLM Provider Configuration
# Options: gemini, openai, anthropic, custom
LLM_PROVIDER=gemini
LLM_MODEL=gemini-2.0-flash
LLM_BASE_URL=
LLM_API_KEY=your_api_key_here

# Alternative API Keys (use based on LLM_PROVIDER)
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
CUSTOM_API_KEY=your_custom_api_key_here

# Sentry Configuration (for debugging container)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=1.0.0
SENTRY_ORG_SLUG=your_org_slug
SENTRY_PROJECT_SLUG=your_project_slug

# Scraper Configuration
KEYWORDS=rag agent,code graph
STARS_FILTER=>100
MAX_REPOS_PER_KEYWORD=40
REPOMIX_WORKERS=30
GEMINI_WORKERS=2
VERBOSE=true

# Container-specific settings
PYTHONUNBUFFERED=1
PYTHONPATH=/app
