apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: repository-research-api
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration for API service
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource allocation - lightweight for API
        run.googleapis.com/cpu: "1"
        run.googleapis.com/memory: "1Gi"
        
        # Timeout configuration
        run.googleapis.com/timeout: "300s"
        
        # VPC configuration for Redis access
        run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/redis-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only
        
        # Execution environment
        run.googleapis.com/execution-environment: gen2
        
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      serviceAccountName: repository-research-sa@PROJECT_ID.iam.gserviceaccount.com
      
      containers:
      - name: repository-research-api
        image: gcr.io/PROJECT_ID/repository-research-tool:latest
        
        # Run API service
        args: ["api"]
        
        ports:
        - name: http1
          containerPort: 8080
          
        env:
        # Deployment configuration
        - name: DEPLOYMENT_MODE
          value: "cloud"
        - name: PORT
          value: "8080"
          
        # Redis configuration
        - name: REDIS_URL
          value: "redis://REDIS_PRIVATE_IP:6379"
          
        # Supabase configuration
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: url
        - name: SUPABASE_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: service-key
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: anon-key
              
        # API keys
        - name: LLM_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: gemini-api-key
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: github-token
              
        # API service doesn't need worker configuration
        - name: REPOMIX_WORKERS
          value: "0"
        - name: LLM_WORKERS
          value: "0"
          
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
            
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30

  traffic:
  - percent: 100
    latestRevision: true
